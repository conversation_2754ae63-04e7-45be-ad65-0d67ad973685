/* 移动端样式 */
@media (max-width: 768px) {
  .nav-container {
    display: none !important;
  }
  
  .main-content {
    margin-top: 0 !important;
    padding-top: 0 !important;
    padding-bottom: 3.5rem !important; /* 为底部导航栏留出空间，但比原来更接近 */
  }
}

/* 桌面端样式 */
@media (min-width: 769px) {
  .nav-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    display: block;
    z-index: 50;
  }
  
  .main-content {
    margin-top: 60px;
  }
} 