/* Masonry grid styles */
.my-masonry-grid {
	display: flex;
	margin-left: -8px; /* 增加间距 */
	width: auto;
	max-width: 100%; /* 确保网格不会超出父容器 */
	box-sizing: border-box;
	min-height: 200px; /* 确保有最小高度 */
}

.my-masonry-grid_column {
	padding-left: 8px; /* 增加间距 */
	background-clip: padding-box;
	width: 100% !important; /* 确保列不会超出容器 */
	box-sizing: border-box;
}

/* 调整卡片样式 */
.my-masonry-grid_column > div {
	margin-bottom: 12px; /* 增加垂直间距 */
	width: 100%; /* 确保卡片宽度不超过列宽 */
	box-sizing: border-box; /* 确保padding和border被包含在宽度内 */
	position: relative; /* 确保定位正确 */
	overflow: hidden;
	opacity: 1;
	transition: opacity 0.5s ease-in-out;
}

/* Card内图片容器的处理 */
.my-masonry-grid_column img {
	transition: opacity 0.3s ease-in-out;
	height: auto; /* 让图片保持比例 */
}

/* 响应式调整 */
@media (max-width: 1200px) {
	.my-masonry-grid {
		margin-left: -6px; /* 小屏幕时调整间距 */
	}
	
	.my-masonry-grid_column {
		padding-left: 6px; /* 小屏幕时调整间距 */
	}
	.my-masonry-grid_column > div {
		margin-bottom: 10px;
	}
}

@media (max-width: 768px) {
	.my-masonry-grid {
		margin-left: -4px; /* 移动端调整间距 */
		max-width: calc(100% + 4px); /* 防止水平溢出 */
	}
	
	.my-masonry-grid_column {
		padding-left: 4px; /* 移动端调整间距 */
	}
	.my-masonry-grid_column > div {
		margin-bottom: 8px;
	}
}

/* 小屏幕额外优化 */
@media (max-width: 500px) {
	.my-masonry-grid_column {
		width: 100% !important; /* 强制单列 */
	}
	
	.my-masonry-grid {
		margin-left: -3px; /* 调整负边距 */
		max-width: 100%; /* 确保不会溢出 */
	}
	
	.my-masonry-grid_column {
		padding-left: 3px; /* 左侧内边距 */
		padding-right: 3px; /* 确保右侧也有内边距 */
		margin: 0 auto; /* 确保居中 */
	}
	
	.my-masonry-grid_column > div {
		margin-left: auto; /* 居中显示 */
		margin-right: auto; /* 居中显示 */
		width: 100%; /* 确保宽度正确 */
		margin-bottom: 8px;
	}
}

/* 超窄屏幕优化 */
@media (max-width: 400px) {
	.my-masonry-grid {
		margin: 0 -3px; /* 适当的水平间距 */
		padding: 0; /* 移除所有内边距 */
		width: 100%; /* 占满可用宽度 */
	}
	
	.my-masonry-grid_column {
		display: flex;
		flex-direction: column;
		align-items: center; /* 水平居中列内容 */
		width: 100% !important;
		padding-left: 3px;
		padding-right: 3px;
	}
	
	.my-masonry-grid_column > div {
		margin-bottom: 8px;
	}
}

/* 图片加载中的状态 */
.masonry-loading .my-masonry-grid_column > div {
	opacity: 0.9;
	transition: opacity 0.5s ease-in-out;
}

/* 超窄屏幕特殊类 */
.extra-narrow-screen {
	margin: 0 -3px !important; /* 增加间距 */
	width: calc(100% + 6px) !important; /* 调整宽度计算 */
	max-width: calc(100% + 6px) !important;
	padding: 0 !important;
	display: flex;
	justify-content: center;
}

.extra-narrow-screen .my-masonry-grid_column {
	max-width: 100% !important;
	width: 100% !important;
	padding: 0 3px !important; /* 左右添加间距 */
	margin: 0 auto !important;
}

.extra-narrow-screen .my-masonry-grid_column > div {
	width: 100% !important;
	max-width: 100% !important;
	margin-left: auto !important;
	margin-right: auto !important;
	margin-bottom: 8px !important;
	box-sizing: border-box !important;
}
