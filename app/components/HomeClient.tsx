"use client";

import Link from "next/link";
import { useEffect, useState } from "react";

export default function HomeClient() {
  const [girlsCount, setGirlsCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function fetchGirlsCount() {
      try {
        const response = await fetch('/api/girls-count');
        const data = await response.json();
        if (response.ok) {
          setGirlsCount(data.count);
        }
      } catch (error) {
        console.error('Failed to fetch girls count:', error);
        // 如果获取失败，使用默认值
        setGirlsCount(10000);
      } finally {
        setIsLoading(false);
      }
    }

    fetchGirlsCount();
  }, []);

  return (
    <main>
      <div className="flex flex-col bg-background dark:bg-background">
        {/* Hero Section */}
        <div className="relative min-h-screen bg-background pt-16 px-4 overflow-hidden">
          {/* Animated Background Gradient */}
          <div className="absolute inset-0 w-full h-full bg-gradient-to-b from-background to-background">
            <div className="absolute inset-0 w-full h-full bg-[radial-gradient(ellipse_at_top_right,rgba(229,0,73,0.15),rgba(229,0,73,0)_50%)]"></div>
            <div className="absolute inset-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(229,0,73,0.1),rgba(229,0,73,0)_50%)]"></div>
          </div>

          {/* Soft Pattern Background */}
          <div className="absolute inset-0 w-full h-full opacity-30">
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(229,0,73,0.05)_1px,transparent_1px)] bg-[size:32px_32px]"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(229,0,73,0.05)_1px,transparent_1px)] bg-[size:24px_24px] rotate-15"></div>
          </div>

          <div className="relative max-w-6xl mx-auto">
            {/* Floating Elements */}
            <div className="absolute -top-20 -left-20 w-60 h-60 bg-[#e50049]/5 rounded-full filter blur-3xl animate-pulse"></div>
            <div className="absolute -top-20 -right-20 w-60 h-60 bg-[#e50049]/5 rounded-full filter blur-3xl animate-pulse delay-1000"></div>

            <div className="text-center relative">
              {/* Brand Logo/Icon */}
              <div className="w-24 h-24 mx-auto mb-8 relative">
                <div className="absolute inset-0 bg-gradient-to-r from-[#e50049] to-[#ff1464] rounded-full opacity-20 animate-ping"></div>
                <div className="relative w-full h-full bg-gradient-to-r from-[#e50049] to-[#ff1464] rounded-full flex items-center justify-center">
                  <span className="text-3xl font-bold text-white">JianierClub</span>
                </div>
              </div>

              <h1 className="relative text-[70px] md:text-[120px] font-bold tracking-tight mb-8 bg-clip-text text-transparent bg-gradient-to-r from-[#e50049] to-[#ff1464] animate-gradient group">
                <span className="relative inline-block">
                  <span className="absolute -inset-2 bg-gradient-to-r from-[#e50049]/20 to-[#ff1464]/20 blur-xl group-hover:blur-2xl transition-all duration-500"></span>
                  <span className="relative z-10 inline-block text-white">佳妮俱乐部</span>
                  <span className="absolute -top-8 -right-4 transform text-[3.5rem] md:text-[4rem] text-[#e50049] font-normal tracking-normal animate-pulse z-20">™</span>
                  {/* 装饰元素 */}
                  <span className="absolute -left-4 -top-4 w-8 h-8 bg-gradient-to-r from-[#e50049] to-[#ff1464] rounded-full opacity-30 animate-ping z-0"></span>
                  <span className="absolute -right-4 -bottom-4 w-6 h-6 bg-gradient-to-r from-[#ff1464] to-[#e50049] rounded-full opacity-30 animate-ping z-0" style={{ animationDelay: '500ms' }}></span>
                  {/* 光效装饰 */}
                  <span className="absolute inset-0 bg-gradient-to-r from-[#e50049]/0 via-[#e50049]/30 to-[#e50049]/0 blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-1000 z-0"></span>
                  {/* 交互装饰线 */}
                  <span className="absolute -bottom-2 left-0 w-0 h-0.5 bg-gradient-to-r from-[#e50049] to-[#ff1464] group-hover:w-full transition-all duration-700 z-0"></span>
                </span>
              </h1>

              <p className="text-2xl md:text-3xl lg:text-4xl text-muted-foreground mb-12 max-w-4xl mx-auto leading-relaxed">
                本俱乐部旨在为<span className="text-[#e50049] font-semibold">高素质的客人</span>，提供
                <br className="hidden md:block" />
                <span className="text-[#e50049] font-semibold">高端私密真人线下约会服务</span>，
                <br className="hidden md:block" />
                累计更新<span className="text-[#e50049] font-semibold text-5xl md:text-6xl animate-pulse">
                  {isLoading ? "100,000" : girlsCount.toLocaleString()}
                </span>优质模特资源。
              </p>

              <div className="flex gap-4 justify-center mb-8">
                <Link 
                  href="/guide" 
                  className="group inline-flex items-center px-8 py-4 border border-muted hover:border-[#e50049] text-foreground font-medium rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg"
                >
                  <span>约会指南</span>
                  <svg className="w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </Link>
                <Link 
                  href="/city" 
                  className="group inline-flex items-center px-8 py-4 bg-[#e50049] hover:bg-[#c8003f] text-white font-medium rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-[#e50049]/20"
                >
                  <span>访问资源库</span>
                  <svg className="w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              </div>

              {/* Feature Tags with Animation */}
              <div className="flex flex-wrap justify-center gap-4 mb-8">
                {[
                  "真人认证",
                  "安全私密",
                  "一对一服务",
                  "灵活预约",
                  "专业靠谱",
                  "实时更新"
                ].map((tag, index) => (
                  <div 
                    key={tag}
                    className="group flex items-center gap-2 bg-muted/50 hover:bg-[#e50049]/10 px-4 py-2 rounded-full transition-all duration-300 hover:scale-105"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <span className="w-2 h-2 bg-[#e50049] rounded-full group-hover:animate-ping"></span>
                    <span className="text-sm font-medium group-hover:text-[#e50049]">{tag}</span>
                  </div>
                ))}
              </div>

              {/* Hero Image with Enhanced Effects */}
              <div className="relative w-full max-w-[180%] mx-auto -mb-16 md:-mb-32">
                <div className="absolute -top-8 -right-8 w-[16rem] md:w-[32rem] h-[16rem] md:h-[32rem] bg-[#e50049]/5 rounded-full filter blur-2xl animate-pulse"></div>
                <div className="absolute -bottom-8 -left-8 w-[16rem] md:w-[32rem] h-[16rem] md:h-[32rem] bg-[#e50049]/5 rounded-full filter blur-2xl animate-pulse delay-1000"></div>
                <Link 
                  href="/city"
                  className="block relative z-10 transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl hover:shadow-[#e50049]/20"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-[#e50049]/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 rounded-2xl md:rounded-[2rem]"></div>
                  <img
                    src="/images/zzk.png"
                    alt="佳妮俱乐部服务展示"
                    className="w-full h-auto rounded-2xl md:rounded-[2rem] shadow-xl md:shadow-2xl cursor-pointer"
                  />
                </Link>
              </div>
            </div>
          </div>

          {/* Scroll Indicator */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center animate-bounce">
            <span className="text-sm text-muted-foreground mb-2">向下滚动</span>
            <svg className="w-6 h-6 text-[#e50049]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="relative bg-muted/50 pt-32 pb-16 px-4 overflow-hidden">
        {/* Decorative Elements */}
        <div className="absolute inset-0 w-full h-full">
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,rgba(229,0,73,0.1),rgba(229,0,73,0)_50%)]"></div>
          <div className="absolute inset-0 bg-[conic-gradient(from_0deg_at_50%_50%,rgba(229,0,73,0.05)_0deg,transparent_60deg,rgba(229,0,73,0.05)_120deg,transparent_180deg,rgba(229,0,73,0.05)_240deg,transparent_300deg,rgba(229,0,73,0.05)_360deg)]"></div>
        </div>
        
        {/* Soft Waves Pattern */}
        <div className="absolute inset-0 w-full h-full opacity-20">
          <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMCAwYzUwIDAgNTAgNTAgMTAwIDUwczUwLTUwIDEwMC01MHY1MGMtNTAgMC01MCA1MC0xMDAgNTBzLTUwLTUwLTEwMC01MFYweiIgZmlsbD0icmdiYSgyMjksMCw3MywwLjAyKSIvPjwvc3ZnPg==')] bg-repeat"></div>
        </div>
        
        <div className="relative max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <span className="inline-block text-[#e50049] text-lg font-semibold mb-4 px-4 py-2 bg-[#e50049]/10 rounded-full">我们的优势</span>
            <h2 className="text-[40px] md:text-[60px] font-bold tracking-tight mb-8 bg-clip-text text-transparent bg-gradient-to-r from-[#e50049] to-[#ff1464]">
              佳妮俱乐部优势
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-16">探索我们的独特之处，享受尊贵体验</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: "M9 12l2 2 4-4m6 2a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0z",
                title: "真人认证",
                description: "所有模特均经过严格筛选和真人认证，确保照片真实可靠，让您放心选择。"
              },
              {
                icon: "M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z",
                title: "安全私密",
                description: "严格保护客户隐私，采用安全支付方式，确保您的体验绝对安全私密。"
              },
              {
                icon: "M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0z",
                title: "一对一服务",
                description: "专属客服为您量身定制服务方案，根据您的需求推荐最合适的模特。"
              },
              {
                icon: "M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z",
                title: "灵活预约",
                description: "强大的实时更新资源库，让您随时随地轻松预约。"
              }
            ].map((feature, index) => (
              <div 
                key={feature.title}
                className="group bg-background/80 backdrop-blur-sm rounded-2xl p-8 border border-muted hover:border-[#e50049] transition-all duration-500 hover:scale-105 hover:shadow-xl hover:shadow-[#e50049]/10"
                style={{ animationDelay: `${index * 150}ms` }}
              >
                <div className="relative w-16 h-16 mb-6 mx-auto">
                  <div className="absolute inset-0 bg-[#e50049]/10 rounded-2xl transform group-hover:rotate-6 transition-transform duration-300"></div>
                  <div className="relative w-full h-full bg-[#e50049]/10 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-8 h-8 text-[#e50049]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={feature.icon} />
                    </svg>
                  </div>
                </div>
                <h3 className="text-2xl font-bold mb-4 text-center group-hover:text-[#e50049] transition-colors">{feature.title}</h3>
                <p className="text-muted-foreground text-lg text-center">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Statistics Section */}
      <div className="relative bg-background py-20 px-4 overflow-hidden">
        {/* Decorative Background */}
        <div className="absolute inset-0 w-full h-full">
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(229,0,73,0.1),rgba(229,0,73,0)_70%)]"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_20%,rgba(229,0,73,0.05),transparent_20%)_0_0/60px_60px,radial-gradient(circle_at_80%_80%,rgba(229,0,73,0.05),transparent_20%)_30px_30px/60px_60px]"></div>
        </div>
        
        <div className="relative max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              { 
                number: isLoading ? "..." : girlsCount.toLocaleString() + "+", 
                label: "优质模特", 
                icon: "M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0z" 
              },
              { number: "50+", label: "覆盖城市", icon: "M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" },
              { number: "24/7", label: "全天候服务", icon: "M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" },
              { number: "98%", label: "满意度", icon: "M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905 0 .905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5" }
            ].map((stat, index) => (
              <div 
                key={stat.label}
                className="group bg-background rounded-2xl p-8 border border-muted hover:border-[#e50049] transition-all duration-500 hover:scale-105 hover:shadow-xl hover:shadow-[#e50049]/10"
                style={{ animationDelay: `${index * 150}ms` }}
              >
                <div className="relative w-16 h-16 mx-auto mb-6">
                  <div className="absolute inset-0 bg-[#e50049]/10 rounded-full transform group-hover:scale-110 transition-transform duration-300"></div>
                  <div className="relative w-full h-full flex items-center justify-center">
                    <svg className="w-8 h-8 text-[#e50049]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={stat.icon} />
                    </svg>
                  </div>
                </div>
                <div className="text-4xl font-bold mb-2 text-center bg-clip-text text-transparent bg-gradient-to-r from-[#e50049] to-[#ff1464] group-hover:scale-110 transition-transform duration-300">
                  {stat.number}
                </div>
                <div className="text-muted-foreground text-lg text-center group-hover:text-[#e50049] transition-colors">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Testimonials Section */}
      <div className="relative bg-muted/50 pt-32 pb-16 px-4 overflow-hidden">
        {/* Decorative Elements */}
        <div className="absolute inset-0 w-full h-full">
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,rgba(229,0,73,0.1),rgba(229,0,73,0)_50%)]"></div>
          <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMzAiIGN5PSIzMCIgcj0iMiIgZmlsbD0icmdiYSgyMjksMCw3MywwLjA1KSIvPjwvc3ZnPg==')] opacity-30"></div>
        </div>
        
        <div className="relative max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <span className="inline-block text-[#e50049] text-lg font-semibold mb-4 px-4 py-2 bg-[#e50049]/10 rounded-full">客户反馈</span>
            <h2 className="text-[40px] md:text-[60px] font-bold tracking-tight mb-8 bg-clip-text text-transparent bg-gradient-to-r from-[#e50049] to-[#ff1464]">
              会员评价
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-16">听听我们的会员怎么说</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: "红豆**思",
                date: "2024年1月",
                content: "你们是我少有见过真人比资料还好看的"
              },
              {
                name: "云**琅",
                date: "2023年12月",
                content: "资源库很牛逼，实时更新，直接选了立刻就能约"
              },
              {
                name: "Me**ius Sun",
                date: "2023年11月",
                content: "你们非常注意细节，很满意，以后我一直是你们的老客户"
              }
            ].map((review, index) => (
              <div 
                key={review.name}
                className="group bg-background/80 backdrop-blur-sm rounded-2xl p-8 border border-muted hover:border-[#e50049] transition-all duration-500 hover:scale-105 hover:shadow-xl hover:shadow-[#e50049]/10"
                style={{ animationDelay: `${index * 150}ms` }}
              >
                <div className="flex items-center mb-8">
                  <div className="relative w-16 h-16">
                    <div className="absolute inset-0 bg-[#e50049]/10 rounded-2xl transform group-hover:rotate-6 transition-transform duration-300"></div>
                    <div className="relative w-full h-full bg-[#e50049]/10 rounded-2xl flex items-center justify-center">
                      <svg className="w-8 h-8 text-[#e50049]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h4 className="text-xl font-bold group-hover:text-[#e50049] transition-colors">{review.name}</h4>
                    <p className="text-muted-foreground">{review.date}</p>
                  </div>
                </div>
                <p className="text-lg text-muted-foreground mb-6 italic">&quot;{review.content}&quot;</p>
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <svg 
                      key={i} 
                      className="w-6 h-6 text-[#e50049] transform group-hover:scale-110 transition-transform duration-300" 
                      style={{ transitionDelay: `${i * 50}ms` }}
                      fill="currentColor" 
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Contact Section */}
      <div className="relative bg-[#c8003f] text-white pt-32 pb-16 px-4 overflow-hidden">
        {/* Decorative Elements */}
        <div className="absolute inset-0 w-full h-full">
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_top_right,rgba(255,255,255,0.15),rgba(255,255,255,0)_50%)]"></div>
          <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMCAwYzUwIDAgNTAgNTAgMTAwIDUwczUwLTUwIDEwMC01MHY1MGMtNTAgMC01MCA1MC0xMDAgNTBzLTUwLTUwLTEwMC01MFYweiIgZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjA1KSIvPjwvc3ZnPg==')] opacity-20"></div>
        </div>

        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-[40px] md:text-[60px] font-bold tracking-tight mb-8">联系我们</h2>
            <p className="text-xl text-white/80 max-w-3xl mx-auto mb-16">随时为您提供专业的咨询服务</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:border-white/40 transition-all hover:scale-105">
              <div className="flex items-center gap-6">
                <div className="w-16 h-16 bg-white/10 rounded-2xl flex items-center justify-center">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <div className="text-xl font-bold mb-2">电子邮件</div>
                  <a href="mailto:<EMAIL>" className="text-white/80 hover:text-white text-lg"><EMAIL></a>
                </div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:border-white/40 transition-all hover:scale-105">
              <div className="flex items-center gap-6">
                <div className="w-16 h-16 bg-white/10 rounded-2xl flex items-center justify-center">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <div>
                  <div className="text-xl font-bold mb-2">地址</div>
                  <span className="text-white/80 text-lg">上海环球金融中心</span>
                </div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:border-white/40 transition-all hover:scale-105">
              <div className="flex items-center gap-6">
                <div className="w-16 h-16 bg-white/10 rounded-2xl flex items-center justify-center">
                  <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
                  </svg>
                </div>
                <div>
                  <div className="text-xl font-bold mb-2">Telegram</div>
                  <a href="https://t.me/jianierclub" target="_blank" rel="noopener noreferrer" className="text-white/80 hover:text-white text-lg">@jianierclub</a>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-black dark:bg-black text-white">
        <div className="max-w-6xl mx-auto py-8 px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-sm mb-4 md:mb-0">
              JianierClub 版权所有
            </div>
            <div className="flex flex-wrap justify-center gap-8">
              <Link href="/" className="text-sm hover:text-gray-300 dark:hover:text-gray-500 transition-colors">
                首页
              </Link>
              <Link href="/about" className="text-sm hover:text-gray-300 dark:hover:text-gray-500 transition-colors">
                关于我们
              </Link>
              <Link href="/contact" className="text-sm hover:text-gray-300 dark:hover:text-gray-500 transition-colors">
                联系我们
              </Link>
              <Link href="/privacy" className="text-sm hover:text-gray-300 dark:hover:text-gray-500 transition-colors">
                隐私政策
              </Link>
              <Link href="/terms" className="text-sm hover:text-gray-300 dark:hover:text-gray-500 transition-colors">
                服务条款
              </Link>
            </div>
            <div className="flex gap-4 mt-4 md:mt-0">
              <a href="#" aria-label="Twitter" className="text-white hover:text-gray-300 dark:hover:text-gray-500">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5 0-.278-.028-.556-.08-.83A7.72 7.72 0 0 0 23 3z" />
                </svg>
              </a>
              <a href="#" aria-label="Facebook" className="text-white hover:text-gray-300 dark:hover:text-gray-500">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
                </svg>
              </a>
              <a href="#" aria-label="Instagram" className="text-white hover:text-gray-300 dark:hover:text-gray-500">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm0 18a8 8 0 1 1 8-8 8 8 0 0 1-8 8zm0-13a5 5 0 1 0 5 5 5 5 0 0 0-5-5zm0 8a3 3 0 1 1 3-3 3 3 0 0 1-3 3z" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}