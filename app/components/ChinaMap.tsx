import React from 'react';
import { Badge } from "../../components/ui/badge";

interface City {
  name: string;
  type: '一线' | '二线';
  coordinates: [number, number]; // [x, y] 坐标，相对于1000x1000的视口
}

const cities: City[] = [
  // 一线城市
  { name: "北京", type: "一线", coordinates: [550, 200] },
  { name: "上海", type: "一线", coordinates: [650, 350] },
  { name: "广州", type: "一线", coordinates: [550, 600] },
  { name: "深圳", type: "一线", coordinates: [570, 620] },
  { name: "杭州", type: "一线", coordinates: [630, 380] },
  
  // 二线城市
  { name: "武汉", type: "二线", coordinates: [550, 400] },
  { name: "西安", type: "二线", coordinates: [450, 350] },
  { name: "天津", type: "二线", coordinates: [570, 220] },
  { name: "苏州", type: "二线", coordinates: [640, 340] },
  { name: "南京", type: "二线", coordinates: [620, 360] },
  { name: "澳门", type: "二线", coordinates: [540, 640] },
  { name: "香港", type: "二线", coordinates: [560, 630] },
  { name: "郑州", type: "二线", coordinates: [520, 320] },
  { name: "长沙", type: "二线", coordinates: [530, 480] },
  { name: "沈阳", type: "二线", coordinates: [650, 180] },
  { name: "青岛", type: "二线", coordinates: [620, 280] },
  { name: "合肥", type: "二线", coordinates: [580, 380] },
  { name: "宁波", type: "二线", coordinates: [660, 390] },
  { name: "昆明", type: "二线", coordinates: [450, 550] },
  { name: "福州", type: "二线", coordinates: [650, 500] },
  { name: "无锡", type: "二线", coordinates: [630, 350] },
  { name: "厦门", type: "二线", coordinates: [620, 550] },
  { name: "济南", type: "二线", coordinates: [580, 280] },
  { name: "大连", type: "二线", coordinates: [630, 200] },
  { name: "贵阳", type: "二线", coordinates: [480, 520] },
  { name: "温州", type: "二线", coordinates: [650, 450] },
  { name: "泉州", type: "二线", coordinates: [630, 530] },
  { name: "银川", type: "二线", coordinates: [450, 280] },
  { name: "长春", type: "二线", coordinates: [630, 150] },
  { name: "南昌", type: "二线", coordinates: [580, 450] },
  { name: "常州", type: "二线", coordinates: [620, 340] },
  { name: "义乌", type: "二线", coordinates: [610, 400] },
  { name: "台州", type: "二线", coordinates: [640, 420] },
  { name: "湖州", type: "二线", coordinates: [600, 370] },
  { name: "徐州", type: "二线", coordinates: [590, 320] },
  { name: "太原", type: "二线", coordinates: [520, 280] },
  { name: "珠海", type: "二线", coordinates: [530, 630] },
  { name: "威海", type: "二线", coordinates: [630, 250] },
  { name: "潍坊", type: "二线", coordinates: [610, 270] },
  { name: "兰州", type: "二线", coordinates: [400, 300] },
  { name: "绍兴", type: "二线", coordinates: [640, 370] },
  { name: "西宁", type: "二线", coordinates: [380, 320] },
  { name: "海口", type: "二线", coordinates: [520, 680] },
  { name: "三亚", type: "二线", coordinates: [520, 700] },
  { name: "金华", type: "二线", coordinates: [620, 400] },
  { name: "惠州", type: "二线", coordinates: [580, 610] },
  { name: "南通", type: "二线", coordinates: [640, 330] },
  { name: "南宁", type: "二线", coordinates: [500, 600] },
  { name: "嘉兴", type: "二线", coordinates: [630, 360] },
  { name: "东莞", type: "二线", coordinates: [560, 610] },
  { name: "重庆", type: "二线", coordinates: [450, 450] },
  { name: "成都", type: "二线", coordinates: [400, 420] },
  { name: "扬州", type: "二线", coordinates: [610, 330] },
  { name: "丽江", type: "二线", coordinates: [420, 500] },
  { name: "大理", type: "二线", coordinates: [400, 480] },
  { name: "石家庄", type: "二线", coordinates: [540, 250] },
  { name: "乌鲁木齐", type: "二线", coordinates: [250, 200] },
  { name: "哈尔滨", type: "二线", coordinates: [650, 120] },
  { name: "呼和浩特", type: "二线", coordinates: [500, 220] }
];

export default function ChinaMap() {
  return (
    <div className="relative w-full aspect-[4/3] bg-white/5 rounded-xl overflow-hidden p-8">
      {/* 装饰性海洋效果 */}
      <div className="absolute inset-0">
        <div className="absolute right-0 top-0 w-1/3 h-full bg-gradient-to-l from-white/5 to-transparent opacity-50"></div>
        <div className="absolute bottom-0 left-0 w-full h-1/3 bg-gradient-to-t from-white/5 to-transparent opacity-50"></div>
      </div>

      {/* 城市标记 */}
      <div className="absolute inset-0">
        {/* 先渲染二线城市 */}
        {cities.filter(city => city.type === '二线').map((city) => (
          <div
            key={city.name}
            className="absolute transform -translate-x-1/2 -translate-y-1/2 group"
            style={{
              left: `${city.coordinates[0] / 10}%`,
              top: `${city.coordinates[1] / 10}%`,
            }}
          >
            <div className={`
              w-4 h-4 bg-[#ffa31a] shadow-lg shadow-[#ffa31a]/50
              rounded-full 
              group-hover:scale-150 transition-transform duration-300
              relative
            `}>
              <div className="absolute inset-0 rounded-full animate-ping bg-[#ffa31a]/30" />
            </div>
            
            <div className="
              absolute top-full mt-2 left-1/2 -translate-x-1/2
              opacity-0 group-hover:opacity-100 transition-opacity duration-300
              whitespace-nowrap z-10
            ">
              <Badge 
                variant="outline" 
                className="bg-white/5 text-white/80 border-white/20 shadow-lg shadow-[#ffa31a]/20 text-sm"
              >
                {city.name}
              </Badge>
            </div>
          </div>
        ))}

        {/* 后渲染一线城市，确保在最上层 */}
        {cities.filter(city => city.type === '一线').map((city) => (
          <div
            key={city.name}
            className="absolute transform -translate-x-1/2 -translate-y-1/2 group z-20"
            style={{
              left: `${city.coordinates[0] / 10}%`,
              top: `${city.coordinates[1] / 10}%`,
            }}
          >
            <div className={`
              w-6 h-6 bg-[#e50049] shadow-xl shadow-[#e50049]/50
              rounded-full 
              group-hover:scale-150 transition-transform duration-300
              relative
            `}>
              <div className="absolute inset-0 rounded-full animate-ping bg-[#e50049]/30 duration-1000" />
            </div>
            
            <div className="
              absolute top-full mt-2 left-1/2 -translate-x-1/2
              whitespace-nowrap z-20 pointer-events-none
            ">
              <Badge 
                variant="outline" 
                className="bg-[#e50049]/10 text-white border-[#e50049] shadow-xl shadow-[#e50049]/20 text-base font-medium"
              >
                {city.name}
              </Badge>
            </div>
          </div>
        ))}
      </div>

      {/* 图例 */}
      <div className="absolute bottom-4 right-4 flex flex-col gap-2 bg-black/40 backdrop-blur-sm p-4 rounded-lg z-30">
        <div className="flex items-center gap-3">
          <div className="w-6 h-6 rounded-full bg-[#e50049] shadow-xl shadow-[#e50049]/50" />
          <span className="text-base text-white font-medium">一线城市</span>
        </div>
        <div className="flex items-center gap-3">
          <div className="w-4 h-4 rounded-full bg-[#ffa31a] shadow-lg shadow-[#ffa31a]/50" />
          <span className="text-sm text-white/90 font-medium">二线城市</span>
        </div>
      </div>
    </div>
  );
} 