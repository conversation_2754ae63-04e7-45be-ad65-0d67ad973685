"use client"; // 添加客户端指令

import { useEffect, useState } from "react";
import ProfileClient from "../../components/ProfileClient";

// 元数据需要移动到单独的metadata.ts文件中，因为客户端组件不支持导出metadata
// export const metadata: Metadata = {
// 	title: "个人资料 | 佳妮俱乐部",
// 	description: "查看和管理您的个人资料。",
// 	robots: "noindex, nofollow", // 防止搜索引擎索引个人资料页面
// };

export default function ProfilePage() {
	// 使用状态管理技术确保页面状态持久化
	// 在开发者控制台中记录组件生命周期，便于调试
	useEffect(() => {
		console.log('ProfilePage 组件已挂载');
		
		// 返回清理函数以检测组件卸载
		return () => {
			console.log('ProfilePage 组件已卸载');
		};
	}, []);

	return <ProfileClient />;
}
