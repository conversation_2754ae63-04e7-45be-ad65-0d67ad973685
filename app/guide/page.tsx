import { Metadata } from "next";
import Link from "next/link";
import {
  Clock,
  Shield,
  CreditCard,
  MapPin,
  Star,
  Diamond,
  Crown,
  Check,
  AlertTriangle,
  Heart,
  Gift,
  Calendar,
  Phone,
  MessageCircle,
  UserCheck,
  Lock
} from "lucide-react";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";

import { Badge } from "../../components/ui/badge";
import { Separator } from "../../components/ui/separator";
import { Button } from "../../components/ui/button";
import ChinaMap from "../components/ChinaMap";

export const metadata: Metadata = {
  title: '约会指南 | 佳妮俱乐部 | 高端约会、商务模特、外围、福利姬、上门、探花',
  description: '佳妮俱乐部官方约会指南，详细介绍高端约会、商务模特、外围、福利姬、上门、探花服务流程、价位、常见问题等重要信息。提供高端伴游、包养、同城约会、空降包夜等专业服务指导，为高素质客户提供安全私密的约会体验。',
  keywords: '佳妮,约会指南,高端约会,约会流程,常见问题,外围,福利姬,上门,探花,高端伴游,商务伴游,包养,伴游,商务模特,同城约会,同城约炮,高端定制,高端外围,商务定制,外围模特,礼仪模特,外围资源,模特资源,约会交友,私人订制,外围女,兼职,空乘兼职,会所,模特会所,空降,包夜,网红兼职,网红模特,抖音网红,探花,金主,SPA,按摩,二奶,模特兼职,礼仪兼职,陪玩,陪玩平台,酒店,酒店服务,品茶,小姐服务,小姐,找小姐,桑拿,嫩模,汗蒸,水疗,温泉,少妇,温泉酒店,学生妹兼职,老师兼职,空姐兼职,艺术生兼职,主持人兼职,健身教练兼职,演员兼职,夜店,极品约会,品茶交流,品茶上课,大保健服务,交友软件,上门服务,会所工作室,工作室,俱乐部,线下,线下约会',
  alternates: {
    canonical: "https://www.jianier.com/guide"
  }
};

const TimelineItem = ({
  step,
  title,
  description,
  icon: Icon
}: {
  step: number;
  title: string;
  description: string;
  icon: any;
}) => (
  <div className="flex items-start gap-4 relative">
    <div className="flex-shrink-0 w-12 h-12 bg-[#e50049]/10 rounded-full flex items-center justify-center">
      <Icon className="w-6 h-6 text-[#e50049]" />
    </div>
    <div className="flex-1">
      <div className="flex items-center gap-2 mb-1">
        <span className="text-[#e50049] font-bold">步骤 {step}</span>
        <h3 className="font-semibold text-white">{title}</h3>
      </div>
      <p className="text-white">{description}</p>
    </div>
    {step < 7 && (
      <div className="absolute left-6 top-12 w-[2px] h-16 bg-gradient-to-b from-[#e50049] to-transparent"></div>
    )}
  </div>
);

const ServiceCard = ({
  icon: Icon,
  title,
  description
}: {
  icon: any;
  title: string;
  description: string;
}) => (
  <Card className="bg-white/5 border-white/10 hover:bg-white/10 transition-colors">
    <CardHeader>
      <div className="w-12 h-12 bg-[#e50049]/10 rounded-full flex items-center justify-center mb-4">
        <Icon className="w-6 h-6 text-[#e50049]" />
      </div>
      <CardTitle className="text-[#ffa31a]">{title}</CardTitle>
    </CardHeader>
    <CardContent>
      <p className="text-white">{description}</p>
    </CardContent>
  </Card>
);

function generateFAQSchema() {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "佳妮俱乐部的价位是多少？",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "单次3K 过夜8K起，更高端的有次5K/ 次万。我们提供多个价位档次的服务，满足不同客户需求。"
        }
      },
      {
        "@type": "Question", 
        "name": "佳妮俱乐部是不是骗子？",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "请客户自行鉴别。我们建议通过官方渠道联系，注意保护个人信息安全。"
        }
      },
      {
        "@type": "Question",
        "name": "预约服务需不需要定金？",
        "acceptedAnswer": {
          "@type": "Answer", 
          "text": "同城不需要定金，路程一小时以上需要定金，以免被鸽。这是为了保障双方的时间和利益。"
        }
      },
      {
        "@type": "Question",
        "name": "付款方式是什么？", 
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "见面付给女孩本人。我们建议使用现金支付，这是最安全的付款方式。"
        }
      },
      {
        "@type": "Question",
        "name": "为什么要设置门槛？",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "过滤口嗨、记者、鸽子党，仅为优质客户服务。门槛200元，约会可以抵扣，等于免费入会，门槛主要起过滤作用。"
        }
      }
    ]
  };
}

export default function GuidePage() {
  const faqSchema = generateFAQSchema();
  
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(faqSchema),
        }}
      />
      <main className="min-h-screen bg-background">
      {/* Background Effects */}
      <div className="fixed inset-0 w-full h-full bg-gradient-to-b from-background to-background">
        <div className="absolute inset-0 w-full h-full bg-[radial-gradient(ellipse_at_top_right,rgba(229,0,73,0.15),rgba(229,0,73,0)_50%)]"></div>
        <div className="absolute inset-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(229,0,73,0.1),rgba(229,0,73,0)_50%)]"></div>
      </div>

      {/* Soft Pattern Background */}
      <div className="fixed inset-0 w-full h-full opacity-30">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(229,0,73,0.05)_1px,transparent_1px)] bg-[size:32px_32px]"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(229,0,73,0.05)_1px,transparent_1px)] bg-[size:24px_24px] rotate-15"></div>
      </div>

      <div className="relative max-w-5xl mx-auto px-4 pt-24 pb-16">
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-[#e50049]/10 rounded-full mb-8">
            <Star className="w-4 h-4 text-[#ffa31a]" />
            <span className="text-white">高端约会服务指南</span>
          </div>

          <h1 className="text-5xl md:text-[6rem] font-bold text-white mb-8">
            约会指南
          </h1>

          <div className="relative mb-12">
            <img
              src="/images/innovation.png"
              alt="Innovation"
              className="mx-auto rounded-xl shadow-lg"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-background/10 to-transparent"></div>
          </div>

          <div className="max-w-2xl mx-auto">
            <p className="text-2xl text-white mb-4">
              欢迎来到佳妮约会俱乐部，这里是您的
              <span className="text-[#e50049]">专属约会指南</span>
            </p>
            <p className="text-lg text-white mb-12">
              本俱乐部旨在为高素质的客人，提供高端私密的约会服务。
              <br />
              我们注重品质、安全和隐私保护。
            </p>
          </div>



          {/* Action Buttons */}
          <div className="flex justify-center gap-4 mb-16">
            <Button
              asChild
              className="bg-[#e50049] hover:bg-[#c8003f] text-white px-8"
            >
              <Link href="/register">
                入会&咨询安排
              </Link>
            </Button>
            <Button
              asChild
              className="bg-[#0088cc] hover:bg-[#006699] text-white px-8"
            >
              <a
                href="https://t.me/Jianierbot"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2"
              >
                <span>Telegram 客服</span>
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.041-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
                </svg>
              </a>
            </Button>
          </div>

          {/* FAQ Section */}
          <Card className="bg-transparent border-0 mb-12">
            <CardHeader>
              <CardTitle className="text-3xl font-bold text-[#ffa31a] tracking-tight">约会快速 Q&A</CardTitle>
              <CardDescription className="text-white">解答您最关心的问题</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6">
                {[
                  { q: "价位？", a: "单次3K 过夜8K起，更高端的有次5K/ 次万", icon: CreditCard },
                  { q: "是不是骗子？", a: "自行鉴别。", icon: Shield },
                  { q: "需不需要定金？", a: "同城不需要定金，路程一小时以上需要，以免被鸽", icon: Lock },
                  { q: "付款方式？", a: "见面付给女孩本人", icon: CreditCard },
                  { q: "为什么要门槛？", a: "过滤口嗨、记者、鸽子党，仅为优质客户服务", icon: Shield }
                ].map((item, index) => (
                  <div
                    key={index}
                    className="p-6 rounded-lg flex items-start gap-4"
                  >
                    <div className="flex-shrink-0">
                      <item.icon className="w-6 h-6 text-[#e50049]" />
                    </div>
                    <div>
                      <p className="font-bold mb-2 text-[#ffa31a]">Q：{item.q}</p>
                      <p className="text-white">A：{item.a}</p>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-6 p-4 bg-[#e50049]/10 rounded-lg">
                <p className="text-white flex items-center gap-2">
                  <AlertTriangle className="w-5 h-5 text-[#ffa31a]" />
                  门槛200🧧，约会可以抵扣等于免费入会，门槛主要起过滤作用。
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Process Section */}
          <Card className="bg-transparent border-0 mb-12">
            <CardHeader>
              <CardTitle className="text-2xl font-bold text-[#ffa31a]">约会流程</CardTitle>
              <CardDescription className="text-white">清晰的约会流程指引</CardDescription>
            </CardHeader>
            <CardContent className="space-y-8">
              <TimelineItem
                step={1}
                icon={UserCheck}
                title="入会认证"
                description="过 200 🧧门槛入会（可抵扣），完成入会登记"
              />
              <TimelineItem
                step={2}
                icon={Heart}
                title="选择心仪对象"
                description="资源库浏览预约 / 定制实时资料选妃"
              />
              <TimelineItem
                step={3}
                icon={Calendar}
                title="确定时间地点"
                description="确定具体时间&地点，确认定单"
              />
              <TimelineItem
                step={4}
                icon={MapPin}
                title="见面"
                description="等待女孩上门或者你去她的工作室"
              />
              <TimelineItem
                step={5}
                icon={CreditCard}
                title="付款"
                description="见面付给女孩本人"
              />
              <TimelineItem
                step={6}
                icon={Gift}
                title="享受服务"
                description="享受专业贴心的服务"
              />
              <TimelineItem
                step={7}
                icon={MessageCircle}
                title="反馈回访"
                description="回访会员服务评价反馈"
              />
            </CardContent>
          </Card>

          {/* Pricing Section */}
          <Card className="bg-transparent border-0 mb-12">
            <CardHeader>
              <CardTitle className="text-3xl font-bold text-[#ffa31a] tracking-tight">服务价位</CardTitle>
              <CardDescription className="text-white">透明的价格体系</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                {[
                  {
                    title: "3K档",
                    price: "3000",
                    night: "8000",
                    features: ["一次1小时", "过夜8小时起", "基础服务"],
                    icon: Star
                  },
                  {
                    title: "5K档",
                    price: "5000",
                    night: "15000",
                    features: ["一次1小时", "过夜8小时起", "进阶服务", "更多选择"],
                    icon: Diamond
                  },
                  {
                    title: "万档",
                    price: "10000",
                    night: "30000",
                    features: ["一次1小时", "过夜8小时起", "顶级服务", "VIP待遇"],
                    icon: Crown
                  },
                  {
                    title: "定制服务",
                    price: "按需定制",
                    night: "无上限",
                    features: ["个性化定制", "专属服务", "灵活时长", "特殊需求"],
                    icon: Gift
                  }
                ].map((tier, index) => (
                  <Card key={index} className="bg-transparent border-0">
                    <CardHeader>
                      <div className="w-12 h-12 bg-[#e50049]/10 rounded-full flex items-center justify-center mb-4">
                        <tier.icon className="w-6 h-6 text-[#e50049]" />
                      </div>
                      <CardTitle className="text-[#ffa31a]">{tier.title}</CardTitle>
                      <div className="mt-2">
                        <span className="text-3xl font-bold text-white">¥{tier.price}</span>
                        <span className="text-white">/次</span>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {tier.features.map((feature, i) => (
                          <div key={i} className="flex items-center gap-2 text-white">
                            <Check className="w-4 h-4 text-[#e50049]" />
                            <span>{feature}</span>
                          </div>
                        ))}
                      </div>
                      <div className="mt-4 pt-4 border-t border-white/10">
                        <div className="text-sm text-white">过夜价格</div>
                        <div className="text-lg font-semibold text-white">¥{tier.night}</div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Service Areas */}
          <Card className="bg-transparent border-0 mb-12">
            <CardHeader>
              <CardTitle className="text-3xl font-bold text-[#ffa31a] tracking-tight">服务区域</CardTitle>
              <CardDescription className="text-white">覆盖全国主要城市</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <p className="text-white">佳妮主打全国一二线城市，服务范围包括：</p>

              <ChinaMap />

              <div className="flex items-center gap-2 p-4 bg-[#e50049]/10 rounded-lg">
                <Shield className="w-5 h-5 text-[#ffa31a]" />
                <p className="text-[#ffa31a]">
                  所有资料库中的女孩，都经过佳妮亲自认证，保证真实可靠。如有其他城市需求，可以联系我们进行咨询。
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Custom Service */}
          <Card className="bg-transparent border-0 mb-12">
            <CardHeader>
              <CardTitle className="text-3xl font-bold text-[#ffa31a] tracking-tight">高端定制服务</CardTitle>
              <CardDescription className="text-white">多样化的私人定制选项</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                {[
                  { name: "单次", icon: Clock },
                  { name: "过夜", icon: Calendar },
                  { name: "包时", icon: Clock },
                  { name: "包天", icon: Calendar },
                  { name: "空降", icon: MapPin },
                  { name: "双飞", icon: Heart },
                  { name: "SM", icon: Lock },
                  { name: "伴游", icon: MapPin },
                  { name: "包养", icon: Diamond },
                  { name: "明星", icon: Star },
                  { name: "网红", icon: Crown },
                  { name: "特殊需求", icon: Gift }
                ].map((service) => (
                  <div
                    key={service.name}
                    className="p-4 rounded-lg flex items-center gap-3 hover:bg-white/10 transition-colors"
                  >
                    <service.icon className="w-5 h-5 text-[#e50049]" />
                    <span className="text-white">{service.name}</span>
                  </div>
                ))}
              </div>
              <Separator className="my-6 bg-white/10" />
              <p className="text-white flex items-center gap-2">
                <Gift className="w-5 h-5 text-[#e50049]" />
                我们提供多样化的定制服务，可以根据您的需求进行安排。
              </p>
            </CardContent>
          </Card>

          {/* Safety Section */}
          <Card className="bg-transparent border-0 mb-12">
            <CardHeader>
              <CardTitle className="text-3xl font-bold text-[#ffa31a] tracking-tight">安全与私密性保障</CardTitle>
              <CardDescription className="text-white">我们重视每位客户的安全与隐私</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6">
                {[
                  {
                    icon: Shield,
                    title: "健康保障",
                    text: "所有从业者都会进行定期检查，注重个人卫生。"
                  },
                  {
                    icon: CreditCard,
                    title: "支付方式",
                    text: "建议使用现金，避免留下交易记录。"
                  },
                  {
                    icon: MapPin,
                    title: "场所选择",
                    text: "均为高端公寓，位置私密安全。"
                  },
                  {
                    icon: Phone,
                    title: "社交安全",
                    text: "不建议加微信，避免信息泄露。"
                  },
                  {
                    icon: MessageCircle,
                    title: "通讯方式",
                    text: "推荐使用Telegram，聊天记录可随时删除。"
                  },
                  {
                    icon: Lock,
                    title: "最佳实践",
                    text: "现金+电报是最安全的组合。"
                  }
                ].map((item, index) => (
                  <Card key={index} className="bg-transparent border-0">
                    <CardHeader>
                      <div className="w-12 h-12 bg-[#e50049]/10 rounded-full flex items-center justify-center mb-4">
                        <item.icon className="w-6 h-6 text-[#e50049]" />
                      </div>
                      <CardTitle className="text-[#ffa31a]">{item.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-white">{item.text}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Other Information */}
          <Card className="bg-transparent border-0 mb-12">
            <CardHeader>
              <CardTitle className="text-3xl font-bold text-[#ffa31a] tracking-tight">其他说明</CardTitle>
              <CardDescription className="text-white">重要提示与补充说明</CardDescription>
            </CardHeader>
            <CardContent className="space-y-8">
              <div>
                <h3 className="text-xl font-bold text-[#ffa31a] mb-3 flex items-center gap-2">
                  <AlertTriangle className="w-5 h-5" />
                  关于地区质量差异
                </h3>
                <div className="space-y-4 text-white">
                  <p>佳妮主要做北上广深杭+二三线城市，是因为其他地区水比较深，主要是照骗问题，容易踩雷，浪费彼此时间。</p>
                  <p>像一些二线省会城市还可以，但再往下就只能开盲盒了。例如三亚/重庆/成都，这些地方虽然也有优质资源，但通常不会提供真实照片。为了保证约会质量，除非您强烈需要且同意开盲盒，否则我们一般不会安排这些城市的服务。</p>
                </div>
              </div>

              <Separator className="bg-white/10" />

              <div>
                <h3 className="text-xl font-bold text-[#ffa31a] mb-3 flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  关于定金与门槛
                </h3>
                <div className="space-y-4">
                  <Card className="bg-transparent border-0">
                    <CardHeader>
                      <CardTitle className="text-[#ffa31a] text-lg">门槛说明</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-white">入会门槛200元，可在3K价位服务中抵扣（见面只需支付2800）。设置门槛的目的是为了过滤非诚意约会者。</p>
                    </CardContent>
                  </Card>

                  <Card className="bg-transparent border-0">
                    <CardHeader>
                      <CardTitle className="text-[#ffa31a] text-lg">定金政策</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-white">同城且路程在一小时以内无需定金，见面直接支付。如果路程超过一小时或需要空降服务，则需要支付定金。</p>
                    </CardContent>
                  </Card>
                </div>

                <div className="mt-6 p-4 bg-[#e50049]/10 rounded-lg flex items-center gap-2">
                  <Shield className="w-5 h-5 text-[#ffa31a]" />
                  <p className="text-[#ffa31a]">佳妮儿致力于打造长期口碑，不会为了小利而失信于人。当然，网络诈骗横行，还请大家提高警惕。</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Final CTA */}
          <div className="bg-[#e50049]/10 rounded-xl p-8 text-center">
            <h2 className="text-2xl font-bold text-white mb-4">准备好开始您的约会之旅了吗？</h2>
            <p className="text-white mb-8">加入我们，享受专业的约会服务</p>
            <div className="flex justify-center gap-4">
              <Button
                asChild
                className="bg-[#e50049] hover:bg-[#c8003f] text-white px-8"
              >
                <Link href="/register">立即入会</Link>
              </Button>
              <Button
                asChild
                variant="outline"
                className="border-[#e50049] text-[#e50049] hover:bg-[#e50049] hover:text-white"
              >
                <a
                  href="https://t.me/Jianierbot"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  联系客服
                </a>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </main>
    </>
  );
}
