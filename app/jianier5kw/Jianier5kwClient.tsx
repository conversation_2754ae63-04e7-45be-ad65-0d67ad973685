"use client";

import React, { useState, useEffect, useMemo, useCallback } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useSearchParams } from "next/navigation";
import Masonry from "react-masonry-css";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { firstTierCities } from "@/lib/cityNameMapping";
import { getGirls, getCityCounts, getDistrictCounts, getPriceCounts, getCups, getTypes, getServices } from "@/lib/api";
import { Girl } from "@/types/girl";
import GirlCard from "@/components/GirlCard";
import GirlCardSkeleton from "@/components/GirlCardSkeleton";
import Pagination from "@/components/Pagination";
import ScrollButtons from "@/components/ScrollButtons";
import { SelectItemWrapper } from "@/components/SelectItemWrapper";
import { normalizeDistrict, getDisplayDistrict, getDistrictVariants, mergeDistrictCounts } from "@/lib/districtUtils";
import { cityDistricts } from "@/lib/cityDistricts";
import "@/styles/masonry.css";

interface Jianier5kwClientProps {
  initialData: {
    girls: Girl[];
    totalPages: number;
    totalGirls: number;
  };
}

// 根据窗口宽度确定列数的辅助函数
const getColumnCountByWidth = (width: number): number => {
  if (width <= 768) return 1;
  if (width <= 1200) return 2;
  if (width <= 1600) return 3;
  if (width <= 2000) return 4;
  return 5;
};

// 修改为使用与feedback页面相同的breakpointColumnsObj配置
const breakpointColumnsObj = {
  default: 5,
  2000: 4,
  1600: 3,
  1200: 2,
  768: 1,
  500: 1,
  400: 1,
  350: 1
};

// 修复：移除初始值计算，防止服务端和客户端不一致
const initialColumnCount = 4; // 默认为4列，对所有设备统一起始值

const DEFAULT_CUPS = { "A": 0, "B": 0, "C": 0, "D": 0, "E": 0, "F": 0 };
const DEFAULT_TYPES = { "角色": 0, "骚浪": 0, "清纯": 0, "风骚": 0, "双飞": 0 };
const DEFAULT_SERVICES = { "口爆": 0, "颜射": 0, "69式": 0, "制服": 0, "丝袜": 0 };

function Jianier5kwClientContent({ initialData }: Jianier5kwClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session } = useSession();
  const [girlsData, setGirlsData] = useState(initialData);
  const [cities, setCities] = useState<string[]>([]);
  const [cityCounts, setCityCounts] = useState<Record<string, number>>({});
  const [districtCounts, setDistrictCounts] = useState<Record<string, number>>({});
  const [priceCounts, setPriceCounts] = useState<Record<string, number>>({});
  const [cups, setCups] = useState<Record<string, number>>(DEFAULT_CUPS);
  const [types, setTypes] = useState<Record<string, number>>(DEFAULT_TYPES);
  const [services, setServices] = useState<Record<string, number>>(DEFAULT_SERVICES);
  const [hasAttemptedLoad, setHasAttemptedLoad] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [imagesLoading, setImagesLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [cardsPerPage, setCardsPerPage] = useState(20);
  // 修复：移除初始值计算，防止服务端和客户端不一致
  const [isMobile, setIsMobile] = useState(false);
  const [isExtraNarrow, setIsExtraNarrow] = useState(false);
  const [currentColumnCount, setCurrentColumnCount] = useState(initialColumnCount);
  const [filters, setFilters] = useState({
    city: searchParams.get("city") || "",
    district: searchParams.get("district") || "",
    price: "5K|W",
    cup: searchParams.get("cup") || "",
    type: searchParams.get("type") || "",
    service: searchParams.get("service") || "",
  });

  const [layoutKey, setLayoutKey] = useState(0);

  // 在组件顶部添加客户端检测
  const isClient = typeof window !== 'undefined';

  // 监听窗口大小变化并更新列数
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const updateColumnCount = () => {
      const width = window.innerWidth;
      setCurrentColumnCount(getColumnCountByWidth(width));
      setIsMobile(width <= 768);
      setIsExtraNarrow(width <= 400);
    };
    
    // 确保初始化时设置了正确的值
    updateColumnCount();
    
    window.addEventListener("resize", updateColumnCount);
    return () => window.removeEventListener("resize", updateColumnCount);
  }, []);

  // 专门用于加载城市计数的effect，确保在组件挂载时立即执行
  useEffect(() => {
    const loadCityCounts = async () => {
      try {
        console.log("【初始化】开始获取城市计数数据...");
        const cityCountsData = await getCityCounts("5K|W");
        console.log("【初始化】获取到城市计数数据:", cityCountsData);
        setCityCounts(cityCountsData);
      } catch (error) {
        console.error("【初始化】获取城市计数失败:", error);
      }
    };

    loadCityCounts();
  }, []);  // 空依赖数组，确保只在组件挂载时执行一次

  // 添加重试函数
  const fetchWithRetry = async (fetchFn: () => Promise<any>, retries = 3, label: string) => {
    let lastError;
    for (let i = 0; i < retries; i++) {
      try {
        return await fetchFn();
      } catch (error) {
        console.error(`【重试${i+1}/${retries}】${label}失败:`, error);
        lastError = error;
        // 等待一小段时间再重试
        await new Promise(r => setTimeout(r, 1000));
      }
    }
    throw lastError;
  };

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const normalizedFilters = {
          city: filters.city,
          district: filters.district,
          price: filters.price || "5K|W",
          cup: filters.cup,
          type: filters.type,
          service: filters.service
        };

        console.log('【DEBUG-筛选器】开始获取数据，筛选条件:', normalizedFilters);
        setHasAttemptedLoad(true);

        // 未登录用户只加载6个卡片
        const effectiveCardsPerPage = session?.user ? cardsPerPage : 6;

        // 分别处理每个Promise，以便单独捕获错误
        try {
          const girlsResponse = await getGirls(currentPage, effectiveCardsPerPage, normalizedFilters);
          console.log(`【DEBUG-女孩数据】获取成功：${girlsResponse.girls.length}个结果`);
          setGirlsData(girlsResponse);
        } catch (error) {
          console.error("【ERROR】获取女孩数据失败:", error);
        }

        try {
          const districtCountsData = await getDistrictCounts(normalizedFilters.city);
          console.log('【DEBUG-区域数据】获取成功:', Object.keys(districtCountsData).length);
          const normalizedDistrictCounts = mergeDistrictCounts(districtCountsData);
          setDistrictCounts(normalizedDistrictCounts);
        } catch (error) {
          console.error("【ERROR】获取区域数据失败:", error);
        }

        try {
          const priceCountsData = await getPriceCounts(normalizedFilters.city);
          console.log('【DEBUG-价格数据】获取成功:', Object.keys(priceCountsData).length);
          setPriceCounts(priceCountsData);
        } catch (error) {
          console.error("【ERROR】获取价格数据失败:", error);
        }

        // 使用重试逻辑获取cups数据
        try {
          const cups = await fetchWithRetry(
            () => getCups(normalizedFilters.city, normalizedFilters.price), 
            3, 
            "获取罩杯数据"
          );
          console.log('【DEBUG-罩杯数据】获取成功:', Object.keys(cups).length, cups);
          setCups(cups);
        } catch (error) {
          console.error("【ERROR】获取罩杯数据最终失败:", error);
          // 设置默认值确保UI不会崩溃
          setCups(DEFAULT_CUPS);
        }

        // 使用重试逻辑获取types数据
        try {
          const types = await fetchWithRetry(
            () => getTypes(normalizedFilters.city, normalizedFilters.price), 
            3, 
            "获取类型数据"
          );
          console.log('【DEBUG-类型数据】获取成功:', Object.keys(types).length, types);
          setTypes(types);
        } catch (error) {
          console.error("【ERROR】获取类型数据最终失败:", error);
          // 设置默认值确保UI不会崩溃
          setTypes(DEFAULT_TYPES);
        }

        // 使用重试逻辑获取services数据
        try {
          const services = await fetchWithRetry(
            () => getServices(normalizedFilters.city, normalizedFilters.price), 
            3, 
            "获取服务数据"
          );
          console.log('【DEBUG-服务数据】获取成功:', Object.keys(services).length, services);
          setServices(services);
        } catch (error) {
          console.error("【ERROR】获取服务数据最终失败:", error);
          // 设置默认值确保UI不会崩溃
          setServices(DEFAULT_SERVICES);
        }

        setCities(firstTierCities);
      } catch (error) {
        console.error("【ERROR】数据获取整体失败:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [currentPage, cardsPerPage, filters, session?.user]);

  useEffect(() => {
    if (!isLoading && girlsData.girls.length > 0) {
      setImagesLoading(true);
      
      const initialTimer = setTimeout(() => {
        setLayoutKey(prev => prev + 1);
      }, 500);
      
      const fullLoadTimer = setTimeout(() => {
        setImagesLoading(false);
        setLayoutKey(prev => prev + 1);
      }, 2000);
      
      return () => {
        clearTimeout(initialTimer);
        clearTimeout(fullLoadTimer);
      };
    }
  }, [isLoading, girlsData.girls]);

  const updateUrlParams = useCallback((newFilters: typeof filters) => {
    const params = new URLSearchParams();
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value) {
        params.set(key, value);
      }
    });
    const newUrl = window.location.pathname + (params.toString() ? `?${params.toString()}` : "");
    window.history.replaceState({}, "", newUrl);
  }, []);

  const handleFilterChange = useCallback((key: string, value: string) => {
    const newFilters = {
      ...filters,
      [key]: value === "_all" ? "" : value,
    };
    setFilters(newFilters);
    setCurrentPage(1);
    updateUrlParams(newFilters);
  }, [filters, updateUrlParams]);

  const handlePageChange = useCallback((newPage: number) => {
    setCurrentPage(newPage);
  }, []);

  const sortedPriceCounts = Object.entries(priceCounts).sort((a, b) => b[1] - a[1]);

  const renderFilters = useMemo(() => {
    // 调试日志
    console.log("【DEBUG-渲染筛选器】", {
      cups: Object.keys(cups).length,
      types: Object.keys(types).length,
      services: Object.keys(services).length,
      hasAttemptedLoad
    });
    
    return (
      <div className="w-full">
        <div className="grid grid-cols-2 md:flex md:flex-row w-full gap-2">
          <Select onValueChange={(value) => handleFilterChange("city", value)} value={filters.city || "_all"}>
            <SelectTrigger className="w-full h-9 px-2 text-sm text-dark-foreground">
              <SelectValue placeholder="选择城市" />
            </SelectTrigger>
            <SelectContent className="bg-black text-white border-gray-800">
              <SelectItem value="_all">所有城市</SelectItem>
              {firstTierCities.map((city) => {
                // 使用API返回的真实数据
                const count = cityCounts[city] || 0;
                
                console.log(`【DEBUG-渲染项】${city}的显示数量:`, count);
                
                return (
                  <SelectItem key={city} value={city}>
                    {city} ({count})
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>

          {filters.city && (
            <Select onValueChange={(value) => handleFilterChange("district", value)} value={filters.district || "_all"}>
              <SelectTrigger className="w-full h-9 px-2 text-sm text-dark-foreground">
                <SelectValue placeholder="选择区域" />
              </SelectTrigger>
              <SelectContent className="bg-black text-white border-gray-800">
                <SelectItem value="_all">所有区域</SelectItem>
                {cityDistricts[filters.city]
                  ? cityDistricts[filters.city]
                      .filter((district) => {
                        const normalizedDistrict = normalizeDistrict(district);
                        return normalizedDistrict && districtCounts[normalizedDistrict] > 0;
                      })
                      .sort((a, b) => {
                        const countA = districtCounts[normalizeDistrict(a)] || 0;
                        const countB = districtCounts[normalizeDistrict(b)] || 0;
                        return countB - countA;
                      })
                      .map((district) => {
                        const normalizedDistrict = normalizeDistrict(district);
                        const displayName = getDisplayDistrict(normalizedDistrict);
                        const count = districtCounts[normalizedDistrict] || 0;
                        return (
                          <SelectItemWrapper key={district} value={normalizedDistrict}>
                            {displayName} ({count})
                          </SelectItemWrapper>
                        );
                      })
                  : Object.entries(districtCounts)
                      .filter(([_, count]) => count > 0)
                      .sort(([, a], [, b]) => b - a)
                      .map(([district, count]) => {
                        const normalizedDistrict = normalizeDistrict(district);
                        const displayName = getDisplayDistrict(normalizedDistrict);
                        return (
                          <SelectItemWrapper key={district} value={normalizedDistrict}>
                            {displayName} ({count})
                          </SelectItemWrapper>
                        );
                      })}
              </SelectContent>
            </Select>
          )}

          <Select onValueChange={(value) => handleFilterChange("price", value)} value={filters.price || "5K|W"}>
            <SelectTrigger className="w-full h-9 px-2 text-sm text-dark-foreground">
              <SelectValue placeholder="选择档次" />
            </SelectTrigger>
            <SelectContent className="bg-black text-white border-gray-800">
              <SelectItem value="5K|W">5K&W 极品资源</SelectItem>
              <SelectItem value="5K">5K{priceCounts["5K"] ? ` (${priceCounts["5K"]})` : ""}</SelectItem>
              <SelectItem value="W">W{priceCounts["W"] ? ` (${priceCounts["W"]})` : ""}</SelectItem>
            </SelectContent>
          </Select>

          <Select onValueChange={(value) => handleFilterChange("cup", value)} value={filters.cup || "_all"}>
            <SelectTrigger className="w-full h-9 px-2 text-sm text-dark-foreground">
              <SelectValue placeholder={isLoading && !hasAttemptedLoad ? "加载中..." : "选择罩杯"} />
            </SelectTrigger>
            <SelectContent className="bg-black text-white border-gray-800">
              <SelectItem value="_all">所有罩杯</SelectItem>
              {Object.keys(cups).length > 0 ? (
                Object.entries(cups)
                  .filter(([_, count]) => count > 0)
                  .sort(([cupA], [cupB]) => {
                    // 按照罩杯字母顺序排序：A, B, C, D, E, F 等
                    return cupA.localeCompare(cupB);
                  })
                  .map(([cup, count]) => (
                    <SelectItem key={cup} value={cup}>
                      {cup || "未知"} ({count})
                      {cup.match(/[A-Z]/) && <span className="text-xs text-gray-400 ml-1"></span>}
                    </SelectItem>
                  ))
              ) : (
                <SelectItem value="_loading" disabled>
                  {isLoading ? "加载中..." : "暂无数据"}
                </SelectItem>
              )}
            </SelectContent>
          </Select>

          <Select onValueChange={(value) => handleFilterChange("type", value)} value={filters.type || "_all"}>
            <SelectTrigger className="w-full h-9 px-2 text-sm text-dark-foreground">
              <SelectValue placeholder={isLoading && !hasAttemptedLoad ? "加载中..." : "选择类型"} />
            </SelectTrigger>
            <SelectContent className="bg-black text-white border-gray-800">
              <SelectItem value="_all">所有类型</SelectItem>
              {Object.keys(types).length > 0 ? (
                Object.entries(types)
                  .filter(([_, count]) => count > 0)
                  .sort(([, a], [, b]) => b - a)
                  .map(([type, count]) => (
                    <SelectItem key={type} value={type}>
                      {type || "未知"} ({count})
                    </SelectItem>
                  ))
              ) : (
                <SelectItem value="_loading" disabled>
                  {isLoading ? "加载中..." : "暂无数据"}
                </SelectItem>
              )}
            </SelectContent>
          </Select>

          <Select onValueChange={(value) => handleFilterChange("service", value)} value={filters.service || "_all"}>
            <SelectTrigger className="w-full h-9 px-2 text-sm text-dark-foreground">
              <SelectValue placeholder={isLoading && !hasAttemptedLoad ? "加载中..." : "选择服务"} />
            </SelectTrigger>
            <SelectContent className="bg-black text-white border-gray-800">
              <SelectItem value="_all">所有服务</SelectItem>
              {Object.keys(services).length > 0 ? (
                Object.entries(services)
                  .filter(([_, count]) => count > 0)
                  .sort(([, a], [, b]) => b - a)
                  .map(([service, count]) => (
                    <SelectItem key={service} value={service}>
                      {service || "未知"} ({count})
                    </SelectItem>
                  ))
              ) : (
                <SelectItem value="_loading" disabled>
                  {isLoading ? "加载中..." : "暂无数据"}
                </SelectItem>
              )}
            </SelectContent>
          </Select>
        </div>
      </div>
    );
  }, [filters, cityCounts, districtCounts, priceCounts, cups, types, services, handleFilterChange, currentColumnCount, isLoading, hasAttemptedLoad]);
  
  // 只在客户端渲染Masonry组件
  const renderMasonry = () => {
    if (typeof window === 'undefined') {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {Array.from({ length: cardsPerPage }).map((_, index) => (
            <div key={index} className="mb-4 w-full">
              <GirlCardSkeleton />
            </div>
          ))}
        </div>
      );
    }
    
    if (isLoading) {
      return (
        <Masonry
          key={`loading-${layoutKey}`}
          breakpointCols={breakpointColumnsObj}
          className={`my-masonry-grid ${isExtraNarrow ? 'extra-narrow-screen' : ''}`}
          columnClassName="my-masonry-grid_column"
        >
          {Array.from({ length: cardsPerPage }).map((_, index) => (
            <div key={index} className="mb-4 w-full">
              <GirlCardSkeleton />
            </div>
          ))}
        </Masonry>
      );
    }
    
    return (
      <>
        <Masonry
          key={`loaded-${layoutKey}`}
          breakpointCols={breakpointColumnsObj}
          className={`my-masonry-grid ${isExtraNarrow ? 'extra-narrow-screen' : ''}`}
          columnClassName="my-masonry-grid_column"
        >
          {girlsData.girls.map((girl) => (
            <div key={girl.id} className="mb-4 w-full mx-auto girl-card">
              <GirlCard 
                data={girl} 
                forceShowContent={!!session?.user?.isVIP}
              />
            </div>
          ))}
        </Masonry>
        
        {session?.user ? (
          <div className={`mt-12 mb-4 flex justify-center ${isMobile ? 'pb-12' : ''}`}>
            <Pagination 
              currentPage={currentPage} 
              totalPages={girlsData.totalPages} 
              onPageChange={handlePageChange}
              isMobile={isMobile}
            />
          </div>
        ) : (
          <div className="mt-8 text-center">
            <p className="text-gray-400 mb-4">注册登录后查看更多内容</p>
          </div>
        )}
      </>
    );
  };

  return (
    <div className={`${isExtraNarrow ? 'px-3' : 'px-4'} pt-4 pb-2 max-w-full mx-auto ${imagesLoading ? 'masonry-loading' : ''}`}>
      <div className="flex flex-wrap gap-4 mb-6">
        {renderFilters}
      </div>
      <div className="w-full">
        {renderMasonry()}
      </div>
      {!isLoading && <ScrollButtons />}
    </div>
  );
}

// 使用memo封装组件
const Jianier5kwClient = React.memo(Jianier5kwClientContent);

// 添加displayName，方便调试
Jianier5kwClientContent.displayName = "Jianier5kwClientContent";
Jianier5kwClient.displayName = "Jianier5kwClient";

export default Jianier5kwClient; 