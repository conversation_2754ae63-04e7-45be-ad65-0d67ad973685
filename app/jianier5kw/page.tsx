import { Metadata } from "next";
import { getGirls } from "@/lib/api";
// 使用别名导入dynamic
import { default as DynamicImport } from 'next/dynamic';

export const metadata: Metadata = {
  title: "⁵ꪝ极品营 | 顶级资源 | 高端约会、商务模特、外围、福利姬、上门、探花",
  description: "极品营专属顶级资源库，汇聚5K/W级高端约会、商务模特、外围、福利姬、上门、探花极品资源。严格筛选，顶级体验，提供高端伴游、包养、同城约会、空降包夜等私人定制服务。",
};

// 强制动态路由，完全禁用缓存
export const dynamic = 'force-dynamic';
export const revalidate = 0;
export const fetchCache = 'force-no-store';

// 使用动态导入，禁用SSR
const Jianier5kwClient = DynamicImport(
  () => import('./Jianier5kwClient'),
  { ssr: false }
);

export default async function Jianier5kwPage() {
  // 获取5K|W级别的资源
  const data = await getGirls(1, 20, {
    city: "",
    district: "",
    price: "5K|W"
  });
  
  console.log(`Jianier5kw Page - 初始数据加载完成: 总共 ${data.totalGirls} 个女孩，共 ${data.totalPages} 页`);
  
  return (
    <div className="min-h-screen">
      <Jianier5kwClient initialData={data} />
    </div>
  );
} 