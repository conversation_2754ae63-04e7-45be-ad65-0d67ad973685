import { Metadata } from "next";

export const metadata: Metadata = {
  title: "博客园 | 佳妮俱乐部 | 高端约会、商务模特、外围、福利姬、上门、探花",
  description: "佳妮俱乐部博客在线，分享高端约会、商务模特、外围、福利姬、上门、探花行业内幕和精彩攻略。提供高端伴游、包养、同城约会技巧分享，约会经验交流，帮助用户更好享受私密服务。",
};

interface BlogPost {
  id: string;
  title: string;
  content: string;
}

const blogPosts: BlogPost[] = [
  {
    id: "1",
    title: "敬请期待",
    content: "建设中...",
  },
  // 更多文章可以稍后添加
];

export default function BlogPage() {
  return (
    <div className="container mx-auto py-8">
      <div className="space-y-8">
        {blogPosts.map((post) => (
          <div key={post.id} className="rounded-lg border bg-card p-6 shadow-sm">
            <h2 className="mb-3 text-2xl font-semibold">{post.title}</h2>
            <p className="text-muted-foreground">{post.content}</p>
          </div>
        ))}
      </div>
      {blogPosts.length === 0 && (
        <div className="mt-10 flex flex-col items-center justify-center text-center">
          <h2 className="text-2xl font-semibold text-gray-700 dark:text-gray-300">暂无文章</h2>
          <p className="mt-2 text-gray-500 dark:text-gray-400">博客区域正在建设中，敬请期待更多精彩内容！</p>
        </div>
      )}
    </div>
  );
} 