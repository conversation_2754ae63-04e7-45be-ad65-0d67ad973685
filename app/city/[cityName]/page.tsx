import { Metadata } from "next";
import { urlToCityName } from "../../../lib/cityNameMapping";
import CityPageClient from "../../../components/CityPageClient";

// 使用增量静态再生(ISR)，每小时更新一次
export const revalidate = 3600; // 3600秒 = 1小时

interface CityPageProps {
	params: {
		cityName: string;
	};
}

export async function generateMetadata({ params }: CityPageProps): Promise<Metadata> {
	const cityName = urlToCityName[params.cityName] || decodeURIComponent(params.cityName);

	return {
		title: `${cityName}资源库 | 佳妮俱乐部 | ${cityName}高端约会、${cityName}商务模特、${cityName}外围、${cityName}福利姬、${cityName}上门、${cityName}探花`,
		description: `在${cityName}寻找高端约会对象。佳妮提供${cityName}地区的真人认证、高质量约会服务，包括商务模特、外围、福利姬、上门、探花、高端伴游、包养、同城约会、空降包夜等多样化服务。`,
		keywords: `${cityName}约会, ${cityName}商务模特, ${cityName}外围, ${cityName}福利姬, ${cityName}上门, ${cityName}探花, ${cityName}高端伴游, ${cityName}商务伴游, ${cityName}包养, ${cityName}伴游, ${cityName}同城约会, ${cityName}同城约炮, ${cityName}高端定制, ${cityName}高端外围, ${cityName}商务定制, ${cityName}外围模特, ${cityName}礼仪模特, ${cityName}外围资源, ${cityName}模特资源, ${cityName}约会交友, ${cityName}私人订制, ${cityName}外围女, ${cityName}兼职, ${cityName}空乘兼职, ${cityName}会所, ${cityName}模特会所, ${cityName}空降, ${cityName}包夜, ${cityName}网红兼职, ${cityName}网红模特, ${cityName}抖音网红, ${cityName}探花, ${cityName}金主, ${cityName}SPA, ${cityName}按摩, ${cityName}模特兼职, ${cityName}礼仪兼职, ${cityName}陪玩, ${cityName}酒店, ${cityName}酒店服务, ${cityName}品茶, ${cityName}小姐服务, ${cityName}小姐, ${cityName}找小姐, ${cityName}桑拿, ${cityName}嫩模, ${cityName}汗蒸, ${cityName}水疗, ${cityName}温泉, ${cityName}少妇, ${cityName}温泉酒店, ${cityName}学生妹兼职, ${cityName}老师兼职, ${cityName}空姐兼职, ${cityName}艺术生兼职, ${cityName}主持人兼职, ${cityName}健身教练兼职, ${cityName}演员兼职, ${cityName}夜店, ${cityName}极品约会, ${cityName}品茶交流, ${cityName}品茶上课, ${cityName}大保健服务, ${cityName}交友软件, ${cityName}上门服务, ${cityName}会所工作室, ${cityName}工作室, ${cityName}俱乐部, ${cityName}线下, ${cityName}线下约会, 高端约会, 真人认证, 线下约会`,
		alternates: {
			canonical: `https://www.jianier.com/city/${params.cityName}`
		},
		openGraph: {
			title: `${cityName}高端约会、上门、探花 | 佳妮真人线下服务`,
			description: `在${cityName}寻找高端约会对象。佳妮提供${cityName}地区的真人认证、高质量约会服务，包括商务模特、外围、福利姬、上门、探花、高端伴游、包养、同城约会、空降包夜等多样化服务。`,
			url: `https://www.jianier.com/city/${params.cityName}`,
			siteName: "佳妮高端约会",
			images: [
				{
					url: `https://www.jianier.com/${params.cityName}-og-image.jpg`,
					width: 1200,
					height: 630,
				},
			],
			locale: "zh_CN",
			type: "website",
		},
		twitter: {
			card: "summary_large_image",
			title: `${cityName}高端约会、上门、探花 | 佳妮真人线下服务`,
			description: `在${cityName}寻找高端约会对象。佳妮提供${cityName}地区的真人认证、高质量约会服务，包括商务模特、外围、福利姬、上门、探花、高端伴游、包养、同城约会、空降包夜等多样化服务。`,
			images: [`https://www.jianier.com/${params.cityName}-twitter-image.jpg`],
		},
	};
}

function generateLocalBusinessSchema(cityName: string) {
	const realCityName = urlToCityName[cityName] || decodeURIComponent(cityName);
	
	return {
		"@context": "https://schema.org",
		"@type": "LocalBusiness",
		"name": `佳妮俱乐部 ${realCityName}分部`,
		"description": `佳妮俱乐部在${realCityName}提供高端私密线下约会服务，包括商务模特、外围、福利姬、上门、探花、高端伴游、包养、同城约会等多样化服务。`,
		"url": `https://www.jianier.com/city/${cityName}`,
		"areaServed": {
			"@type": "City",
			"name": realCityName,
			"addressCountry": "CN"
		},
		"serviceArea": {
			"@type": "City", 
			"name": realCityName
		},
		"hasOfferingCatalog": {
			"@type": "OfferingCatalog",
			"name": "约会服务目录",
			"itemListElement": [
				{
					"@type": "Offer",
					"itemOffered": {
						"@type": "Service",
						"name": "高端约会服务",
						"description": "商务模特、外围、福利姬、上门、探花等高端约会服务"
					}
				},
				{
					"@type": "Offer", 
					"itemOffered": {
						"@type": "Service",
						"name": "伴游服务",
						"description": "高端伴游、商务伴游服务"
					}
				},
				{
					"@type": "Offer",
					"itemOffered": {
						"@type": "Service", 
						"name": "同城约会",
						"description": "同城约会、包养等个性化服务"
					}
				}
			]
		},
		"contactPoint": {
			"@type": "ContactPoint",
			"contactType": "客服咨询",
			"url": "https://t.me/Jianierbot"
		},
		"parentOrganization": {
			"@type": "Organization",
			"name": "佳妮俱乐部"
		}
	};
}

export default function CityPage({ params }: CityPageProps) {
	console.log(`城市页面加载: ${params.cityName}`);
	const localBusinessSchema = generateLocalBusinessSchema(params.cityName);
	
	return (
		<>
			<script
				type="application/ld+json"
				dangerouslySetInnerHTML={{
					__html: JSON.stringify(localBusinessSchema),
				}}
			/>
			<CityPageClient cityName={params.cityName} />
		</>
	);
}
