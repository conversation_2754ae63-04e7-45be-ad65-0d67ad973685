"use client";

import CityLayoutClient from "../../components/CityLayoutClient";
import { useSidebarContext } from "../../contexts/SidebarContext";
import { useEffect, useState } from "react";

export default function CityLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isSidebarOpen, setIsSidebarOpen } = useSidebarContext();
  const [isClientMounted, setIsClientMounted] = useState(false);
  
  useEffect(() => {
    setIsClientMounted(true);
  }, []);

  return (
    <CityLayoutClient 
      isSidebarOpen={isSidebarOpen} 
      setIsSidebarOpen={setIsSidebarOpen}
    >
      {children}
    </CityLayoutClient>
  );
}
