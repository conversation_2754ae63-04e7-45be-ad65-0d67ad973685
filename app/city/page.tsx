import { Metadata } from "next";
import { CityGrid } from "../../components/CityGrid";
import { MobileCityPage } from "../../components/MobileCityPage";
import { ExternalLink, Star, BarChart3, Sparkles } from "lucide-react";
import { ResourceCards } from "../../components/ResourceCards";
import { HotRecommendations } from "../../components/HotRecommendations";

export const metadata: Metadata = {
  title: "城市导航 | 佳妮俱乐部 | 高端约会、商务模特、外围、福利姬、上门、探花",
  description: "佳妮俱乐部城市导航中心，探索全国各大城市高端约会、商务模特、外围、福利姬、上门、探花资源库。包含北京、上海、广州、深圳等一二线城市，提供高端伴游、包养、同城约会等私密服务。",
  keywords: "城市导航, 资源库, 高端约会, 商务模特, 外围, 福利姬, 上门, 探花",
};

export const dynamic = 'force-dynamic';

export default async function CityPage() {
  return (
    <>
      {/* 移动端视图 - 仅在小屏幕显示 */}
      <div className="md:hidden">
        <MobileCityPage />
      </div>

      {/* PC端视图 - 仅在中等及以上屏幕显示 */}
      <div className="hidden md:block w-full max-w-full mx-auto px-4 py-6 overflow-x-hidden">
        {/* 极品资源推荐 */}
        <section className="mb-12">
          <ResourceCards variant="desktop" />
        </section>

        {/* 城市目录 */}
        <section className="mb-8">
          <h2 className="text-lg font-medium mb-4 flex items-center gap-2">
            <span className="text-xl mr-1">🌆</span> 城市目录
          </h2>
          <CityGrid />
        </section>
        
        {/* 热门推荐 */}
        <section className="mb-8">
          <HotRecommendations variant="desktop" />
        </section>
      </div>
    </>
  );
}
