@tailwind base;
@tailwind components;
@tailwind utilities;

/* 防止滚动条导致的布局偏移 */
html {
	overflow-y: scroll;
	padding-left: calc(100vw - 100%);
}

body {
	width: 100%;
	overflow-x: hidden;
	max-width: 100%;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
	width: 10px;
	height: 10px;
}

::-webkit-scrollbar-track {
	background: #1a1a1a;
	border-radius: 4px;
}

::-webkit-scrollbar-thumb {
	background: #333;
	border-radius: 4px;
	border: 2px solid #1a1a1a;
}

::-webkit-scrollbar-thumb:hover {
	background: #444;
}

/* Firefox */
* {
	scrollbar-width: thin;
	scrollbar-color: #333 #1a1a1a;
}

.my-masonry-grid {
	display: flex;
	margin-left: -30px; /* gutter size offset */
	width: auto;
}
.my-masonry-grid_column {
	padding-left: 30px; /* gutter size */
	background-clip: padding-box;
}

@layer base {
	:root {
		--background: 0 0% 100%;
		--foreground: 0 0% 3.9%;

		--muted: 0 0% 96.1%;
		--muted-foreground: 0 0% 45.1%;

		--popover: 0 0% 100%;
		--popover-foreground: 0 0% 3.9%;

		--card: 0 0% 100%;
		--card-foreground: 0 0% 3.9%;

		--border: 0 0% 89.8%;
		--input: 0 0% 89.8%;

		--primary: 0 0% 9%;
		--primary-foreground: 0 0% 98%;

		--secondary: 0 0% 96.1%;
		--secondary-foreground: 0 0% 9%;

		--accent: 0 0% 96.1%;
		--accent-foreground: 0 0% 9%;

		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 0 0% 98%;

		--ring: 0 0% 3.9%;

		--radius: 0.5rem;

		--chart-1: 12 76% 61%;

		--chart-2: 173 58% 39%;

		--chart-3: 197 37% 24%;

		--chart-4: 43 74% 66%;

		--chart-5: 27 87% 67%;
	}

	.dark {
		--background: 0 0% 3.9%;
		--foreground: 0 0% 98%;

		--muted: 0 0% 14.9%;
		--muted-foreground: 0 0% 63.9%;

		--popover: 0 0% 3.9%;
		--popover-foreground: 0 0% 98%;

		--card: 0 0% 3.9%;
		--card-foreground: 0 0% 98%;

		--border: 0 0% 14.9%;
		--input: 0 0% 14.9%;

		--primary: 0 0% 98%;
		--primary-foreground: 0 0% 9%;

		--secondary: 0 0% 14.9%;
		--secondary-foreground: 0 0% 98%;

		--accent: 0 0% 14.9%;
		--accent-foreground: 0 0% 98%;

		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 0 0% 98%;

		--ring: 0 0% 83.1%;

		--chart-1: 220 70% 50%;

		--chart-2: 160 60% 45%;

		--chart-3: 30 80% 55%;

		--chart-4: 280 65% 60%;

		--chart-5: 340 75% 55%;
	}
}

@layer base {
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground;
	}
}

@layer utilities {
	.animate-accordion-down {
		animation: accordion-down 0.2s ease-out;
	}

	.animate-accordion-up {
		animation: accordion-up 0.2s ease-out;
	}

	/* 呼吸加载动画 */
	.animate-breath {
		animation: breathe 4s infinite ease-in-out;
	}

	@keyframes breathe {
		0%,
		100% {
			opacity: 1;
			transform: scale(1);
		}
		50% {
			opacity: 0.7;
			transform: scale(1.05);
		}
	}
}

@keyframes accordion-down {
	from {
		height: 0;
	}
	to {
		height: var(--radix-accordion-content-height);
	}
}

@keyframes accordion-up {
	from {
		height: var(--radix-accordion-content-height);
	}
	to {
		height: 0;
	}
}
