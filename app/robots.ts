import { MetadataRoute } from 'next';

export default function robots(): MetadataRoute.Robots {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://jianier.com';

  return {
    rules: [
      {
        userAgent: '*',
        allow: [
          '/',
          '/city',
          '/city/*',
          '/girl/*',
          '/guide',
          '/discover',
          '/jianier5kw',
        ],
        disallow: [
          '/api/*',
          '/admin/*',
          '/dashboard/*',
          '/profile/*',
          '/vip/*',
          '/login',
          '/register',
          '/auth/*',
          '/_next/*',
          '/static/*',
        ],
      },
      {
        userAgent: 'Googlebot',
        allow: [
          '/',
          '/city',
          '/city/*',
          '/girl/*',
          '/guide',
          '/discover',
          '/jianier5kw',
        ],
        disallow: [
          '/api/*',
          '/admin/*',
          '/dashboard/*',
          '/profile/*',
          '/vip/*',
          '/login',
          '/register',
          '/auth/*',
        ],
      },
    ],
    sitemap: `${baseUrl}/sitemap.xml`,
    host: baseUrl,
  };
}