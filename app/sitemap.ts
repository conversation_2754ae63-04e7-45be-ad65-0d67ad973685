import { MetadataRoute } from 'next';
import { getGir<PERSON>, <PERSON> } from '@/lib/api';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://jianier.com';

  // 静态页面
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/city`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/guide`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/vip`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.6,
    },
    {
      url: `${baseUrl}/profile`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.5,
    },
    {
      url: `${baseUrl}/discover`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/jianier5kw`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.8,
    },
  ];

  // 主要城市页面 - 这些是热门城市，优先级较高
  const majorCities = [
    '北京', '上海', '广州', '深圳', '杭州', '南京', '苏州', '成都', 
    '重庆', '武汉', '西安', '天津', '长沙', '郑州', '青岛', '大连',
    '厦门', '宁波', '无锡', '福州', '合肥', '昆明', '哈尔滨', '长春',
    '沈阳', '石家庄', '太原', '济南', '南昌', '贵阳', '兰州', '银川',
    '西宁', '呼和浩特', '乌鲁木齐', '拉萨', '海口', '三亚', '珠海',
    '汕头', '佛山', '东莞', '中山', '江门', '湛江', '茂名', '肇庆',
    '惠州', '梅州', '汕尾', '河源', '阳江', '清远', '韶关', '潮州',
    '揭阳', '云浮'
  ];

  const cityPages = majorCities.map(city => ({
    url: `${baseUrl}/city/${encodeURIComponent(city)}`,
    lastModified: new Date(),
    changeFrequency: 'daily' as const,
    priority: 0.8,
  }));

  // 获取动态女孩页面
  const girlPages = await generateGirlPages(baseUrl);

  return [
    ...staticPages,
    ...cityPages,
    ...girlPages,
  ];
}

// 从数据库获取女孩页面 - 优化版本
async function generateGirlPages(baseUrl: string) {
  try {
    // 限制sitemap中的女孩页面数量，避免影响SEO效果
    const MAX_GIRL_PAGES = 5000; // Google建议单个sitemap不超过50,000个URL
    const PRIORITY_LIMIT = 1000; // 优先包含最新/最热门的1000个
    
    // 分两步：1) 获取优先页面 2) 获取其他页面
    const allGirls: Girl[] = [];
    
    // 第一步：获取优先页面（最新创建或更新的）
    console.log('Fetching priority girl pages for sitemap...');
    const priorityResponse = await getGirls(1, PRIORITY_LIMIT, {
      city: '',
      district: '',
    });
    
    allGirls.push(...priorityResponse.girls);
    
    // 第二步：如果还没达到上限，继续获取其他页面
    let currentPage = 2;
    const remainingSlots = MAX_GIRL_PAGES - allGirls.length;
    
    if (remainingSlots > 0) {
      console.log(`Fetching additional ${remainingSlots} girl pages...`);
      
      while (allGirls.length < MAX_GIRL_PAGES) {
        const perPage = Math.min(100, remainingSlots);
        const response = await getGirls(currentPage, perPage, {
          city: '',
          district: '',
        });
        
        if (response.girls.length === 0) {
          break; // 没有更多数据了
        }
        
        // 过滤重复的页面（避免同一个girl_id出现多次）
        const newGirls = response.girls.filter(
          girl => !allGirls.some(existing => existing.girl_id === girl.girl_id)
        );
        
        allGirls.push(...newGirls.slice(0, MAX_GIRL_PAGES - allGirls.length));
        
        if (response.girls.length < perPage || allGirls.length >= MAX_GIRL_PAGES) {
          break;
        }
        
        currentPage++;
        
        // 安全限制
        if (currentPage > 50) {
          console.warn('Sitemap generation stopped at page 50 to prevent timeout');
          break;
        }
      }
    }
    
    console.log(`Generated sitemap for ${allGirls.length} girls (max: ${MAX_GIRL_PAGES})`);
    
    // 按更新时间排序，最新的优先级更高
    const sortedGirls = allGirls.sort((a, b) => {
      const dateA = new Date(a.updatedAt || a.createdAt || '1970-01-01');
      const dateB = new Date(b.updatedAt || b.createdAt || '1970-01-01');
      return dateB.getTime() - dateA.getTime();
    });
    
    return sortedGirls.map((girl, index) => ({
      url: `${baseUrl}/girl/${girl.girl_id}`,
      lastModified: new Date(girl.updatedAt || girl.createdAt || new Date()),
      changeFrequency: 'weekly' as const,
      // 最新的1000个给更高优先级
      priority: index < PRIORITY_LIMIT ? 0.8 : 0.6,
    }));
    
  } catch (error) {
    console.error('Error generating girl pages for sitemap:', error);
    // 即使出错也返回空数组，不影响其他页面的sitemap生成
    return [];
  }
}