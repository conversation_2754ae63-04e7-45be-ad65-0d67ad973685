"use client";

import Link from "next/link";
import { motion } from "framer-motion";

export default function LinkTreeContent() {
  const mainLinks = [
    {
      title: "实时更新 ❤️‍🔥 资源库",
      href: "/city",
      isImage: true,
      imageSrc: "/images/zzk.png",
      description: "浏览全国各地优质约会资源"
    },
    {
      title: "🧭 约会指南",
      href: "/guide",
      description: "了解约会流程和注意事项"
    },
    {
      title: "💫 会员反馈",
      href: "/feedback",
      description: "查看真实会员约会体验反馈"
    },
    {
      title: "☀️ 开通VIP",
      href: "/vip",
      description: "开通佳妮俱乐部VIP"
    },
    {
      title: "👑 官网",
      href: "/",
      description: "返回佳妮俱乐部官网"
    }
  ];

  const telegramLinks = [
    {
      title: "✈️ Telegram 电报群",
      href: "https://t.me/jianierclub",
      description: "加入我们的电报群，获取最新资讯"
    },
    {
      title: "✈️ Telegram 客服",
      href: "https://t.me/jianierbot",
      description: "联系我们的客服，获取帮助"
    }
  ];

  return (
    <div className="min-h-screen bg-dark-background pt-12 pb-12">
      {/* Animated Background */}
      <div className="fixed inset-0 w-full h-full">
        <div className="absolute inset-0 bg-gradient-to-b from-background to-background">
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,rgba(229,0,73,0.15),rgba(229,0,73,0)_50%)]"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_bottom_left,rgba(229,0,73,0.1),rgba(229,0,73,0)_50%)]"></div>
        </div>
        <div className="absolute inset-0 w-full h-full opacity-30">
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(229,0,73,0.05)_1px,transparent_1px)] bg-[size:32px_32px]"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(229,0,73,0.05)_1px,transparent_1px)] bg-[size:24px_24px] rotate-15"></div>
        </div>
      </div>

      {/* Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="relative container mx-auto max-w-[500px] px-4"
      >
        {/* Floating Elements */}
        <div className="absolute -top-20 -left-20 w-60 h-60 bg-[#e50049]/5 rounded-full filter blur-3xl animate-pulse"></div>
        <div className="absolute -top-20 -right-20 w-60 h-60 bg-[#e50049]/5 rounded-full filter blur-3xl animate-pulse delay-1000"></div>

        {/* Logo and Title */}
        <div className="w-full flex flex-col items-center bg-[#1a1a1a]/30 backdrop-blur-sm rounded-3xl p-4 sm:p-6 mb-4">
          <div className="text-center mb-4">
            <h2 className="text-[32px] font-bold tracking-tight mb-2">
              <span className="relative inline-block">
                <span className="absolute -inset-2 bg-gradient-to-r from-[#e50049]/20 to-[#ff1464]/20 blur-xl"></span>
                <span className="relative z-10 inline-block text-white">佳妮 ❤️‍🔥 导航</span>
              </span>
            </h2>
            <p className="text-white/80 text-sm px-4">
              本俱乐部旨在为高素质的客人，提供高端私密线下约会服务。主营全国一二线省会城市，拥有10000+优质模特资源。
            </p>
          </div>

          {/* Social Links */}
          <div className="flex justify-center gap-6 mb-2">
            <Link 
              href="https://x.com/JointheJlub" 
              target="_blank"
              className="w-10 h-10 flex items-center justify-center bg-[#1a1a1a] rounded-full hover:bg-[#292929] hover:scale-110 transition-all duration-300 hover:shadow-lg hover:shadow-[#e50049]/20"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 24 24" className="text-white">
                <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
              </svg>
            </Link>
            <Link 
              href="https://t.me/jianierclub" 
              target="_blank"
              className="w-10 h-10 flex items-center justify-center bg-[#1a1a1a] rounded-full hover:bg-[#292929] hover:scale-110 transition-all duration-300 hover:shadow-lg hover:shadow-[#0088cc]/20"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 24 24" className="text-[#0088cc]">
                <path d="M9.78 18.65l.28-4.23 7.68-6.92c.34-.31-.07-.46-.52-.19L7.74 13.3 3.64 12c-.88-.25-.89-.86.2-1.3l15.97-6.16c.73-.33 1.43.18 1.15 1.3l-2.72 12.81c-.19.91-.74 1.13-1.5.71L12.6 16.3l-1.99 1.93c-.23.23-.42.42-.83.42z"/>
              </svg>
            </Link>
          </div>
        </div>

        {/* Main Links */}
        <div className="w-full space-y-3">
          {mainLinks.map((link, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Link
                href={link.href}
                className="block w-full bg-[#1a1a1a]/30 backdrop-blur-sm rounded-2xl hover:bg-[#1a1a1a]/50 transition-all duration-300 overflow-hidden group"
              >
                {link.isImage ? (
                  <div>
                    <div className="relative">
                      <img src={link.imageSrc} alt={link.title} className="w-full h-48 object-cover transform group-hover:scale-105 transition-transform duration-500" />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                    </div>
                    <div className="p-4">
                      <h3 className="text-lg font-semibold text-white group-hover:text-[#e50049] transition-colors duration-300 mb-1">{link.title}</h3>
                      <p className="text-sm text-white/60 group-hover:text-white/80 transition-colors duration-300">{link.description}</p>
                    </div>
                  </div>
                ) : (
                  <div className="p-4">
                    <h3 className="text-lg font-semibold text-white group-hover:text-[#e50049] transition-colors duration-300 mb-1">{link.title}</h3>
                    <p className="text-sm text-white/60 group-hover:text-white/80 transition-colors duration-300">{link.description}</p>
                  </div>
                )}
              </Link>
            </motion.div>
          ))}
        </div>

        {/* Telegram Links */}
        <div className="w-full space-y-3 mt-3">
          {telegramLinks.map((link, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: (mainLinks.length + index) * 0.1 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Link
                href={link.href}
                target="_blank"
                className="block w-full bg-[#1a1a1a]/30 backdrop-blur-sm rounded-2xl hover:bg-[#1a1a1a]/50 transition-all duration-300 p-4 group"
              >
                <h3 className="text-lg font-semibold text-white group-hover:text-[#0088cc] transition-colors duration-300 mb-1">{link.title}</h3>
                <p className="text-sm text-white/60 group-hover:text-white/80 transition-colors duration-300">{link.description}</p>
              </Link>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  );
} 