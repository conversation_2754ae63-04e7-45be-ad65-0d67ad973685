"use client";

import { ChevronRight } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import Intercom, { show } from "@intercom/messenger-js-sdk";
import { useSession, signOut } from "next-auth/react";
import { useEffect, useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";

// 定义菜单项类型
type MenuItemWithPath = {
  label: string;
  path: string;
  description?: string;
  isExternal?: boolean;
  onClick?: () => void;
  icon?: React.ReactNode;
  badge?: string;
} & (
  | { emoji: string; iconPath?: never }
  | { emoji?: never; iconPath: string }
);

type MenuItemWithAction = {
  label: string;
  path?: never;
  description?: string;
  isExternal?: boolean;
  onClick: () => void;
  icon?: React.ReactNode;
  badge?: string;
} & (
  | { emoji: string; iconPath?: never }
  | { emoji?: never; iconPath: string }
);

type MenuItem = MenuItemWithPath | MenuItemWithAction;

const profileItem: MenuItemWithPath = {
  emoji: "👤",
  label: "我的资料",
  description: "查看并编辑个人信息",
  path: "/profile",
};

// 丰富菜单内容
const otherMenuItems: MenuItem[] = [
  {
    emoji: "🧭",
    label: "约会指南",
    description: "新手必看的约会技巧",
    path: "/guide",
  },
  {
    emoji: "👑",
    label: "开通VIP",
    description: "解锁所有高级功能",
    path: "/vip",
  }
];

export default function DiscoverPage() {
  // 获取用户登录状态
  const { data: session } = useSession();
  const [mounted, setMounted] = useState(false);
  const router = useRouter();

  useEffect(() => {
    setMounted(true);
  }, []);

  // 处理登出
  const handleSignOut = () => {
    signOut({ callbackUrl: '/' });
  };

  // 处理在线客服点击
  const handleCustomerService = () => {
    if (session) {
      // 已登录，打开客服对话
      show();
    } else {
      // 未登录，显示提示并跳转到登录页面
      toast.error("请登录后联系在线客服");
      router.push("/login");
    }
  };

  // 联系我们菜单项
  const contactItems: MenuItem[] = [
    {
      emoji: "✈️",
      label: "电报群",
      description: "加入官方社区",
      path: "https://t.me/jianierclub",
      isExternal: true,
    },
    {
      emoji: "✈️",
      label: "电报客服",
      description: "一对一专属服务",
      path: "https://t.me/jianierbot",
      isExternal: true,
    },
    {
      iconPath: "/icons/kefu.jpg",
      label: "在线客服",
      description: "随时解答您的问题",
      onClick: handleCustomerService,
    }
  ];

  if (!mounted) {
    return null; // 避免客户端/服务器渲染不匹配
  }

  // 渲染菜单按钮/链接的通用函数
  const renderMenuItem = (item: MenuItem, index: number) => {
    const isAction = 'onClick' in item && !!item.onClick;
    const isLink = 'path' in item && !!item.path;

    const content = (
      <div className="flex items-center group">
        <div className="w-9 h-9 rounded-full bg-gradient-to-br from-[#FFB800]/10 to-[#FF8A00]/10 flex items-center justify-center group-hover:from-[#FFB800]/20 group-hover:to-[#FF8A00]/20 transition-all duration-300">
          {item.icon || (
            <span className="text-lg relative flex items-center justify-center w-full h-full">
              {item.iconPath ? (
                <div className="relative w-full h-full flex items-center justify-center">
                  <Image
                    src={item.iconPath}
                    alt={item.label}
                    width={30}
                    height={30}
                    className="rounded-full object-cover w-[29px] h-[29px]"
                  />
                  {item.label === "在线客服" && (
                    <div className="absolute w-2 h-2 bottom-[1px] right-[1px]">
                      <span className="absolute inset-0 rounded-full bg-green-500"></span>
                    </div>
                  )}
                </div>
              ) : item.emoji}
            </span>
          )}
        </div>
        <div className="ml-4 flex-1">
          <div className="flex items-center">
            <span className="text-sm font-medium text-white group-hover:text-[#FFB800] transition-colors duration-300">{item.label}</span>
            {item.badge && (
              <span className="ml-2 px-2 py-0.5 text-[10px] font-bold bg-[#E11D48] text-white rounded-full">
                {item.badge}
              </span>
            )}
          </div>
          {item.description && (
            <p className="text-xs text-gray-400 mt-0.5 text-left w-full">{item.description}</p>
          )}
        </div>
        <ChevronRight className="w-4 h-4 text-gray-500 group-hover:text-[#FFB800] group-hover:translate-x-0.5 transition-all duration-300" />
      </div>
    );

    const className = "w-full py-2 px-4 bg-[#121212] rounded-lg mb-2 border border-gray-800 hover:border-[#FFB800]/30 transition-all duration-300 active:scale-[0.98] transform relative group";

    if (isAction) {
      return (
        <button
          key={index}
          onClick={(item as MenuItemWithAction).onClick}
          className={className}
        >
          <div className="absolute -inset-0.5 bg-gradient-to-r from-[#FFB800]/20 to-[#FF8A00]/20 rounded-lg blur opacity-0 group-hover:opacity-100 transition duration-500"></div>
          <div className="relative">{content}</div>
        </button>
      );
    }

    if (isLink) {
      const linkItem = item as MenuItemWithPath;
      return (
        <div key={linkItem.path}>
          <Link
            href={linkItem.path}
            target={linkItem.isExternal ? "_blank" : undefined}
            className={`block ${className}`}
          >
            <div className="absolute -inset-0.5 bg-gradient-to-r from-[#FFB800]/20 to-[#FF8A00]/20 rounded-lg blur opacity-0 group-hover:opacity-100 transition duration-500"></div>
            <div className="relative">{content}</div>
          </Link>
        </div>
      );
    }

    return null;
  };

  return (
    <div className="min-h-screen bg-[#121212] pb-20">
      <div className="max-w-xl mx-auto px-4 pt-6">
        <div className="space-y-1">
          {/* 个人中心 */}
          <Card className="border-0 bg-[#121212] border-gray-800">
            <CardHeader className="pb-1 pt-3 px-4">
              <CardTitle className="text-sm bg-gradient-to-r from-[#FFB800] to-[#FF8A00] bg-clip-text text-transparent">
                个人中心
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0 pb-2 px-4">
              {renderMenuItem(profileItem, 0)}
            </CardContent>
          </Card>

          {/* 功能中心 */}
          <Card className="border-0 bg-[#121212] border-gray-800">
            <CardHeader className="pb-1 pt-3 px-4">
              <CardTitle className="text-sm bg-gradient-to-r from-[#FFB800] to-[#FF8A00] bg-clip-text text-transparent">
                功能中心
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0 pb-2 px-4">
              {otherMenuItems.map((item, index) => renderMenuItem(item, index))}
            </CardContent>
          </Card>

          {/* 联系我们 */}
          <Card className="border-0 bg-[#121212] border-gray-800">
            <CardHeader className="pb-1 pt-3 px-4">
              <CardTitle className="text-sm bg-gradient-to-r from-[#FFB800] to-[#FF8A00] bg-clip-text text-transparent">
                联系我们
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0 pb-2 px-4">
              {contactItems.map((item, index) => renderMenuItem(item, index))}
            </CardContent>
          </Card>

          {/* 登录/登出区域 */}
          <Card className="border-0 bg-[#121212] border-gray-800">
            <CardContent className="py-4 px-4">
              {session ? (
                <button
                  onClick={handleSignOut}
                  className="w-full h-10 bg-gradient-to-r from-[#E11D48] to-[#C01441] hover:from-[#CF1940] hover:to-[#B01038] text-white font-bold rounded-lg shadow-lg shadow-[#E11D48]/20 hover:shadow-xl hover:shadow-[#E11D48]/25 focus:ring-2 focus:ring-offset-2 focus:ring-[#E11D48] transform transition-all duration-300 hover:scale-[1.02] active:scale-[0.98]"
                >
                  🔒 退出登录
                </button>
              ) : (
                <Link
                  href="/login"
                  className="block w-full h-10 bg-gradient-to-r from-[#E11D48] to-[#C01441] hover:from-[#CF1940] hover:to-[#B01038] text-white font-bold rounded-lg shadow-lg shadow-[#E11D48]/20 hover:shadow-xl hover:shadow-[#E11D48]/25 focus:ring-2 focus:ring-offset-2 focus:ring-[#E11D48] transform transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] flex items-center justify-center"
                >
                  🔑 登录账户
                </Link>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 网页版权信息 */}
        <div className="text-center text-xs text-gray-500 mt-4 pb-6">
          © Jianier Club · All Rights Reserved
        </div>
      </div>
    </div>
  );
}