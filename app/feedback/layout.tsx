"use client";

import CityLayoutClient from "../../components/CityLayoutClient";
import { useSidebarContext } from "../../contexts/SidebarContext";

export default function FeedbackLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isSidebarOpen, setIsSidebarOpen } = useSidebarContext();

  return (
    <CityLayoutClient 
      isSidebarOpen={isSidebarOpen} 
      setIsSidebarOpen={setIsSidebarOpen}
    >
      {children}
    </CityLayoutClient>
  );
} 