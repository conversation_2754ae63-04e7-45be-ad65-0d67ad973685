import { Metadata } from "next";
import FeedbackPageClient from "../../components/FeedbackPageClient";
import { getFeedbackData } from "../../lib/api";

export const metadata: Metadata = {
  title: "至真园™ | 会员真实评价反馈 | 高端约会、商务模特、外围、福利姬、上门、探花",
  description: "至真园真实用户评价反馈中心，查看高端约会、商务模特、外围、福利姬、上门、探花服务的真实体验评价。包含高端伴游、包养、同城约会等服务评价，帮助选择最适合的私密服务。",
};

export const dynamic = 'force-dynamic';

// 设置页面重新验证时间为60秒
export const revalidate = 60;

export default async function FeedbackPage() {
  const data = await getFeedbackData(1, 20);
  return <FeedbackPageClient initialData={data} />;
} 