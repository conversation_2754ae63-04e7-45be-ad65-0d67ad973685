import { Metadata } from "next";
import HomeClient from "./components/HomeClient";

export const metadata: Metadata = {
  title: "佳妮俱乐部 | 全网最强高端约会、商务模特、外围、福利姬、上门、探花俱乐部",
  description: "佳妮俱乐部专注提供高端私密线下约会服务，覆盖全国一二线城市。累计更新数千优质模特资源，包括外围模特、商务模特、网红模特、福利姬、上门、探花、礼仪模特、空乘兼职等。提供包养、高端伴游、商务伴游、同城约会、空降、包夜等多样化服务，灵活预约系统。",
  keywords: "高端约会, 线下约会, 商务模特, 外围模特, 外围, 网红模特, 福利姬, 上门, 探花, 私密约会, 高端伴游, 商务伴游, 包养, 伴游, 同城约会, 同城约炮, 高端定制, 高端外围, 商务定制, 外围模特, 礼仪模特, 外围资源, 模特资源, 约会交友, 私人订制, 外围女, 外围经济, 商务模特经济, 兼职, 高校兼职, 素人兼职, 空乘兼职, 会所, 模特会所, 空降, 包夜, 网红兼职, 网红模特, 抖音网红, 外围微信, 探花, 金主, SPA, Spa, 按摩, 二奶, 反差婊, 黑料, 裸聊, 模特兼职, 礼仪兼职, 陪玩, 陪玩平台, 酒店, 酒店服务, 品茶, 小姐服务, 小姐, 找小姐, 桑拿, 嫩模, 汗蒸, 水疗, 温泉, 少妇, 温泉酒店, 学生妹兼职, 老师兼职, 空姐兼职, 艺术生兼职, 主持人兼职, 健身教练兼职, 演员兼职, 夜店, 极品约会, 品茶交流, 品茶上课, 大保健服务, 交友软件, 上门服务, 会所工作室, 工作室, 俱乐部, 线下, 线下约会",
  alternates: {
    canonical: "https://www.jianier.club"
  },
  openGraph: {
    title: "佳妮俱乐部 - 全网最强高端约会、商务模特、外围、福利姬、上门、探花俱乐部",
    description: "专业提供高端私密线下约会服务，覆盖全国各大城市。外围、福利姬、商务模特、上门、探花、礼仪模特资源丰富，提供包养、高端伴游、同城约会、空降包夜等服务，真人认证，一对一定制。",
    url: "https://www.jianier.club",
    siteName: "佳妮俱乐部",
    images: [
      {
        url: "https://www.jianier.com/og-image.jpg",
        width: 1200,
        height: 630,
      },
    ],
    locale: "zh_CN",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "全网最强高端约会、商务模特、外围、福利姬、上门、探花俱乐部",
    description: "专业提供高端私密线下约会服务，覆盖全国各大城市。外围、福利姬、商务模特、上门、探花、礼仪模特资源丰富，提供包养、高端伴游、同城约会、空降包夜等服务，真人认证，一对一定制。",
    images: ["https://www.jianier.com/twitter-image.jpg"],
  },
};

function generateOrganizationSchema() {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "佳妮俱乐部",
    "alternateName": "JianierClub",
    "url": "https://www.jianier.club",
    "logo": "https://www.jianier.com/logo.png",
    "description": "佳妮俱乐部专注提供高端私密线下约会服务，覆盖全国一二线城市。累计更新数千优质模特资源，包括外围模特、商务模特、网红模特、福利姬、上门、探花、礼仪模特、空乘兼职等。",
    "foundingDate": "2023",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "CN",
      "addressRegion": "全国"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "客服咨询",
      "url": "https://t.me/Jianierbot"
    },
    "sameAs": [
      "https://t.me/Jianierbot"
    ],
    "serviceArea": {
      "@type": "Place",
      "name": "中国一二线城市"
    }
  };
}

function generateWebsiteSchema() {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "佳妮俱乐部",
    "url": "https://www.jianier.club",
    "description": "高端线下约会服务平台",
    "inLanguage": "zh-CN",
    "publisher": {
      "@type": "Organization",
      "name": "佳妮俱乐部"
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": "https://www.jianier.com/city?search={search_term_string}"
      },
      "query-input": "required name=search_term_string"
    }
  };
}

export default function Home() {
  const organizationSchema = generateOrganizationSchema();
  const websiteSchema = generateWebsiteSchema();

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema),
        }}
      />
      <HomeClient />
    </>
  );
}