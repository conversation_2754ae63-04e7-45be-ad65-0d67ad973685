import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { NextAuthProvider } from './providers'
import ClientLayout from '../components/ClientLayout'
import { GoogleAnalytics } from "@next/third-parties/google";
import { Toaster } from "react-hot-toast";
import { SidebarProvider } from '../contexts/SidebarContext'
import ClarityAnalytics from '../components/ClarityAnalytics'
import PlausibleProvider from 'next-plausible'
import DisableDevtool from '../components/DisableDevtool'
import { MobileLayout } from '../components/mobile/MobileLayout'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: '佳妮俱乐部 | 高端约会、商务模特、外围、福利姬、上门、探花',
  description: '佳妮俱乐部专业提供高端约会、商务模特、外围、福利姬、上门、探花等私密线下约会服务。涵盖全国一二线城市，提供高端伴游、包养、同城约会、空降包夜等多样化服务，真人认证，一对一定制。',
  manifest: '/manifest.json',
  themeColor: '#000000',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: '佳妮俱乐部',
  },
  viewport: 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh" className="dark">
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="apple-touch-icon" href="/favicon.ico" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <PlausibleProvider domain="jianier.com" />
      </head>
      <body className={`${inter.className} bg-dark-background text-foreground min-h-screen antialiased`}>
        <NextAuthProvider>
          <SidebarProvider>
            <MobileLayout>
              <ClientLayout>{children}</ClientLayout>
            </MobileLayout>
          </SidebarProvider>
        </NextAuthProvider>
        <GoogleAnalytics gaId="G-1LDR1Z48WV" />
        <ClarityAnalytics />
        <Toaster />
        <DisableDevtool />
      </body>
    </html>
  )
}
