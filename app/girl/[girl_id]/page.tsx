import { getGirlById } from "../../../lib/api";
import GirlDetail from "../../../components/GirlDetail";
import { Metadata, ResolvingMetadata } from "next";
import Link from "next/link";
import Breadcrumb from "../../../components/Breadcrumb";

// 使用增量静态再生(ISR)，每30分钟更新一次
export const revalidate = 1800; // 1800秒 = 30分钟

type Props = {
	params: { girl_id: string };
};

export async function generateMetadata({ params }: Props, parent: ResolvingMetadata): Promise<Metadata> {
	const girlData = await getGirlById(params.girl_id);

	if (!girlData) {
		return {
			title: "未找到女孩信息",
		};
	}

	const title = `${girlData.title} | ${girlData.city} ${girlData.price} | 佳妮俱乐部 |  ${girlData.city}高端约会、 ${girlData.city}商务模特、 ${girlData.city}外围、 ${girlData.city}福利姬、 ${girlData.city}上门、 ${girlData.city}探花`;
	const description = girlData.description || `${girlData.city}${girlData.price}高端约会女孩${girlData.title}的详细资料。专业提供商务模特、外围、福利姬、上门、探花等私密线下约会服务，真人认证，支持高端伴游、包养、同城约会、空降包夜等个性化服务。`;
	
	// 获取第一张合法的图片
	const firstImage = girlData.media.find(url => url.match(/\.(jpeg|jpg|gif|png)$/)) || "/default-og-image.jpg";

	const fixedKeywords = ["商务模特", "外围", "福利姬", "上门", "探花", "高端约会", "真人认证", "线下交友", "伴游", "高端伴游", "商务伴游", "包养", "同城约会", "同城约炮", "高端定制", "高端外围", "商务定制", "外围模特", "礼仪模特", "外围资源", "模特资源", "约会交友", "私人订制", "外围女", "兼职", "空乘兼职", "会所", "模特会所", "空降", "包夜", "网红兼职", "网红模特", "抖音网红", "探花", "金主", "SPA", "按摩", "模特兼职", "礼仪兼职", "陪玩", "酒店", "酒店服务", "品茶", "小姐服务", "小姐", "找小姐", "桑拿", "嫩模", "汗蒸", "水疗", "温泉", "少妇", "温泉酒店", "学生妹兼职", "老师兼职", "空姐兼职", "艺术生兼职", "主持人兼职", "健身教练兼职", "演员兼职", "夜店", "极品约会", "品茶交流", "品茶上课", "大保健服务", "交友软件", "上门服务", "会所工作室", "工作室", "俱乐部", "线下", "线下约会", `${girlData.city}约会`, `${girlData.city}女孩`, `${girlData.city}商务模特`, `${girlData.city}外围`, `${girlData.city}伴游`, `${girlData.city}包养`, `${girlData.city}同城约会`];
	const keywords = [girlData.title, girlData.city, girlData.price, ...girlData.type, ...fixedKeywords].join(", ");

	return {
		title,
		description,
		keywords,
		alternates: {
			canonical: `https://www.jianier.com/girl/${params.girl_id}`
		},
		openGraph: {
			title,
			description,
			images: [firstImage],
			type: "profile",
		},
		twitter: {
			card: "summary_large_image",
			title,
			description,
			images: [firstImage],
		},
	};
}

// 生成结构化数据
function generateStructuredData(girlData: any) {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://www.example.com';
	const firstImage = girlData.media.find((url: string) => url.match(/\.(jpeg|jpg|gif|png)$/)) || "/default-og-image.jpg";
	
	const structuredData = {
		"@context": "https://schema.org",
		"@type": "Person",
		"name": girlData.title,
		"description": girlData.description || `${girlData.city}${girlData.price}女孩${girlData.title}的详细信息`,
		"image": firstImage.startsWith('http') ? firstImage : `${baseUrl}${firstImage}`,
		"address": {
			"@type": "PostalAddress",
			"addressLocality": girlData.city,
			"addressCountry": "CN"
		},
		"url": `${baseUrl}/girl/${girlData.girl_id}`,
		"identifier": girlData.girl_id,
		"additionalProperty": [
			{
				"@type": "PropertyValue",
				"name": "年龄",
				"value": girlData.age
			},
			{
				"@type": "PropertyValue", 
				"name": "身高",
				"value": girlData.height
			},
			{
				"@type": "PropertyValue",
				"name": "城市",
				"value": girlData.city
			}
		].filter(prop => prop.value), // 过滤掉空值
		"aggregateRating": girlData.overallRating ? {
			"@type": "AggregateRating",
			"ratingValue": girlData.overallRating,
			"bestRating": "10",
			"worstRating": "1"
		} : undefined
	};

	// 清理undefined字段
	return JSON.parse(JSON.stringify(structuredData));
}

export default async function GirlPage({ params }: { params: { girl_id: string } }) {
	const girlData = await getGirlById(params.girl_id);

	if (!girlData) {
		return <div>Girl not found</div>;
	}

	const structuredData = generateStructuredData(girlData);

	const breadcrumbItems = [
		{ label: "城市导航", href: "/city" },
		{ label: `${girlData.city}资源库`, href: `/city/${girlData.city}` },
		{ label: girlData.title }
	];

	return (
		<>
			{/* 结构化数据 */}
			<script
				type="application/ld+json"
				dangerouslySetInnerHTML={{
					__html: JSON.stringify(structuredData),
				}}
			/>
			
			<div className="container mx-auto max-w-7xl px-4 pt-4">
				<Breadcrumb items={breadcrumbItems} />
			</div>
			
			<GirlDetail girl={girlData} />
			
			<div className="relative container mx-auto max-w-3xl px-4 md:px-6 pb-16 mt-8">
				<div className="overflow-hidden rounded-xl p-6">
					<div className="text-center">
						<h3 className="text-2xl font-semibold text-white mb-4 flex items-center justify-center gap-2">
							<svg className="w-6 h-6 text-dark-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
							</svg>
							<span>发现更多极品资源</span>
						</h3>
						<p className="text-gray-300 text-lg mb-6">我们的资源库还有更多高质量女孩等你探索，各种类型任你选择</p>
						<Link 
							href="/city" 
							className="inline-flex items-center justify-center px-6 py-3 bg-[#e50049] hover:bg-[#ff1464] text-white font-medium rounded-md transition-all duration-300 shadow-lg shadow-[#e50049]/20 hover:shadow-xl hover:shadow-[#e50049]/30"
						>
							前往资源库
							<svg 
								xmlns="http://www.w3.org/2000/svg" 
								className="h-5 w-5 ml-2" 
								fill="none" 
								viewBox="0 0 24 24" 
								stroke="currentColor"
							>
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
							</svg>
						</Link>
					</div>
				</div>
			</div>
		</>
	);
}
