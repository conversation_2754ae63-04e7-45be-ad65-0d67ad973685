import { NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';
import { getServerSession } from "next-auth/next";
import { authOptions } from '../auth/authOptions'; // Updated import path
import { Prisma } from '@prisma/client';

export async function GET(req: Request) {
    const session = await getServerSession(authOptions);

    if (!session || !session.user?.username) {
        return NextResponse.json({ error: '未认证' }, { status: 401 });
    }

    try {
        const user = await prisma.user.findUnique({
            where: { username: session.user.username },
            select: {
                username: true,
                city: true,
                preferredTypes: true,
                dislikedTraits: true,
                priceRanges: true,
                fetishes: true,
                preferredJobs: true,
                createdAt: true,
            } as Prisma.UserSelect,
        });

        if (!user) {
            return NextResponse.json({ error: '用户未找到' }, { status: 404 });
        }

        return NextResponse.json(user);
    } catch (error) {
        console.error('获取个人资料时出错:', error);
        return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
    }
}