import { NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma'; // 使用导入的 prisma
import bcrypt from 'bcrypt';

export async function POST(req: Request) {
  try {
    const {
      username,
      password,
      city,
      preferredTypes,
      dislikedTraits,
      priceRanges,
      fetishes,
      preferredJobs
    } = await req.json();

    // 检查用户名长度
    if (username.length < 3) {
      return NextResponse.json(
        { message: '用户名长度不能少于3个字符' },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { username },
    });

    if (existingUser) {
      return NextResponse.json({ message: '用户已存在' }, { status: 400 });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create user
    const user = await prisma.user.create({
      data: {
        username,
        password: hashedPassword,
        city,
        preferredTypes,
        dislikedTraits,
        priceRanges,
        fetishes,
        preferredJobs,
      },
    });

    return NextResponse.json({ message: '注册成功' }, { status: 201 });
  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json({ message: '注册失败' }, { status: 500 });
  }
}
