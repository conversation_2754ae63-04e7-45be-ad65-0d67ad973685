import { NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';
import { getCache, setCache, generateStatsKey, CACHE_EXPIRY } from '../../../lib/cache';

// 强制动态路由
export const dynamic = 'force-dynamic';
export const revalidate = 0;

export async function GET() {
    try {
        const cacheKey = generateStatsKey('girls-total-count');
        
        // 尝试从缓存获取数据
        const cachedCount = await getCache<{ count: number }>(cacheKey);
        if (cachedCount) {
            console.log('返回缓存的总数统计:', cacheKey);
            return NextResponse.json(cachedCount, {
                headers: {
                    'Cache-Control': 'public, max-age=300',
                    'Content-Type': 'application/json',
                    'X-Cache': 'HIT',
                },
            });
        }

        // 获取Girl数据库中的总数
        const totalCount = await prisma.girl.count();
        
        const responseData = { count: totalCount };

        // 将数据存入缓存
        await setCache(cacheKey, responseData, CACHE_EXPIRY.COUNT_DATA);
        console.log('总数统计已缓存:', cacheKey);
        
        return NextResponse.json(responseData, {
            headers: {
                'Cache-Control': 'public, max-age=300',
                'Content-Type': 'application/json',
                'X-Cache': 'MISS',
            },
        });
    } catch (error) {
        console.error('Error fetching girls count:', error);
        
        return NextResponse.json({
            error: 'Failed to fetch girls count',
            details: error instanceof Error ? error.message : 'Unknown error',
        }, {
            status: 500,
            headers: {
                'Cache-Control': 'no-store, no-cache, must-revalidate',
                'Content-Type': 'application/json',
            },
        });
    }
}