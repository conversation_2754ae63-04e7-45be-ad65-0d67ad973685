import { NextResponse } from 'next/server';
import { clearGirlsCache, deleteCachePattern } from '../../../lib/cache';
import { getRedisClient } from '../../../lib/redis';
import { getServerSession } from 'next-auth';

export async function DELETE(request: Request) {
    try {
        // 可选：检查用户权限
        // const session = await getServerSession();
        // if (!session) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const { searchParams } = new URL(request.url);
        const pattern = searchParams.get('pattern');
        const action = searchParams.get('action');

        if (action === 'clear-all') {
            // 清除所有Girls相关缓存
            await clearGirlsCache();
            console.log('已清除所有Girls相关缓存');
            
            return NextResponse.json({ 
                message: '已清除所有Girls相关缓存',
                timestamp: new Date().toISOString()
            });
        }

        if (pattern) {
            // 根据模式删除特定缓存
            await deleteCachePattern(pattern);
            console.log(`已清除缓存模式: ${pattern}`);
            
            return NextResponse.json({ 
                message: `已清除缓存模式: ${pattern}`,
                timestamp: new Date().toISOString()
            });
        }

        return NextResponse.json({ error: '请提供有效的action或pattern参数' }, { status: 400 });

    } catch (error) {
        console.error('清除缓存失败:', error);
        return NextResponse.json({ 
            error: '清除缓存失败',
            details: error instanceof Error ? error.message : 'Unknown error'
        }, { status: 500 });
    }
}

export async function GET(request: Request) {
    try {
        const { searchParams } = new URL(request.url);
        const action = searchParams.get('action');

        if (action === 'info') {
            // 获取Redis连接信息
            const client = await getRedisClient();
            const info = await client.info('memory');
            const dbSize = await client.dbSize();
            
            return NextResponse.json({
                connected: true,
                keyCount: dbSize,
                memoryInfo: info,
                timestamp: new Date().toISOString()
            });
        }

        if (action === 'keys') {
            // 获取所有缓存键（仅用于调试，生产环境慎用）
            const pattern = searchParams.get('pattern') || '*';
            const client = await getRedisClient();
            const keys = await client.keys(pattern);
            
            return NextResponse.json({
                pattern,
                keys: keys.slice(0, 100), // 限制返回数量
                totalCount: keys.length,
                timestamp: new Date().toISOString()
            });
        }

        return NextResponse.json({ 
            message: 'Redis缓存管理API',
            availableActions: [
                'GET /api/cache?action=info - 获取Redis信息',
                'GET /api/cache?action=keys&pattern=* - 获取缓存键列表',
                'DELETE /api/cache?action=clear-all - 清除所有Girls缓存',
                'DELETE /api/cache?pattern=girls:* - 清除指定模式的缓存'
            ]
        });

    } catch (error) {
        console.error('获取缓存信息失败:', error);
        return NextResponse.json({ 
            error: '获取缓存信息失败',
            details: error instanceof Error ? error.message : 'Unknown error'
        }, { status: 500 });
    }
}