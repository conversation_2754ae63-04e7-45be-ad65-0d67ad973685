import { NextResponse } from 'next/server';
import { prisma } from '../../../../../lib/prisma';
import { getCache, setCache, generateCacheKey, CACHE_PREFIXES, CACHE_EXPIRY } from '../../../../../lib/cache';

export async function GET(
    req: Request,
    { params }: { params: { girl_id: string } }
) {
    try {
        const cacheKey = generateCacheKey(CACHE_PREFIXES.GIRL_DETAIL, params.girl_id);
        
        // 尝试从缓存获取数据
        const cachedGirl = await getCache(cacheKey);
        if (cachedGirl) {
            console.log('返回缓存的Girl详情:', params.girl_id);
            return NextResponse.json(cachedGirl, {
                headers: {
                    'Cache-Control': 'public, max-age=900',
                    'X-Cache': 'HIT',
                },
            });
        }

        const girl = await prisma.girl.findUnique({
            where: { girl_id: params.girl_id }
        });

        if (!girl) {
            return NextResponse.json({ error: 'Girl not found' }, { status: 404 });
        }

        // 将数据存入缓存
        await setCache(cacheKey, girl, CACHE_EXPIRY.GIRL_DETAIL);
        console.log('Girl详情已缓存:', params.girl_id);

        return NextResponse.json(girl, {
            headers: {
                'Cache-Control': 'public, max-age=900',
                'X-Cache': 'MISS',
            },
        });
    } catch (error) {
        console.error('Error fetching girl by ID:', error);
        return NextResponse.json({ error: 'Error fetching girl' }, { status: 500 });
    }
} 