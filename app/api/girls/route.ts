import { NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';
import { getCache, setCache, generateGirlsApiKey, CACHE_EXPIRY } from '../../../lib/cache';

// 强制动态路由
export const dynamic = 'force-dynamic';
export const revalidate = 0;

// 设置时间范围常量（单位：天）
const RECENT_DAYS = 3;

// 创建一个函数来获取最近日期
const getRecentDate = () => new Date(Date.now() - RECENT_DAYS * 24 * 60 * 60 * 1000);

export async function GET(request: Request) {
    console.log('GET /api/girls endpoint hit');

    try {
        // 解析查询参数
        const { searchParams } = new URL(request.url);
        const page = Number(searchParams.get('page')) || 1;
        const perPage = Number(searchParams.get('perPage')) || 20;
        const city = searchParams.get('city');
        const district = searchParams.get('district');
        const cup = searchParams.get('cup');
        const price = searchParams.get('price');
        const type = searchParams.get('type');
        const service = searchParams.get('service');
        const hasRating = searchParams.get('hasRating') === 'true';
        const beforeDays = searchParams.get('beforeDays') === 'true';

        console.log('Query params:', { page, perPage, city, district, cup, price, type, service, hasRating, beforeDays });

        // 生成缓存键
        const filters = {
            city, district, cup, price, type, service, 
            hasRating: hasRating.toString(), 
            beforeDays: beforeDays.toString()
        };
        const cacheKey = generateGirlsApiKey(page, perPage, filters);

        // 尝试从缓存获取数据
        const cachedData = await getCache<{
            girls: any[];
            totalPages: number;
            totalGirls: number;
        }>(cacheKey);

        if (cachedData) {
            console.log('返回缓存数据:', cacheKey);
            return NextResponse.json(cachedData, {
                headers: {
                    'Cache-Control': 'public, max-age=300',
                    'Content-Type': 'application/json',
                    'X-Cache': 'HIT',
                },
            });
        }

        // 构建查询条件
        const where: any = {};
        
        // 添加时间限制
        if (beforeDays) {
            // 显示3天前的数据
            where.updatedAt = {
                lt: getRecentDate()
            };
        } else {
            // 显示最近3天的数据
            where.updatedAt = {
                gte: getRecentDate()
            };
        }
        
        // 如果需要筛选有评价的女孩
        if (hasRating) {
            where.overallRating = { not: null };
        };

        if (city) where.city = city;
        if (district) {
            const districts = district.split('|');
            where.district = {
                in: districts
            };
        }
        if (cup) {
            // 支持大小写不敏感的罩杯匹配
            // 如果输入D，将匹配数据库中的'D'和'd'
            where.cup = {
                mode: 'insensitive',
                equals: cup
            };
        }
        if (price) {
            const prices = price.split('|');
            where.OR = prices.map(p => ({
                price: p.trim()
            }));
        }
        if (type) where.type = { has: type };
        if (service) where.service = { has: service };

        console.log('Prisma where condition:', JSON.stringify(where, null, 2));

        const skip = (page - 1) * perPage;

        // 并行查询数据和总数
        const [girls, totalGirls] = await Promise.all([
            prisma.girl.findMany({
                where,
                skip,
                take: perPage,
                orderBy: {
                    // 如果筛选有评价的女孩，按评分排序；否则按更新时间排序
                    ...(hasRating 
                        ? { overallRating: 'desc' } 
                        : { updatedAt: 'desc' })
                },
                select: {
                    id: true,
                    girl_id: true,
                    title: true,
                    feature: true,
                    city: true,
                    district: true,
                    height: true,
                    age: true,
                    cup: true,
                    price: true,
                    type: true,
                    service: true,
                    description: true,
                    media: true,
                    updatedAt: true,
                    photoAccuracy: true,
                    appearance: true,
                    attitude: true,
                    serviceQuality: true,
                    overallRating: true,
                    comments: true,
                }
            }),
            prisma.girl.count({ where })
        ]);

        const totalPages = Math.ceil(totalGirls / perPage);

        console.log(`Found ${girls.length} girls out of ${totalGirls} total`);

        // 准备响应数据
        const responseData = {
            girls,
            totalPages,
            totalGirls,
        };

        // 将数据存入缓存
        await setCache(cacheKey, responseData, CACHE_EXPIRY.GIRLS_LIST);
        console.log('数据已缓存:', cacheKey);

        // 返回响应
        return NextResponse.json(responseData, {
            headers: {
                'Cache-Control': 'public, max-age=300',
                'Content-Type': 'application/json',
                'X-Cache': 'MISS',
            },
        });
    } catch (error) {
        console.error('Error in GET /api/girls:', error);

        return NextResponse.json({
            error: 'Failed to fetch girls',
            details: error instanceof Error ? error.message : 'Unknown error',
        }, {
            status: 500,
            headers: {
                'Cache-Control': 'no-store, no-cache, must-revalidate',
                'Content-Type': 'application/json',
            },
        });
    }
} 