import { NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';

// 设置时间范围常量（单位：天）
const RECENT_DAYS = 3;

// 创建一个函数来获取最近日期
const getRecentDate = () => new Date(Date.now() - RECENT_DAYS * 24 * 60 * 60 * 1000);

export const dynamic = 'force-dynamic';

export async function GET(req: Request) {
    console.log('【DEBUG】services API调用开始');
    try {
        const url = new URL(req.url);
        const city = url.searchParams.get('city');
        const price = url.searchParams.get('price');
        
        console.log(`【DEBUG】services API参数: city=${city || 'all'}, price=${price || 'all'}`);

        const where: any = {
            updatedAt: {
                gte: getRecentDate()
            }
        };
        if (city) where.city = city;
        
        // 处理价格筛选
        if (price) {
            if (price.includes('|')) {
                // 处理组合价格，例如 "5K|W"
                const priceArray = price.split('|');
                where.OR = priceArray.map(p => ({ price: p }));
            } else {
                where.price = price;
            }
        }
        
        console.log(`【DEBUG】services API查询条件:`, JSON.stringify(where, null, 2));

        const services = await prisma.girl.findMany({
            where,
            select: {
                service: true
            }
        });
        
        console.log(`【DEBUG】services API查询结果: 获取到${services.length}条记录`);

        const serviceCounts: Record<string, number> = {};
        services.forEach(girl => {
            if (girl.service && Array.isArray(girl.service)) {
                girl.service.forEach(service => {
                    serviceCounts[service] = (serviceCounts[service] || 0) + 1;
                });
            }
        });
        
        console.log(`【DEBUG】services API处理后结果:`, JSON.stringify(serviceCounts, null, 2));
        
        // 如果没有结果，添加一些默认值以确保UI不会崩溃
        if (Object.keys(serviceCounts).length === 0) {
            console.log(`【WARNING】services API没有找到任何结果，返回默认值`);
            return NextResponse.json({ "默认": 0 });
        }

        return NextResponse.json(serviceCounts);
    } catch (error) {
        console.error('【ERROR】获取服务数据失败:', error);
        // 返回一个空对象，确保前端不会崩溃
        return NextResponse.json({ error: 'Error fetching service counts' }, { status: 500 });
    }
} 