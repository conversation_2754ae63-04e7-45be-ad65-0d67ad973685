import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { PrismaAdapter } from '@auth/prisma-adapter';
import { prisma } from '../../../lib/prisma';
import bcrypt from 'bcrypt';

// Extend the built-in session types
declare module 'next-auth' {
    interface User {
        username: string;
    }
    interface Session {
        user: User & {
            id: string;
            username: string;
        };
    }
}

// Extend AdapterUser type
declare module '@auth/prisma-adapter' {
    interface AdapterUser {
        username: string;
    }
}

export const authOptions: NextAuthOptions = {
    adapter: PrismaAdapter(prisma) as any,
    providers: [
        CredentialsProvider({
            name: 'Credentials',
            credentials: {
                username: { label: 'Username', type: 'text' },
                password: { label: 'Password', type: 'password' }
            },
            async authorize(credentials) {
                if (!credentials?.username || !credentials?.password) {
                    return null;
                }
                const user = await prisma.user.findUnique({
                    where: { username: credentials.username }
                });
                if (!user) {
                    return null;
                }
                const isPasswordValid = await bcrypt.compare(credentials.password, user.password);
                if (!isPasswordValid) {
                    return null;
                }
                return {
                    id: user.id,
                    username: user.username,
                    isVIP: user.isVIP, // 确保这里返回 isVIP 状态
                };
            }
        })
    ],
    session: {
        strategy: 'jwt'
    },
    pages: {
        signIn: '/login',
    },
    callbacks: {
        async jwt({ token, user }) {
            if (user) {
                token.id = user.id;
                token.username = user.username;
                token.isVIP = user.isVIP;
            }
            return token;
        },
        async session({ session, token }) {
            if (session.user) {
                session.user.id = token.id as string;
                session.user.username = token.username as string;
                session.user.isVIP = token.isVIP as boolean;
            }
            return session;
        }
    },
    secret: process.env.NEXTAUTH_SECRET,
};
