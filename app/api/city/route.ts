import { NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';
import { getCache, setCache, generateLocationKey, CACHE_EXPIRY } from '../../../lib/cache';

// 设置时间范围常量（单位：天）
const RECENT_DAYS = 3;

// 创建一个函数来获取最近日期
const getRecentDate = () => new Date(Date.now() - RECENT_DAYS * 24 * 60 * 60 * 1000);

export const dynamic = 'force-dynamic';

export async function GET() {
    try {
        console.log('Fetching cities...');

        const cacheKey = generateLocationKey('cities');
        
        // 尝试从缓存获取数据
        const cachedCities = await getCache<string[]>(cacheKey);
        if (cachedCities) {
            console.log('返回缓存的城市列表:', cacheKey);
            return NextResponse.json(cachedCities, {
                headers: {
                    'Cache-Control': 'public, max-age=1800',
                    'X-Cache': 'HIT',
                },
            });
        }

        const cities = await prisma.girl.findMany({
            where: {
                updatedAt: {
                    gte: getRecentDate()
                }
            },
            select: {
                city: true
            },
            distinct: ['city']
        });

        const cityList = cities
            .map(c => c.city)
            .filter(city => city !== null && city !== "");

        console.log(`Found ${cityList.length} cities`);

        // 将数据存入缓存
        await setCache(cacheKey, cityList, CACHE_EXPIRY.LOCATION_DATA);
        console.log('城市列表已缓存:', cacheKey);

        return NextResponse.json(cityList, {
            headers: {
                'Cache-Control': 'public, max-age=1800',
                'X-Cache': 'MISS',
            },
        });
    } catch (error) {
        console.error('Error fetching cities:', error);
        return NextResponse.json(
            { error: 'Error fetching cities', details: error instanceof Error ? error.message : 'Unknown error' },
            { status: 500 }
        );
    }
} 