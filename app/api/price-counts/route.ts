import { NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';

// 设置时间范围常量（单位：天）
const RECENT_DAYS = 3;

// 创建一个函数来获取最近日期
const getRecentDate = () => new Date(Date.now() - RECENT_DAYS * 24 * 60 * 60 * 1000);

// 添加 dynamic = 'force-dynamic' 配置
export const dynamic = 'force-dynamic';

export async function GET(req: Request) {
    try {
        // 使用 searchParams 替代 URL 构造
        const searchParams = new URL(req.url).searchParams;
        const city = searchParams.get('city');

        const where: any = {
            updatedAt: {
                gte: getRecentDate()
            }
        };
        if (city) where.city = city;

        const priceCounts = await prisma.girl.groupBy({
            by: ['price'],
            _count: {
                _all: true
            },
            where
        });

        const result = priceCounts.reduce((acc, item) => {
            if (item.price) {
                acc[item.price] = item._count._all;
            }
            return acc;
        }, {} as Record<string, number>);

        // 设置缓存控制头
        return NextResponse.json(result, {
            headers: {
                'Cache-Control': 'no-store, max-age=0',
            },
        });
    } catch (error) {
        console.error('Error fetching price counts:', error);
        return NextResponse.json(
            { error: 'Error fetching price counts' },
            {
                status: 500,
                headers: {
                    'Cache-Control': 'no-store, max-age=0',
                },
            }
        );
    }
} 