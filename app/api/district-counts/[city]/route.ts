import { NextResponse } from 'next/server';
import { prisma } from '../../../../lib/prisma';

// 设置时间范围常量（单位：天）
const RECENT_DAYS = 3;

// 创建一个函数来获取最近日期
const getRecentDate = () => new Date(Date.now() - RECENT_DAYS * 24 * 60 * 60 * 1000);

export async function GET(
    req: Request,
    { params }: { params: { city: string } }
) {
    try {
        const districtCounts = await prisma.girl.groupBy({
            by: ['district'],
            _count: {
                _all: true
            },
            where: {
                city: params.city,
                updatedAt: {
                    gte: getRecentDate()
                }
            }
        });

        const result = districtCounts.reduce((acc, item) => {
            if (item.district) {
                acc[item.district] = item._count._all;
            }
            return acc;
        }, {} as Record<string, number>);

        return NextResponse.json(result);
    } catch (error) {
        console.error(`Error fetching district counts for city ${params.city}:`, error);
        return NextResponse.json({ error: `Error fetching district counts for city ${params.city}` }, { status: 500 });
    }
} 