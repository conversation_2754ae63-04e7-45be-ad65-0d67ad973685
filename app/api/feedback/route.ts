import { NextResponse } from "next/server";
import { prisma } from "../../../lib/prisma";

// 设置路由的缓存策略
export const revalidate = 60;

export const dynamic = 'force-dynamic';

export async function GET(request: Request) {
  try {
    // 解析查询参数
    const { searchParams } = new URL(request.url);
    const page = Number(searchParams.get('page')) || 1;
    const perPage = Number(searchParams.get('perPage')) || 20;
    const city = searchParams.get('city') || undefined;
    const price = searchParams.get('price') || undefined;

    // 计算分页参数
    const skip = (page - 1) * perPage;

    // 构建查询条件
    const where: any = {
      OR: [
        { photoAccuracy: { not: null } },
        { appearance: { not: null } },
        { attitude: { not: null } },
        { serviceQuality: { not: null } },
        { overallRating: { not: null } },
        { comments: { not: null } }
      ]
    };

    // 添加过滤条件
    if (city) where.city = city;
    if (price) where.price = price;

    // 并行查询数据和总数
    const [girls, totalGirls] = await Promise.all([
      prisma.girl.findMany({
        where,
        skip,
        take: perPage,
        orderBy: {
          updatedAt: 'desc'
        }
      }),
      prisma.girl.count({ where })
    ]);

    // 计算总页数
    const totalPages = Math.ceil(totalGirls / perPage);

    return NextResponse.json({
      girls,
      totalPages,
      totalGirls
    }, {
      headers: {
        'Cache-Control': 'public, s-maxage=60, stale-while-revalidate=30',
      },
    });
  } catch (error) {
    console.error("Error fetching girls with feedback:", error);
    return NextResponse.json({ error: "Failed to fetch girls" }, { status: 500 });
  }
} 