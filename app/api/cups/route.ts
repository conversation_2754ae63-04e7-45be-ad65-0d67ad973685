import { NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';

// 设置时间范围常量（单位：天）
const RECENT_DAYS = 3;

// 创建一个函数来获取最近日期
const getRecentDate = () => new Date(Date.now() - RECENT_DAYS * 24 * 60 * 60 * 1000);

export const dynamic = 'force-dynamic';

export async function GET(req: Request) {
    console.log('【DEBUG】cups API调用开始');
    try {
        const url = new URL(req.url);
        const city = url.searchParams.get('city');
        const price = url.searchParams.get('price');
        
        console.log(`【DEBUG】cups API参数: city=${city || 'all'}, price=${price || 'all'}`);

        const where: any = {
            updatedAt: {
                gte: getRecentDate()
            }
        };
        if (city) where.city = city;
        
        // 处理价格筛选
        if (price) {
            if (price.includes('|')) {
                // 处理组合价格，例如 "5K|W"
                const priceArray = price.split('|');
                where.OR = priceArray.map(p => ({ price: p }));
            } else {
                where.price = price;
            }
        }
        
        console.log(`【DEBUG】cups API查询条件:`, JSON.stringify(where, null, 2));

        const cupCounts = await prisma.girl.groupBy({
            by: ['cup'],
            _count: {
                _all: true
            },
            where
        });
        
        console.log(`【DEBUG】cups API查询结果:`, JSON.stringify(cupCounts, null, 2));

        // 创建一个映射来合并大小写相同的罩杯
        const normalizedCups: Record<string, number> = {};
        
        for (const item of cupCounts) {
            if (!item.cup) continue;
            
            // 转换为大写作为标准化键
            const normalizedCup = item.cup.toUpperCase();
            
            // 累加计数到标准化的罩杯中
            if (!normalizedCups[normalizedCup]) {
                normalizedCups[normalizedCup] = 0;
            }
            normalizedCups[normalizedCup] += item._count._all;
        }
        
        console.log(`【DEBUG】cups API处理后结果(大小写合并):`, JSON.stringify(normalizedCups, null, 2));
        
        // 如果没有结果，添加一些默认值以确保UI不会崩溃
        if (Object.keys(normalizedCups).length === 0) {
            console.log(`【WARNING】cups API没有找到任何结果，返回默认值`);
            return NextResponse.json({ "默认": 0 });
        }

        return NextResponse.json(normalizedCups);
    } catch (error) {
        console.error('【ERROR】获取罩杯数据失败:', error);
        // 返回一个空对象，确保前端不会崩溃
        return NextResponse.json({ error: 'Error fetching cup counts' }, { status: 500 });
    }
} 