import { NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';
import { getCache, setCache, generateLocationKey, CACHE_EXPIRY } from '../../../lib/cache';

// 设置时间范围常量（单位：天）
const RECENT_DAYS = 3;

// 创建一个函数来获取最近日期
const getRecentDate = () => new Date(Date.now() - RECENT_DAYS * 24 * 60 * 60 * 1000);

export async function GET() {
    try {
        const cacheKey = generateLocationKey('districts');
        
        // 尝试从缓存获取数据
        const cachedDistricts = await getCache<Array<{ city: string; district: string }>>(cacheKey);
        if (cachedDistricts) {
            console.log('返回缓存的地区列表:', cacheKey);
            return NextResponse.json(cachedDistricts, {
                headers: {
                    'Cache-Control': 'public, max-age=1800',
                    'X-Cache': 'HIT',
                },
            });
        }

        const districts = await prisma.girl.findMany({
            where: {
                updatedAt: {
                    gte: getRecentDate()
                }
            },
            select: {
                city: true,
                district: true
            },
            distinct: ['city', 'district']
        });

        const districtList = districts
            .filter(d => d.city !== null && d.city !== "" && d.district !== null && d.district !== "")
            .map(d => ({ city: d.city, district: d.district }));

        // 将数据存入缓存
        await setCache(cacheKey, districtList, CACHE_EXPIRY.LOCATION_DATA);
        console.log('地区列表已缓存:', cacheKey);

        return NextResponse.json(districtList, {
            headers: {
                'Cache-Control': 'public, max-age=1800',
                'X-Cache': 'MISS',
            },
        });
    } catch (error) {
        console.error('Error fetching districts:', error);
        return NextResponse.json({ error: 'Error fetching districts' }, { status: 500 });
    }
} 