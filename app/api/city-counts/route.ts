import { NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';
import { firstTierCities } from '../../../lib/cityNameMapping';
import { getCache, setCache, generateStatsKey, CACHE_EXPIRY } from '../../../lib/cache';

// 设置时间范围常量（单位：天）
const RECENT_DAYS = 3;

// 创建一个函数来获取最近日期
const getRecentDate = () => new Date(Date.now() - RECENT_DAYS * 24 * 60 * 60 * 1000);

export async function GET(req: Request) {
    try {
        // 获取查询参数
        const { searchParams } = new URL(req.url);
        const price = searchParams.get('price');

        // 生成缓存键
        const cacheKey = generateStatsKey('city-counts', price || 'all');
        
        // 尝试从缓存获取数据
        const cachedData = await getCache<Record<string, number>>(cacheKey);
        if (cachedData) {
            console.log('返回缓存的城市统计:', cacheKey);
            return new NextResponse(JSON.stringify(cachedData), {
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'public, max-age=600',
                    'X-Cache': 'HIT',
                }
            });
        }

        // 构建查询条件，添加时间限制
        const where: any = {
            updatedAt: {
                gte: getRecentDate()
            }
        };

        // 如果有价格筛选，添加价格条件
        if (price) {
            // 解析price参数，处理可能的多值情况（例如'5K|W'）
            const priceValues = price.split('|');
            
            // 简化的OR条件 - 只要包含任一价格值即可
            if (priceValues.length > 0) {
                where.OR = priceValues.map(p => ({
                    price: { contains: p }
                }));
            }
        }

        const cityCounts = await prisma.girl.groupBy({
            by: ['city'],
            _count: {
                _all: true
            },
            where
        });

        const result = cityCounts.reduce((acc, item) => {
            if (item.city) {
                acc[item.city] = item._count._all;
            }
            return acc;
        }, {} as Record<string, number>);

        // 补全所有一线城市，没数据的城市 count=0
        firstTierCities.forEach(city => {
            if (!(city in result)) {
                result[city] = 0;
            }
        });

        // 将数据存入缓存
        await setCache(cacheKey, result, CACHE_EXPIRY.STATS_DATA);
        console.log('城市统计已缓存:', cacheKey);
        
        return new NextResponse(JSON.stringify(result), {
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'public, max-age=600',
                'X-Cache': 'MISS',
            }
        });
    } catch (error) {
        console.error('Error fetching city counts:', error);
        return NextResponse.json({ error: 'Error fetching city counts' }, { status: 500 });
    }
} 