-- CreateTable
CREATE TABLE "girls" (
    "id" TEXT NOT NULL,
    "girl_id" TEXT NOT NULL,
    "user_id" TEXT,
    "title" TEXT NOT NULL,
    "feature" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "district" TEXT,
    "height" TEXT,
    "age" TEXT,
    "weight" TEXT,
    "cup" TEXT,
    "price" TEXT NOT NULL,
    "type" TEXT[],
    "service" TEXT[],
    "media" TEXT[],
    "pusher" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "girls_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "girls_girl_id_key" ON "girls"("girl_id");
