// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model Girl {
  id          String   @id @default(cuid())
  girl_id     String   @unique @map("girl_id")
  user_id     String?  @map("user_id")
  title       String
  feature     String
  city        String
  district    String?
  height      String?
  age         String?
  weight      String?
  cup         String?
  price       String
  type        String[]
  service     String[]
  media       String[]
  photoAccuracy Float?   // 照片准确度评分
  appearance    Float?   // 外貌评分
  attitude Float?   // 态度评分
  serviceQuality Float?   // 服务质量评分
  overallRating Float?   // 整体评分
  comments      String?  // 评论
  pusher        String
  message       String
  description   String?
  notes         String?  // 备注
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@map("girls")
}

model User {
  id                String   @id @default(cuid())
  username          String   @unique
  password          String
  city              String?
  preferredTypes    String[]
  dislikedTraits    String[]
  priceRanges       String[]
  fetishes          String[]
  preferredJobs     String[]
  balance           Float    @default(0)
  points            Int      @default(0)
  isVIP             Boolean  @default(false)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("users")
}
