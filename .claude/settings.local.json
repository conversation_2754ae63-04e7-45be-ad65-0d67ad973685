{"permissions": {"allow": ["<PERSON><PERSON>(htop:*)", "Bash(pm2 list:*)", "<PERSON><PERSON>(pm2 show:*)", "Bash(find:*)", "Bash(/root/.nvm/versions/node/v18.14.2/bin/pm2 list)", "Bash(/root/.nvm/versions/node/v18.14.2/bin/pm2 show:*)", "<PERSON><PERSON>(cat:*)", "Bash(/root/.nvm/versions/node/v18.14.2/bin/pm2 stop:*)", "Bash(/root/.nvm/versions/node/v18.14.2/bin/pm2 delete:*)", "Bash(/root/.nvm/versions/node/v18.14.2/bin/pm2 save:*)", "<PERSON><PERSON>(top:*)", "Bash(ls:*)", "Bash(grep:*)", "Bash(redis-cli:*)", "Bash(/root/.nvm/versions/node/v18.14.2/bin/pm2 restart:*)", "Bash(ps:*)", "Bash(sar:*)", "Bash(/root/.nvm/versions/node/v18.14.2/bin/pm2 logs --lines 50)", "Bash(pm2 reload:*)", "Bash(npx pm2 reload:*)", "Bash(npx pm2 list:*)", "Bash(npx pm2 delete:*)", "Bash(npx pm2 start:*)", "WebFetch(domain:www.jianier.com)", "Bash(openssl x509:*)", "Bash(nginx:*)", "<PERSON><PERSON>(curl:*)", "Bash(rg:*)"], "deny": []}}