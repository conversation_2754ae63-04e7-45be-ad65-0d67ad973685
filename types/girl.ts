export interface Girl {
    id: string;
    girl_id: string;
    user_id?: string;
    title: string;
    feature: string;
    city: string;
    district?: string;
    height?: string;
    age?: string;
    weight?: string;
    cup?: string;
    price: string;
    type: string[];
    service: string[];
    media: string[];
    pusher?: string;
    message?: string;
    description?: string;
    createdAt?: string; // Use Date if handling Date objects
    updatedAt?: string; // Use Date if handling Date objects
    photoAccuracy?: number;
    appearance?: number;
    attitude?: number;
    serviceQuality?: number;
    overallRating?: number;
    comments?: string;
}