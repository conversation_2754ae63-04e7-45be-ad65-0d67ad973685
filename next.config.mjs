import withPWA from 'next-pwa';

/** @type {import('next').NextConfig} */
const nextConfig = {
	images: {
		remotePatterns: [
			{
				protocol: "https",
				hostname: "tgproxy.jianier.club",
			},
			{
				protocol: "https",
				hostname: "tgproxy.jianier.com",
			},
			{
				protocol: "https",
				hostname: "webhook1.jianier.com",
			},
		]
	},
	// 启用 gzip 压缩
	compress: true,
	// 优化生产构建
	swcMinify: true,
};

const config = withPWA({
	dest: 'public',
	register: true,
	skipWaiting: true,
})(nextConfig);

export default config;
