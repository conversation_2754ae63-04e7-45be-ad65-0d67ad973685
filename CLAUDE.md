# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 在此代码仓库中工作时提供指导。

## 项目概述

这是一个基于 Next.js 14 和 TypeScript 的应用程序，作为管理和展示个人档案的平台，具有基于位置的过滤功能。项目使用：

- **框架**: Next.js 14 with App Router
- **数据库**: PostgreSQL with Prisma ORM
- **身份验证**: NextAuth.js with credentials provider
- **UI组件**: Shadcn UI components with Tailwind CSS
- **部署**: PM2 用于生产环境管理
- **附加功能**: PWA 支持、Telegram 机器人集成、图像处理

## 开发命令

```bash
# 启动开发服务器
npm run dev

# 生产环境构建（包含 Prisma 生成）
npm run build

# 启动生产服务器
npm start

# 运行 Telegram 机器人 webhook 接收器
npm run webhook
npm run webhook:dev  # 开发模式，使用 nodemon

# 数据库操作
npx prisma generate  # 生成 Prisma 客户端
npx prisma migrate dev  # 在开发环境中运行迁移
npx prisma studio  # 打开 Prisma Studio
```

## 架构

### 数据库架构
- **Girl**: 核心实体，包含个人资料信息、评分和位置数据
- **User**: 身份验证和用户偏好，支持VIP状态

### API 结构
- `/api/auth/` - NextAuth.js 身份验证端点
- `/api/girls/` - 个人资料管理和检索
- `/api/city/` - 基于位置的过滤
- `/api/services/` - 服务类型管理
- 各种用于分析的计数端点

### 组件架构
- **客户端组件**: 对交互式组件使用 "use client" 指令
- **服务端组件**: 数据获取和静态渲染的默认选择
- **移动端支持**: 专用移动组件和响应式设计
- **身份验证**: 用于受保护路由的 `withAuth` HOC

### 关键技术
- **状态管理**: React Context (SidebarContext)
- **样式**: Tailwind CSS 与自定义动画
- **图像处理**: Sharp 用于优化，Jimp 用于水印
- **机器人集成**: Telegraf 用于 Telegram 机器人功能
- **PWA**: Service worker 实现离线支持

### 特殊考虑
- 使用中文语言注释和UI文本
- 实现具有高级功能的VIP用户系统
- 基于城市/区域层次结构的位置过滤
- 个人资料质量评估的评分系统
- 用于内容保护的图像水印

## 开发指南

- UI框架：使用 Shadcn UI 组件
- 代码风格：遵循 Next.js 14 和 React 18 最佳实践
- 始终提供完整的代码实现
- 在UI生成中保持视觉美观
- 使用 TypeScript 确保类型安全
- 遵循现有的命名约定和文件结构