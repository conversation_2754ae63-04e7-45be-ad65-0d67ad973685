{"name": "telegram-login-nextjs", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "prisma generate && next build", "postinstall": "prisma generate", "start": "next start"}, "dependencies": {"@auth/prisma-adapter": "^2.5.3", "@intercom/messenger-js-sdk": "^0.0.14", "@jimp/plugin-print": "^1.6.0", "@microsoft/clarity": "^1.0.0", "@next/third-parties": "^14.2.13", "@notionhq/client": "^2.2.15", "@prisma/client": "^5.20.0", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@types/axios": "^0.14.4", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.7", "@types/redis": "^4.0.10", "@types/uuid": "^10.0.0", "axios": "^1.7.9", "bcrypt": "^5.1.1", "bullmq": "^5.34.10", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cors": "^2.8.5", "disable-devtool": "^0.3.8", "dotenv": "^16.3.1", "express": "^4.18.2", "framer-motion": "^11.18.2", "ioredis": "^5.4.2", "jimp": "^1.6.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.447.0", "next": "^14.2.13", "next-auth": "^4.24.10", "next-plausible": "^3.12.4", "next-pwa": "^5.6.0", "node-cron": "^3.0.3", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.13.0", "react-masonry-css": "^1.0.16", "redis": "^5.5.6", "sharp": "^0.33.5", "tailwind-merge": "^2.5.3", "tailwindcss-animate": "^1.0.7", "telegraf": "^4.16.3", "telegram": "^2.26.8", "uuid": "^11.0.5"}, "devDependencies": {"@shadcn/ui": "^0.0.4", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.17.16", "@types/node-cron": "^3.0.11", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-icons": "^3.0.0", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "autoprefixer": "^10.4.20", "eslint": "^8", "eslint-config-next": "14.2.7", "nodemon": "^3.0.2", "postcss": "^8", "prisma": "^5.20.0", "react-icons": "^5.3.0", "shadcn-ui": "^0.9.2", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5"}}