import type { Config } from 'tailwindcss'

const config: Config = {
  darkMode: 'class',
  content: [
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        dark: {
          DEFAULT: '#121212',
          '50': '#1C1C1C',
          '100': '#1E1E1E',
          '200': '#242424',
          '300': '#282828',
          '400': '#2C2C2C',
          '500': '#323232',
          '600': '#3E3E3E',
          '700': '#484848',
          '800': '#505050',
          '900': '#5A5A5A',
        },
        primary: {
          DEFAULT: '#FF3366',
          dark: '#FF1A53',
        },
        secondary: {
          DEFAULT: '#9333EA',
          dark: '#7B22C3',
        },
      },
      animation: {
        'slide-up': 'slideUp 0.3s ease-out',
      },
      keyframes: {
        slideUp: {
          '0%': { transform: 'translateY(100%)' },
          '100%': { transform: 'translateY(0)' },
        },
      },
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
    },
  },
  plugins: [],
}

export default config
