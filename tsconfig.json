{
	"compilerOptions": {
		"lib": ["dom", "dom.iterable", "esnext"],
		"allowJs": true,
		"skipLibCheck": true,
		"strict": true,
		"noEmit": true,
		"esModuleInterop": true,
		"module": "esnext",
		"moduleResolution": "bundler",
		"resolveJsonModule": true,
		"isolatedModules": true,
		"jsx": "preserve",
		"incremental": true,
		"plugins": [
			{
				"name": "next"
			}
		],
		"paths": {
			"@/*": ["./*"]
		},
		"baseUrl": "."
	},
	"include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "components/GirlCard.tsx.backup", "ecosystem.config.cjs"],
	"exclude": ["node_modules"],
	"ts-node": {
		"esm": true,
		"transpileOnly": true,
		"files": true,
		// Tell ts-node CLI to install the --loader automatically, explained below
		"compilerOptions": {
			"module": "ESNext"
		}
	}
}
