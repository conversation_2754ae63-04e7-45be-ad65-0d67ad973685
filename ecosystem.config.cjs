module.exports = {
  apps: [
    {
      name: 'next-app', // 应用名称
      script: 'node_modules/next/dist/bin/next', // Next.js 启动脚本
      args: 'start', // 启动参数
      env: {
        PORT: 3000, // 设置端口
        NODE_ENV: 'production',
      },
      exec_mode: 'cluster', // 运行模式
      instances: '1', // CPU 核心数，可以设置具体数字
      autorestart: true, // 自动重启
      watch: false, // 文件变化监控
      max_memory_restart: '1G', // 内存超过上限自动重启
    },
  ],
}