# Gemini 项目配置

此文件帮助 Gemini 理解项目的约定和技术。

## 技术栈

*   **框架:** Next.js
*   **语言:** TypeScript
*   **样式:** Tailwind CSS
*   **ORM:** Prisma
*   **数据库:** PostgreSQL

## 项目约定

*   **组件化架构:** 组件位于 `/components` 和 `/app/components` 目录中。
*   **API 路由:** API 逻辑位于 `/app/api` 目录下。
*   **样式:** 使用 Tailwind CSS 类进行样式设置。全局样式位于 `/app/globals.css`。
*   **状态管理:** (请指明, 例如: React Context, Redux)
*   **认证:** 使用 NextAuth.js 进行认证。

## 可用脚本

(请在此处列出重要的 `package.json` 脚本)

*   `npm run dev`: 启动开发服务器。
*   `npm run build`: 构建生产版本的应用。
*   `npm run start`: 启动生产服务器。
*   `npm run lint`: 对代码库进行 Lint 检查。