"use client";

import React, { createContext, useContext, useState, useEffect } from 'react';

interface SidebarContextType {
  isSidebarOpen: boolean;
  setIsSidebarOpen: (isOpen: boolean) => void;
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

export function SidebarProvider({ children }: { children: React.ReactNode }) {
  // 使用useState的初始化函数来避免水合不匹配
  const [isSidebarOpen, setIsSidebarOpen] = useState(() => {
    // 在服务器端渲染时，默认为true
    if (typeof window === 'undefined') return true;
    
    // 在客户端，检查localStorage和设备类型
    const savedState = localStorage.getItem('sidebarState');
    if (savedState !== null) {
      return JSON.parse(savedState);
    }
    
    // 如果没有保存的状态，根据设备类型决定
    return window.innerWidth > 768;
  });

  // 当状态改变时保存到localStorage
  useEffect(() => {
    localStorage.setItem('sidebarState', JSON.stringify(isSidebarOpen));
  }, [isSidebarOpen]);

  return (
    <SidebarContext.Provider value={{ isSidebarOpen, setIsSidebarOpen }}>
      {children}
    </SidebarContext.Provider>
  );
}

export function useSidebarContext() {
  const context = useContext(SidebarContext);
  if (context === undefined) {
    throw new Error('useSidebarContext must be used within a SidebarProvider');
  }
  return context;
} 