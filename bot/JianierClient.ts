import { client } from './tgClient.js';
import { NewMessage } from 'telegram/events/index.js';
import { PrismaClient } from '@prisma/client';
import cron from 'node-cron';
import dotenv from 'dotenv';
import { Api } from 'telegram';

dotenv.config();

const CHANNEL_ID = "-1001664570620";
//const CHANNEL_ID = "5986862132"; //杂七杂八
const SECOND_CHANNEL_ID = "-1001656283267"; // 第二个频道
const prisma = new PrismaClient();

// 存储最近发送的消息ID
let lastStatusMessageId: number | null = null;
let lastCommentsMessageId: number | null = null;
let lastSecondChannelCommentsMessageId: number | null = null;

// 设置时间范围常量（单位：天）
const RECENT_DAYS = 3;

// 检查数据库状态的函数
async function checkDatabaseStatus() {
    try {
        // 定义城市统计信息的接口
        interface CityStats {
            city: string;
            count3k: number;
            count5k: number;
            info?: string;
        }

        // 定义中国主要城市列表
        const chineseCities = [
            '北京', '上海', '广州', '深圳', '杭州', // 一线
            '成都', '重庆', '武汉', '西安', '南京', '长沙', '郑州', '东莞', 
            '青岛', '沈阳', '天津', '苏州', '佛山', '济南', '合肥', '石家庄',
            '福州', '厦门', '哈尔滨', '长春', '昆明', '太原', '南宁', '贵阳',
            '南昌', '温州', '大连', '无锡', '宁波', '常州', '嘉兴', '绍兴'
        ];

        // 计算3天的时间范围
        const now = new Date();
        const startTime = new Date(now.getTime() - (RECENT_DAYS * 24 * 60 * 60 * 1000));

        // 获取3K的统计
        const cityStats3k = await prisma.girl.groupBy({
            by: ['city'],
            _count: {
                _all: true
            },
            where: {
                updatedAt: {
                    gte: startTime,
                    lte: now
                },
                city: {
                    in: chineseCities
                },
                price: "3K"
            }
        });

        // 获取5K的统计
        const cityStats5k = await prisma.girl.groupBy({
            by: ['city'],
            _count: {
                _all: true
            },
            where: {
                updatedAt: {
                    gte: startTime,
                    lte: now
                },
                city: {
                    in: chineseCities
                },
                price: "5K"
            }
        });

        // 创建城市统计映射
        const cityStatsMap = new Map<string, CityStats>();

        // 处理3K数据
        cityStats3k.forEach(stat => {
            if (stat.city) {
                cityStatsMap.set(stat.city, {
                    city: stat.city,
                    count3k: stat._count._all,
                    count5k: 0
                });
            }
        });

        // 处理5K数据
        cityStats5k.forEach(stat => {
            if (stat.city) {
                const existingStat = cityStatsMap.get(stat.city);
                if (existingStat) {
                    existingStat.count5k = stat._count._all;
                } else {
                    cityStatsMap.set(stat.city, {
                        city: stat.city,
                        count3k: 0,
                        count5k: stat._count._all
                    });
                }
            }
        });

        // 定义一线城市
        const tierOneCities = ['北京', '上海', '广州', '杭州', '深圳'];

        // 定义城市拼音映射
        const cityPinyinMap: { [key: string]: string } = {
            '北京': 'beijing',
            '上海': 'shanghai',
            '广州': 'guangzhou',
            '深圳': 'shenzhen',
            '杭州': 'hangzhou',
            '成都': 'chengdu',
            '重庆': 'chongqing',
            '武汉': 'wuhan',
            '西安': 'xian',
            '南京': 'nanjing',
            '长沙': 'changsha',
            '郑州': 'zhengzhou',
            '东莞': 'dongguan',
            '青岛': 'qingdao',
            '沈阳': 'shenyang',
            '天津': 'tianjin',
            '苏州': 'suzhou',
            '佛山': 'foshan',
            '济南': 'jinan',
            '合肥': 'hefei',
            '石家庄': 'shijiazhuang',
            '福州': 'fuzhou',
            '厦门': 'xiamen',
            '哈尔滨': 'haerbin',
            '长春': 'changchun',
            '昆明': 'kunming',
            '太原': 'taiyuan',
            '南宁': 'nanning',
            '贵阳': 'guiyang',
            '南昌': 'nanchang',
            '温州': 'wenzhou',
            '大连': 'dalian',
            '无锡': 'wuxi',
            '宁波': 'ningbo',
            '常州': 'changzhou',
            '嘉兴': 'jiaxing',
            '绍兴': 'shaoxing'
        };

        // 生成最终统计
        const tierOneCityStats: CityStats[] = [];
        const otherCityStats: CityStats[] = [];

        cityStatsMap.forEach((stat) => {
            const cityPinyin = cityPinyinMap[stat.city] || stat.city;
            const cityLink = `<a href="https://jianier.com/city/${cityPinyin}">${stat.city}</a>`;
            
            if (tierOneCities.includes(stat.city)) {
                // 一线城市显示3K和5K
                stat.info = `<b>${cityLink} 3K ${stat.count3k} 位 / 5K ${stat.count5k} 位</b>`;
                tierOneCityStats.push(stat);
            } else if (stat.count3k > 0) {
                // 其他城市只显示3K，简化格式
                stat.info = `${cityLink}${stat.count3k}位`;
                otherCityStats.push(stat);
            }
        });

        // 按人数排序（一线城市按3K+5K排序，其他城市只按3K排序）
        const sortByTotal = (a: CityStats, b: CityStats) => {
            if (tierOneCities.includes(a.city) && tierOneCities.includes(b.city)) {
                // 一线城市比较总数
                return (b.count3k + b.count5k) - (a.count3k + a.count5k);
            } else {
                // 其他城市只比较3K
                return b.count3k - a.count3k;
            }
        };

        tierOneCityStats.sort(sortByTotal);
        otherCityStats.sort(sortByTotal);

        // 获取总数统计
        const totalGirls = await prisma.girl.count({
            where: {
                updatedAt: {
                    gte: startTime,
                    lte: now
                }
            }
        });

        // 获取历史累计总数
        const historicalTotal = await prisma.girl.count();

        // 构建消息
        const message = `<b>佳妮 ☀️ 俱乐部 营业中</b>

<b>当前资源库 ${totalGirls} 位女孩在线中</b>

${tierOneCityStats.map(stat => stat.info).join('\n')}

<b>${otherCityStats.map(stat => stat.info!).join(' ')}</b>

<b>历史累计已更新 ${historicalTotal} 份资料</b>

<b>入会后 立刻解锁 全网最强资源库！</b>

<b>❤️‍🔥 <a href="https://jianier.com/city">浏览资源库</a></b>

<b>📙 <a href="https://jianier.com/guide">约会指南</a></b>

<b>😻 <a href="https://t.me/JianierSelected">佳妮☀️朋友圈(过往反馈)</a></b>

<b>💬 入会&客服：@Jianierbot</b>`;

        return message;
    } catch (error) {
        console.error('获取数据库状态失败:', error);
        return '获取数据库状态时出错';
    }
}

// 发送状态到频道
async function sendStatusToChannel() {
    try {
        // 如果有之前发送的状态消息，先删除
        if (lastStatusMessageId !== null) {
            try {
                await client.deleteMessages(CHANNEL_ID, [lastStatusMessageId], {});
                console.log('Previous status message deleted successfully');
            } catch (error) {
                console.error('Error deleting previous status message:', error);
            }
        }

        const status = await checkDatabaseStatus();
        const message = await client.sendFile(CHANNEL_ID, {
            file: './bot/zzk1.mov',
            caption: status,
            parseMode: 'html',
            forceDocument: false,
            attributes: [
                new Api.DocumentAttributeVideo({
                    duration: 51,
                    w: 1684,
                    h: 1080,
                    supportsStreaming: true
                })
            ]
        });

        // 保存最近发送的状态消息ID
        lastStatusMessageId = message.id;
        console.log('Status message sent successfully, ID:', lastStatusMessageId);
    } catch (error) {
        console.error('Error sending status message:', error);
    }
}

// 获取最新评价的函数
async function getLatestComments() {
    try {
        // 获取所有有评价的记录总数
        const totalCommentsCount = await prisma.girl.count({
            where: {
                comments: {
                    not: null,
                    notIn: [""]
                }
            }
        });

        // 如果没有评价，直接返回
        if (totalCommentsCount === 0) {
            return "暂无评价";
        }

        // 随机选择5条评价
        // 使用Prisma的skip和take实现随机选择
        // 为了避免性能问题，我们先获取所有符合条件的ID，然后随机选择5个
        const allGirlsWithComments = await prisma.girl.findMany({
            where: {
                comments: {
                    not: null,
                    notIn: [""]
                }
            },
            select: {
                id: true
            }
        });

        // 随机打乱数组并选择前5个
        const shuffledIds = allGirlsWithComments
            .map(girl => girl.id)
            .sort(() => Math.random() - 0.5)
            .slice(0, 5);

        // 获取这5个ID对应的完整记录
        const randomGirlsWithComments = await prisma.girl.findMany({
            where: {
                id: {
                    in: shuffledIds
                }
            },
            select: {
                girl_id: true,
                comments: true,
                city: true
            }
        });

        if (randomGirlsWithComments.length === 0) {
            return "暂无评价";
        }

        // 构建评价消息
        let commentsMessage = `<b>佳妮 ☀️ 俱乐部 - 精选体验反馈</b>\n\n`;
        
        randomGirlsWithComments.forEach((girl, index) => {
            // 添加序号和城市信息，以及评价内容，全部在一行
            let commentLine = `<b>${index + 1}.</b>`;
            
            // 添加城市信息（如果有）
            if (girl.city) {
                commentLine += ` <i>(${girl.city})</i>`;
            }
            
            // 添加评价内容，使其可点击跳转到对应网页
            commentLine += ` <a href="https://jianier.com/girl/${girl.girl_id}"><b>${girl.comments}</b></a>`;
            
            // 添加到消息中
            commentsMessage += `${commentLine}\n\n`;
        });
        
        // 添加底部信息
        commentsMessage += `\n`;
        commentsMessage += `<b>入会后 立刻解锁 全网最强资源库！</b>\n\n`;
        commentsMessage += `<b>❤️‍🔥 <a href="https://jianier.com/city">浏览资源库</a></b>\n\n`;
        commentsMessage += `<b>📙 <a href="https://jianier.com/guide">约会指南</a></b>\n\n`;
        commentsMessage += `<b>😻 <a href="https://t.me/JianierSelected">佳妮☀️朋友圈(过往反馈)</a></b>\n\n`;
        commentsMessage += `<b>💬 入会&客服：@Jianierbot</b>`;

        return commentsMessage;
    } catch (error) {
        console.error('获取最新评价失败:', error);
        return '获取最新评价时出错';
    }
}

// 发送最新评价到频道
async function sendLatestCommentsToChannel() {
    try {
        const commentsMessage = await getLatestComments();
        
        // 发送到第一个频道
        // 如果有之前发送的评价消息，先删除
        if (lastCommentsMessageId !== null) {
            try {
                await client.deleteMessages(CHANNEL_ID, [lastCommentsMessageId], {});
                console.log('Previous comments message deleted successfully from primary channel');
            } catch (error) {
                console.error('Error deleting previous comments message from primary channel:', error);
            }
        }
        
        // 使用sendFile方法发送图片和消息到第一个频道
        const message = await client.sendFile(CHANNEL_ID, {
            file: './bot/zzk2.png',
            caption: commentsMessage,
            parseMode: 'html'
        });

        // 保存最近发送的评价消息ID
        lastCommentsMessageId = message.id;
        console.log('Latest comments message with image sent successfully to primary channel, ID:', lastCommentsMessageId);
        
        // 发送到第二个频道
        // 如果有之前发送的评价消息，先删除
        if (lastSecondChannelCommentsMessageId !== null) {
            try {
                await client.deleteMessages(SECOND_CHANNEL_ID, [lastSecondChannelCommentsMessageId], {});
                console.log('Previous comments message deleted successfully from second channel');
            } catch (error) {
                console.error('Error deleting previous comments message from second channel:', error);
            }
        }
        
        // 使用sendFile方法发送图片和消息到第二个频道
        const secondMessage = await client.sendFile(SECOND_CHANNEL_ID, {
            file: './bot/zzk2.png',
            caption: commentsMessage,
            parseMode: 'html'
        });

        // 保存最近发送的评价消息ID
        lastSecondChannelCommentsMessageId = secondMessage.id;
        console.log('Latest comments message with image sent successfully to second channel, ID:', lastSecondChannelCommentsMessageId);
    } catch (error) {
        console.error('Error sending latest comments message with image:', error);
    }
}

// 开始定时发送任务
function startScheduledMessages() {

    // 立即发送第一条评价消息
    sendLatestCommentsToChannel();

    // 立即发送第一条状态消息
    sendStatusToChannel();
    
    // 使用node-cron设置定时任务
    // 每天上午10点发送状态消息
    cron.schedule('0 10 * * *', () => {
        console.log('执行每日状态消息发送任务');
        sendStatusToChannel();
    });
    
    // 每3小时发送一次评价消息（每天0点、3点、6点、9点、12点、15点、18点、21点）
    cron.schedule('0 */3 * * *', () => {
        console.log('执行评价消息发送任务');
        sendLatestCommentsToChannel();
    });
    
    console.log('定时任务已设置：状态消息每天10点发送，评价消息每3小时发送一次');
}

// 启动机器人
async function startBot() {
    try {
        await client.connect();
        
        console.log('Telegram 机器人已启动');

        // 启动定时发送任务
        startScheduledMessages();
        console.log('定时发送任务已启动');

    } catch (error) {
        console.error('Error starting bot:', error);
    }
}

// 启动机器人
startBot();