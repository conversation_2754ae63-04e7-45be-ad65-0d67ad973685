import { TelegramClient } from "telegram";
import { StringSession } from "telegram/sessions/index.js";
import * as dotenv from "dotenv";
import readline from "readline";

dotenv.config();

function throwMissingEnvError(envVar: string): never {
    throw new Error(`Missing required environment variable: ${envVar}`);
}

const apiId: number = parseInt(process.env.TG_API_ID ?? throwMissingEnvError('TG_API_ID')); 
const apiHash: string = process.env.TG_API_HASH ?? throwMissingEnvError('TG_API_HASH'); 
const stringSession = new StringSession(process.env.TG_SESSION_STRING ?? ""); 

export const client = new TelegramClient(stringSession, apiId, apiHash, {
    connectionRetries: 5,
});

export async function startClient() {
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout,
    });

    await client.start({
        phoneNumber: async () =>
            new Promise((resolve) =>
                rl.question("Please enter your number: ", resolve)
            ),
        password: async () =>
            new Promise((resolve) =>
                rl.question("Please enter your password: ", resolve)
            ),
        phoneCode: async () =>
            new Promise((resolve) =>
                rl.question("Please enter the code you received: ", resolve)
            ),
        onError: (err) => console.log(err),
    });

    console.log("Connected to Telegram Client.");
    rl.close();
}