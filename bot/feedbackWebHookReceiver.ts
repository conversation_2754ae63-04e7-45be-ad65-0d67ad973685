import { NextApiRequest, NextApiResponse } from 'next';
import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { PrismaClient, Prisma } from '@prisma/client';
import { Queue } from 'bullmq';
import IORedis from 'ioredis';
import { 
  Client,
  isFullBlock,
  APIErrorCode,
  ClientErrorCode,
  APIResponseError,
} from '@notionhq/client';
import type { 
  BlockObjectResponse,
  RichTextItemResponse,
  PartialBlockObjectResponse,
} from '@notionhq/client/build/src/api-endpoints';
import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import sharp from 'sharp';

// 加载环境变量
dotenv.config();

// 图片保存配置
const UPLOAD_DIR = path.join(process.cwd(), 'public', 'uploads');
const PUBLIC_URL = process.env.PUBLIC_URL || 'https://webhook1.jianier.com';

// 确保上传目录存在
if (!fs.existsSync(UPLOAD_DIR)) {
  fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}

// 初始化 Notion 客户端
const notion = new Client({
  auth: process.env.NOTION_API_KEY,
});

// 初始化 Prisma 客户端
const prisma = new PrismaClient();

// 初始化队列
const GirlsNoteQueue = new Queue('NewGirlPusher', {
  connection: new IORedis(),
  defaultJobOptions: {
    attempts: 25,
    backoff: 3000
  }
});

// 定义数据类型
interface WebhookSource {
  type: string;
  automation_id: string;
  action_id: string;
  event_id: string;
  attempt: number;
}

interface User {
  object: string;
  id: string;
}

interface Parent {
  type: string;
  database_id: string;
}

interface MultiSelectItem {
  id: string;
  name: string;
  color: string;
}

interface RichTextContent {
  content: string;
  link: string | null;
}

interface RichTextAnnotations {
  bold: boolean;
  italic: boolean;
  strikethrough: boolean;
  underline: boolean;
  code: boolean;
  color: string;
}

interface RichTextItem {
  type: string;
  text: RichTextContent;
  annotations: RichTextAnnotations;
  plain_text: string;
  href: string | null;
}

interface DateProperty {
  start: string;
  end: string | null;
  time_zone: string | null;
}

interface Status {
  id: string;
  name: string;
  color: string;
}

interface Properties {
  城市: {
    id: string;
    type: string;
    multi_select: MultiSelectItem[];
  };
  类型: {
    id: string;
    type: string;
    multi_select: MultiSelectItem[];
  };
  备注: {
    id: string;
    type: string;
    rich_text: RichTextItem[];
  };
  费用: {
    id: string;
    type: string;
    number: number;
  };
  'S?': {
    id: string;
    type: string;
    checkbox: boolean;
  };
  Created: {
    id: string;
    type: string;
    created_time: string;
  };
  什么单: {
    id: string;
    type: string;
    multi_select: MultiSelectItem[];
  };
  盯单: {
    id: string;
    type: string;
    multi_select: MultiSelectItem[];
  };
  方式: {
    id: string;
    type: string;
    multi_select: MultiSelectItem[];
  };
  Time: {
    id: string;
    type: string;
    date: DateProperty;
  };
  Status: {
    id: string;
    type: string;
    status: Status;
  };
  M: {
    id: string;
    type: string;
    multi_select: MultiSelectItem[];
  };
  事项: {
    id: string;
    type: string;
    title: RichTextItem[];
  };
  [key: string]: any;
}

interface WebhookData {
  object: string;
  id: string;
  created_time: string;
  last_edited_time: string;
  created_by: User;
  last_edited_by: User;
  cover: null;
  icon: null;
  parent: Parent;
  archived: boolean;
  in_trash: boolean;
  properties: Properties;
  url: string;
  public_url: string | null;
  request_id: string;
}

interface WebhookPayload {
  source: WebhookSource;
  data: WebhookData;
}

// Express 应用配置
const app = express();
const PORT = process.env.WEBHOOK_PORT || 3003;

// 中间件配置
app.use(express.json());
app.use(cors());

// 配置静态文件服务
app.use('/uploads', express.static(UPLOAD_DIR));

// 定义 Notion 块的类型
type NotionBlockType = BlockObjectResponse | PartialBlockObjectResponse;

// 定义图片块类型
interface NotionImageBlock {
  type: 'image';
  image: {
    type: 'external' | 'file';
    external?: { url: string };
    file?: { url: string; expiry_time: string };
    caption: RichTextItemResponse[];
  };
}

// 添加获取 Notion 页面内容的函数
async function getNotionPageContent(pageId: string) {
  try {
    // 检查页面ID格式
    if (!pageId || !pageId.match(/^[a-zA-Z0-9-]+$/)) {
      throw new Error('Invalid page ID format');
    }

    // 尝试获取页面内容
    let page;
    try {
      page = await notion.pages.retrieve({
        page_id: pageId,
      });
    } catch (error) {
      if (error instanceof APIResponseError) {
        if (error.code === APIErrorCode.ObjectNotFound) {
          console.error(`Notion integration permissions error: Page ${pageId} not accessible`);
          console.error('Please ensure:');
          console.error('1. The Notion integration is added to the page');
          console.error('2. The page is shared with the integration');
          console.error('3. The integration has the correct capabilities enabled');
          throw new Error(`Notion page ${pageId} not accessible - check integration permissions`);
        }
      }
      throw error;
    }

    // 获取页面块内容
    let blocks;
    try {
      blocks = await notion.blocks.children.list({
        block_id: pageId,
        page_size: 100,
      });
    } catch (error) {
      console.error(`Error fetching blocks for page ${pageId}:`, error);
      blocks = { results: [] };
    }

    // 处理块内容
    const processedBlocks = await Promise.all(
      blocks.results.map(async (block) => {
        if ('has_children' in block && block.has_children) {
          try {
            const children = await notion.blocks.children.list({
              block_id: block.id,
              page_size: 50,
            });
            return {
              ...block,
              children: children.results,
            };
          } catch (error) {
            console.error(`Error fetching children for block ${block.id}:`, error);
            return block;
          }
        }
        return block;
      })
    );

    // 提取文本内容
    const extractTextContent = (block: NotionBlockType): string => {
      if (!('type' in block)) return '';
      
      const getRichTextContent = (richText: RichTextItemResponse[]): string => {
        return richText.map(text => text.plain_text).join('');
      };

      switch (block.type) {
        case 'paragraph':
          return getRichTextContent(block.paragraph.rich_text);
        case 'heading_1':
          return getRichTextContent(block.heading_1.rich_text);
        case 'heading_2':
          return getRichTextContent(block.heading_2.rich_text);
        case 'heading_3':
          return getRichTextContent(block.heading_3.rich_text);
        case 'bulleted_list_item':
          return getRichTextContent(block.bulleted_list_item.rich_text);
        case 'numbered_list_item':
          return getRichTextContent(block.numbered_list_item.rich_text);
        case 'to_do':
          return getRichTextContent(block.to_do.rich_text);
        case 'toggle':
          return getRichTextContent(block.toggle.rich_text);
        case 'quote':
          return getRichTextContent(block.quote.rich_text);
        default:
          return '';
      }
    };

    // 提取图片内容
    const extractImageUrls = (blocks: NotionBlockType[]): string[] => {
      return blocks
        .filter((block): block is BlockObjectResponse => 'type' in block && block.type === 'image')
        .map(block => {
          const image = (block as unknown as NotionImageBlock).image;
          if (image.type === 'external') {
            return image.external?.url || '';
          } else if (image.type === 'file') {
            return image.file?.url || '';
          }
          return '';
        })
        .filter(url => url !== '');
    };

    // 提取所有文本内容
    const textContent = processedBlocks
      .map(block => extractTextContent(block))
      .filter(text => text)
      .join('\n');

    // 提取所有图片 URL
    const imageUrls = extractImageUrls(processedBlocks);

    return {
      page,
      blocks: processedBlocks,
      textContent,
      imageUrls,
    };
  } catch (error) {
    console.error('Error fetching Notion page content:', error);
    throw error;
  }
}

// 添加水印函数
async function addWatermark(imagePath: string): Promise<string> {
  try {
    // 读取图片
    const imageBuffer = await fs.promises.readFile(imagePath);
    const image = sharp(imageBuffer);
    
    // 获取图片信息
    const metadata = await image.metadata();
    const width = metadata.width || 800;
    const height = metadata.height || 600;
    
    // 创建水印文字的 SVG
    const watermarkText = '@JianierClub';
    const fontSize = Math.min(width, height) / 6; // 更大的字体大小（从1/8改为1/6）
    
    // 创建单个大水印文字的 SVG，使用 rgba 来控制透明度
    const svg = `
      <svg width="${width}" height="${height}">
        <style>
          .watermark {
            font-family: Arial, sans-serif;
            font-size: ${fontSize}px;
            font-weight: bold;
            fill: rgba(255, 255, 255, 0.4); /* 降低透明度（从0.3改为0.4） */
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
          }
        </style>
        <text class="watermark" x="50%" y="50%" text-anchor="middle" dominant-baseline="middle" transform="rotate(-45, ${width/2}, ${height/2})">${watermarkText}</text>
      </svg>
    `;

    // 生成新的文件名
    const ext = path.extname(imagePath);
    const watermarkedPath = imagePath.replace(ext, `_watermarked${ext}`);

    // 添加水印并保存
    await image
      .composite([
        {
          input: Buffer.from(svg),
          blend: 'over' as const,
          gravity: 'center',
        }
      ])
      .toFile(watermarkedPath);

    return watermarkedPath;
  } catch (error) {
    console.error('Error adding watermark:', error);
    throw error;
  }
}

// 修改下载图片函数
async function downloadImage(url: string): Promise<string> {
  try {
    const response = await axios({
      url,
      method: 'GET',
      responseType: 'arraybuffer'
    });

    // 获取文件扩展名
    const contentType = response.headers['content-type'];
    const ext = contentType.split('/')[1];
    
    // 生成唯一文件名
    const fileName = `${uuidv4()}.${ext}`;
    const filePath = path.join(UPLOAD_DIR, fileName);

    // 保存原始文件
    await fs.promises.writeFile(filePath, response.data);

    // 添加水印
    const watermarkedPath = await addWatermark(filePath);

    // 删除原始文件
    await fs.promises.unlink(filePath);

    // 返回公开访问URL
    const watermarkedFileName = path.basename(watermarkedPath);
    return `${PUBLIC_URL}/uploads/${watermarkedFileName}`;
  } catch (error) {
    console.error('Error downloading image:', error);
    throw error;
  }
}

// 处理图片下载和更新
async function processImages(imageUrls: string[]): Promise<string[]> {
  try {
    // 下载所有图片
    const downloadPromises = imageUrls.map(url => downloadImage(url));
    const localUrls = await Promise.all(downloadPromises);
    return localUrls;
  } catch (error) {
    console.error('Error processing images:', error);
    throw error;
  }
}

export async function handleWebhook(req: NextApiRequest | express.Request, res: NextApiResponse | express.Response) {
  console.log('Webhook received');

  console.log(req.body);
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const payload = req.body as WebhookPayload;
    const { properties } = payload.data;

    // 转换所有属性为简单的 JSON 格式
    const processedProperties: Record<string, any> = {};

    for (const [key, value] of Object.entries(properties)) {
      if (!value || !value.type) {
        processedProperties[key] = value;
        continue;
      }

      switch (value.type) {
        case 'multi_select':
          processedProperties[key] = Array.isArray(value.multi_select) 
            ? value.multi_select.map((item: MultiSelectItem) => item.name).filter(Boolean)
            : [];
          break;
        case 'rich_text':
          processedProperties[key] = Array.isArray(value.rich_text)
            ? value.rich_text.map((item: RichTextItem) => item.plain_text).filter(Boolean).join(' ')
            : '';
          break;
        case 'number':
          processedProperties[key] = value.number;
          break;
        case 'checkbox':
          processedProperties[key] = value.checkbox;
          break;
        case 'created_time':
          processedProperties[key] = value.created_time;
          break;
        case 'date':
          processedProperties[key] = value.date;
          break;
        case 'status':
          processedProperties[key] = value.status ? value.status.name : null;
          break;
        case 'title':
          processedProperties[key] = Array.isArray(value.title)
            ? value.title.map((item: RichTextItem) => item.plain_text).filter(Boolean).join(' ')
            : '';
          break;
        default:
          processedProperties[key] = value;
      }
    }

    // 获取 Girl ID（去掉 # 符号）
    const girlId = processedProperties.Girld_ID.replace('#', '');

    // 获取 Notion 页面内容
    const pageContent = await getNotionPageContent(payload.data.id);
    console.log('Notion page content:', {
      ...pageContent,
      imageUrls: pageContent.imageUrls,
    });

    // 处理图片
    let localImageUrls: string[] = [];
    if (pageContent.imageUrls && pageContent.imageUrls.length > 0) {
      localImageUrls = await processImages(pageContent.imageUrls);
      console.log('Processed images:', localImageUrls);
    }

    // 更新数据库中的评分信息
    try {
      // 先获取当前的 girl 数据
      const currentGirl = await prisma.girl.findUnique({
        where: {
          girl_id: girlId
        },
        select: {
          media: true
        }
      });

      // 合并新旧图片数组，新图片放在开头
      const updatedMedia = currentGirl?.media || [];
      const newMedia = [...localImageUrls, ...updatedMedia];

      const updateData: Prisma.GirlUpdateInput = {
        pusher: typeof processedProperties.Pusher === 'string' ? processedProperties.Pusher : undefined,
        photoAccuracy: processedProperties['人照合一(分)'] ? Number(processedProperties['人照合一(分)']) : undefined,
        appearance: processedProperties['颜值(分)'] ? Number(processedProperties['颜值(分)']) : undefined,
        attitude: processedProperties['服务态度(分)'] ? Number(processedProperties['服务态度(分)']) : undefined,
        serviceQuality: processedProperties['技巧技术(分)'] ? Number(processedProperties['技巧技术(分)']) : undefined,
        overallRating: processedProperties['整体评价(分)'] ? Number(processedProperties['整体评价(分)']) : undefined,
        comments: processedProperties['客户评价'] || undefined,
        notes: processedProperties['备注'] || undefined,
        media: localImageUrls.length > 0 ? {
          set: newMedia
        } : undefined
      };

      // 更新 girl 数据
      const updatedGirl = await prisma.girl.update({
        where: {
          girl_id: girlId
        },
        data: updateData
      });

      // 构造新的笔记
      const newNote = {
        id: updatedGirl.id,
        girl_id: updatedGirl.girl_id,
        user_id: updatedGirl.user_id || "",
        title: updatedGirl.title,
        feature: updatedGirl.feature,
        city: updatedGirl.city,
        district: updatedGirl.district || "",
        height: updatedGirl.height || "",
        age: updatedGirl.age || "",
        weight: updatedGirl.weight || "",
        cup: updatedGirl.cup || "",
        price: updatedGirl.price,
        type: updatedGirl.type,
        service: updatedGirl.service,
        media: updatedGirl.media,
        pusher: "JIANIER",
        message: updatedGirl.message,
        description: updatedGirl.description || "",
        photoAccuracy: updatedGirl.photoAccuracy,
        appearance: updatedGirl.appearance,
        attitude: updatedGirl.attitude,
        serviceQuality: updatedGirl.serviceQuality,
        overallRating: updatedGirl.overallRating,
        comments: updatedGirl.comments,
        notes: updatedGirl.notes || ""
      };

      try {
        // 生成随机ID
        const randomId = Math.floor(Math.random() * 1000000) + 1;
        
        // 添加到队列，而不是直接发送到远端服务器
        await GirlsNoteQueue.add(`task_${randomId}`, newNote);
        
        console.log('Successfully added data to queue:', `task_${randomId}`);
      } catch (error) {
        console.error('Error adding data to queue:', error);
        throw error;
      }

      console.log('Updated girl data and added to queue:', updatedGirl);
      
      return res.status(200).json({
        message: 'Webhook received, girl data updated and added to queue successfully',
        data: {
          id: payload.data.id,
          created_time: payload.data.created_time,
          last_edited_time: payload.data.last_edited_time,
          created_by: payload.data.created_by.id,
          properties: processedProperties,
          source: {
            automation_id: payload.source.automation_id,
            event_id: payload.source.event_id,
          }
        },
        updatedGirl,
        localImageUrls
      });
    } catch (dbError) {
      console.error('Error updating girl data:', dbError);
      return res.status(404).json({
        message: 'Girl not found or database error',
        error: dbError instanceof Error ? dbError.message : 'Unknown database error',
        data: {
          id: payload.data.id,
          properties: processedProperties,
          source: {
            automation_id: payload.source.automation_id,
            event_id: payload.source.event_id,
          }
        }
      });
    }
  } catch (error) {
    console.error('Error processing webhook:', error);
    return res.status(500).json({
      message: 'Error processing webhook',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

// 配置路由
app.post('/webhook', handleWebhook);

// 健康检查端点
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 启动服务器

app.listen(PORT, () => {
    console.log(`Webhook server is running on port ${PORT}`);
    console.log(`Health check endpoint: http://localhost:${PORT}/health`);
    console.log(`Webhook endpoint: http://localhost:${PORT}/webhook`);
  });


export default handleWebhook;
