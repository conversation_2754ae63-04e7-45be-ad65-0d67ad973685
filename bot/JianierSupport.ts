import { Telegraf } from 'telegraf';
import { PrismaClient } from '@prisma/client';
import { Message, User } from 'telegraf/types';

// 定义用户类型
type TelegramUser = {
    id: number;
    username: string;
    first_name?: string;
};

// 定义消息类型
interface ForwardedMessage extends Message.TextMessage {
    forward_from?: User;
    forward_sender_name?: string;
}

class TelegramBot {
    private bot: Telegraf;
    private prisma: PrismaClient;
    private users: TelegramUser[];
    private readonly token: string;
    private readonly adminAccount: string;
    private readonly keyboard_markup: any;
    private readonly navigation_markup: any;
    private readonly videoFileId: string = "BAACAgUAAxkBAAJ6LmdQXfQJOrYhZZ1fmjc_OaVNgRsAA8sSAALY7IlWJkJI2d5FXD42BA"; // 预先上传的视频文件ID

    constructor(token: string, adminAccount: string) {
        this.token = token;
        this.adminAccount = adminAccount;
        this.bot = new Telegraf(this.token);
        this.prisma = new PrismaClient();
        this.users = [];
        this.keyboard_markup = {
            keyboard: [
                [{ text: '☀️ 导航' }]
            ],
            resize_keyboard: true,
            one_time_keyboard: false,
            input_field_placeholder: "直接发消息我可以看到"
        };
        this.navigation_markup = {
            inline_keyboard: [
                [{
                    text: "全网最强❤️‍🔥实时更新资源库",
                    web_app: { url: "https://jianier.com/city" }
                }],
                [{
                    text: "📙 约会指南",
                    web_app: { url: "https://jianier.com/guide" }
                }],
                [{
                    text: "📝 免费注册",
                    web_app: { url: "https://jianier.com/register" }
                }],
                [{
                    text: "✈️ 电报主群",
                    url: "https://t.me/jianierclub"
                }],
                [{
                    text: "👩‍💼 联系佳妮",
                    url: "https://t.me/Jianier1314"
                }]
            ]
        };
        this.initializeHandlers();
    }

    private sendNavigationMessage(ctx: any) {
        return ctx.reply(`<b>佳妮 ☀️ 导航</b>`, {
            parse_mode: "HTML",
            reply_markup: {
                ...this.navigation_markup,
                input_field_placeholder: "直接发消息我可以看到"
            }
        });
    }

    private async sendWelcomeMessages(ctx: any): Promise<void> {

        await new Promise(resolve => setTimeout(resolve, 1000));
        await ctx.reply('你来了，宝。', {
            parse_mode: "HTML",
            reply_markup: this.keyboard_markup
        });

        await new Promise(resolve => setTimeout(resolve, 1000));
        await ctx.reply('终于等到你了。', {
            parse_mode: "HTML",
            reply_markup: this.keyboard_markup
        });


        // 延迟1秒后发送服务介绍
        await new Promise(resolve => setTimeout(resolve, 1000));
        await ctx.reply('<b>这里是佳妮 ☀️ 高端真人线下约会</b>', {
            parse_mode: "HTML",
            reply_markup: this.keyboard_markup
        });

        // 延迟1.5秒后发送俱乐部介绍
        await new Promise(resolve => setTimeout(resolve, 1500));
        await ctx.reply('<b>俱乐部旨在为高素质的客人，提供高端私密线下约会服务。主营全国一二线城市，拥有10000+优质模特资源。</b>', {
            parse_mode: "HTML",
            reply_markup: this.keyboard_markup
        });

        // 发送视频
        await new Promise(resolve => setTimeout(resolve, 1000));
        try {
            await ctx.telegram.sendVideo(ctx.chat.id, this.videoFileId, {
                reply_markup: this.keyboard_markup
            });
        } catch (error) {
            console.error('Error sending video:', error);
        }

        // 延迟1.5秒后发送最后的提示
        await new Promise(resolve => setTimeout(resolve, 1000));
        await ctx.reply('<b>下面是俱乐部导航</b>', {
            parse_mode: "HTML",
            reply_markup: this.keyboard_markup
        });

        // 延迟1秒后发送导航
        await new Promise(resolve => setTimeout(resolve, 1000));
        await this.sendNavigationMessage(ctx);

        // 延迟1秒后发送最后的提示
        await new Promise(resolve => setTimeout(resolve, 1000));
        await ctx.reply('<b>直接通过机器人发消息 我可以看到</b>', {
            parse_mode: "HTML",
            reply_markup: this.keyboard_markup
        });

        // 延迟1秒后发送最后的提示
        await new Promise(resolve => setTimeout(resolve, 1000));
        await ctx.reply('<b>入会 请直接发送"入会"</b>', {
            parse_mode: "HTML",
            reply_markup: this.keyboard_markup
        });
    }

    private initializeHandlers(): void {
        // 处理 /start 命令
        this.bot.command('start', async (ctx) => {
            // 如果不是管理员，则转发 /start 命令给管理员
            if (ctx.message && ctx.message.from && ctx.message.from.id !== +this.adminAccount) {
                await ctx.forwardMessage(this.adminAccount);
            }
            await this.sendWelcomeMessages(ctx);
        });

        // 处理导航按钮
        this.bot.hears('☀️ 导航', async (ctx) => {
            // 如果不是管理员，则转发导航请求给管理员
            if (ctx.message && ctx.message.from && ctx.message.from.id !== +this.adminAccount) {
                await ctx.forwardMessage(this.adminAccount);
            }
            this.sendNavigationMessage(ctx);
        });

        // 处理所有消息
        this.bot.on('message', async (ctx) => {
            const msg = ctx.message as Message.TextMessage | Message.PhotoMessage | Message.VideoMessage;

            if (!msg || !msg.from) return;

            // 忽略超级群组消息
            if (ctx.chat?.type === "supergroup") {
                return;
            }

            try {
                // 处理非管理员消息
                if (msg.from.id !== +this.adminAccount) {
                    // 更新用户信息
                    const user: TelegramUser = {
                        id: msg.from.id,
                        username: msg.from.first_name || msg.from.username || 'Unknown',
                        first_name: msg.from.first_name
                    };

                    const index = this.users.findIndex((u) => u.id === user.id);
                    if (index !== -1) {
                        this.users.splice(index, 1);
                    }
                    this.users.push(user);

                    // 如果不是命令消息，才转发（因为命令已在各自的处理器中转发）
                    // 检查消息是否为文本消息且以 '/' 开头
                    const isCommand = 'text' in msg && msg.text.startsWith('/');
                    // 检查消息是否为导航按钮
                    const isNavigationButton = 'text' in msg && msg.text === '☀️ 导航';
                    
                    // 如果不是已处理的命令，则转发
                    if (!isCommand && !isNavigationButton) {
                        await ctx.forwardMessage(this.adminAccount);
                    }

                    return;
                }

                // 处理管理员回复
                if ('reply_to_message' in msg) {
                    const replyMsg = msg.reply_to_message as ForwardedMessage;
                    if (!replyMsg) return;

                    let toUserId: number | undefined;

                    if (replyMsg.forward_from) {
                        toUserId = replyMsg.forward_from.id;
                    } else if (replyMsg.forward_sender_name) {
                        const foundUser = this.users.find((u) =>
                            u.username === replyMsg.forward_sender_name ||
                            u.first_name === replyMsg.forward_sender_name
                        );
                        toUserId = foundUser?.id;
                    }

                    if (!toUserId) {
                        console.log('Target user not found');
                        return;
                    }

                    // 发送回复消息
                    try {
                        if ('photo' in msg) {
                            const photo = msg.photo[msg.photo.length - 1];
                            await this.bot.telegram.sendPhoto(toUserId, photo.file_id);
                            console.log('Photo sent successfully');
                        } else if ('text' in msg) {
                            await this.bot.telegram.sendMessage(toUserId, msg.text, {
                                parse_mode: "HTML",
                                reply_markup: this.keyboard_markup
                            });
                            console.log('Message sent successfully');
                        }
                    } catch (error) {
                        console.error('Error sending reply:', error);
                    }
                }
            } catch (error) {
                console.error(`Error processing message: ${error}`);
            }
        });
    }

    public async start(): Promise<void> {
        try {
            await this.bot.launch();
            console.log('Bot started successfully');
            console.log('Scheduled messages started');
        } catch (error) {
            console.error('Error starting bot:', error);
        }

        // Enable graceful stop
        process.once('SIGINT', () => this.bot.stop('SIGINT'));
        process.once('SIGTERM', () => this.bot.stop('SIGTERM'));
    }
}

// 创建并启动机器人
const bot = new TelegramBot(
    "**********************************************",
    "5986862132"
);
bot.start();
