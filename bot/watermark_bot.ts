import { Telegraf, Context } from 'telegraf';
import { InputMediaPhoto } from 'telegraf/types';
import { message } from 'telegraf/filters';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import axios from 'axios';
import sharp from 'sharp';

// 配置信息
const BOT_TOKEN = '7582707478:AAE8PIAxK9koVcvK0MLWTLsTyL_TBsTQgmU';

// 创建机器人实例
const bot = new Telegraf(BOT_TOKEN);

// 临时目录
const TEMP_DIR = path.join(os.tmpdir(), 'watermark-bot');
if (!fs.existsSync(TEMP_DIR)) {
  fs.mkdirSync(TEMP_DIR, { recursive: true });
}

// 添加水印函数
async function addWatermark(imagePath: string): Promise<string> {
  try {
    // 读取图片
    const imageBuffer = await fs.promises.readFile(imagePath);
    const image = sharp(imageBuffer);
    
    // 获取图片信息
    const metadata = await image.metadata();
    const width = metadata.width || 800;
    const height = metadata.height || 600;
    
    // 创建水印文字的 SVG
    const watermarkText = 'Jianier.com';
    const fontSize = Math.min(width, height) / 6; // 更大的字体大小（从1/8改为1/6）
    
    // 创建单个大水印文字的 SVG，使用 rgba 来控制透明度
    const svg = `
      <svg width="${width}" height="${height}">
        <style>
          .watermark {
            font-family: Arial, sans-serif;
            font-size: ${fontSize}px;
            font-weight: bold;
            fill: rgba(255, 255, 255, 0.4); /* 降低透明度（从0.3改为0.4） */
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
          }
        </style>
        <text class="watermark" x="50%" y="50%" text-anchor="middle" dominant-baseline="middle" transform="rotate(-45, ${width/2}, ${height/2})">${watermarkText}</text>
      </svg>
    `;

    // 生成新的文件名
    const ext = path.extname(imagePath);
    const watermarkedPath = imagePath.replace(ext, `_watermarked${ext}`);

    // 添加水印并保存
    await image
      .composite([
        {
          input: Buffer.from(svg),
          blend: 'over' as const,
          gravity: 'center',
        }
      ])
      .toFile(watermarkedPath);

    return watermarkedPath;
  } catch (error) {
    console.error('Error adding watermark:', error);
    throw error;
  }
}

// 下载文件函数
async function downloadFile(fileId: string, bot: Telegraf): Promise<string> {
  try {
    // 获取文件信息
    const fileInfo = await bot.telegram.getFile(fileId);
    if (!fileInfo || !fileInfo.file_path) {
      throw new Error('无法获取文件信息');
    }

    // 获取文件 URL
    const fileUrl = `https://api.telegram.org/file/bot${BOT_TOKEN}/${fileInfo.file_path}`;
    
    // 创建临时文件路径
    const fileName = `${Date.now()}_${path.basename(fileInfo.file_path)}`;
    const localFilePath = path.join(TEMP_DIR, fileName);
    
    // 下载文件
    const response = await axios({
      method: 'GET',
      url: fileUrl,
      responseType: 'stream'
    });

    const writer = fs.createWriteStream(localFilePath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', () => resolve(localFilePath));
      writer.on('error', reject);
    });
  } catch (error) {
    console.error('下载文件时出错:', error);
    throw error;
  }
}

// 处理单张图片
async function handleSinglePhoto(ctx: Context, fileId: string): Promise<void> {
  try {
    await ctx.reply('正在处理图片，请稍候...');
    
    // 下载图片
    const imagePath = await downloadFile(fileId, bot);
    
    // 添加水印
    const watermarkedImagePath = await addWatermark(imagePath);
    
    // 发送带水印的图片
    await ctx.replyWithPhoto({ source: watermarkedImagePath });
    
    // 清理临时文件
    fs.promises.unlink(imagePath).catch(console.error);
    fs.promises.unlink(watermarkedImagePath).catch(console.error);
  } catch (error) {
    console.error('处理图片时出错:', error);
    await ctx.reply('处理图片时出错，请稍后再试。');
  }
}

// 下载视频文件
async function downloadVideo(fileId: string, bot: Telegraf): Promise<string> {
  try {
    // 获取文件信息
    const fileInfo = await bot.telegram.getFile(fileId);
    if (!fileInfo || !fileInfo.file_path) {
      throw new Error('无法获取文件信息');
    }

    // 获取文件 URL
    const fileUrl = `https://api.telegram.org/file/bot${BOT_TOKEN}/${fileInfo.file_path}`;
    
    // 创建临时文件路径
    const fileName = `${Date.now()}_${path.basename(fileInfo.file_path)}`;
    const localFilePath = path.join(TEMP_DIR, fileName);
    
    // 下载文件
    const response = await axios({
      method: 'GET',
      url: fileUrl,
      responseType: 'stream'
    });

    const writer = fs.createWriteStream(localFilePath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', () => resolve(localFilePath));
      writer.on('error', reject);
    });
  } catch (error) {
    console.error('下载视频文件时出错:', error);
    throw error;
  }
}

// 处理媒体组
async function handleMediaGroup(ctx: Context, mediaGroup: any[]): Promise<void> {
  try {
    await ctx.reply('正在处理媒体组，请稍候...');
    
    const filesToCleanup: string[] = []; // 所有需要清理的文件
    const mediaItems: any[] = []; // 使用any类型来处理不同类型的媒体
    
    console.log(`开始处理媒体组，共有 ${mediaGroup.length} 个项目`);
    
    // 处理每个媒体项
    for (const media of mediaGroup) {
      try {
        // 处理图片
        if (media.photo) {
          console.log('处理图片项目');
          // 获取最大尺寸的图片
          const fileId = media.photo[media.photo.length - 1].file_id;
          
          // 下载图片
          const imagePath = await downloadFile(fileId, bot);
          filesToCleanup.push(imagePath);
          
          // 添加水印
          const watermarkedPath = await addWatermark(imagePath);
          filesToCleanup.push(watermarkedPath);
          
          // 添加到媒体组
          mediaItems.push({
            type: 'photo',
            media: { source: watermarkedPath },
            caption: media.caption || '',
            ...(media.caption_entities ? { caption_entities: media.caption_entities } : { parse_mode: 'HTML' })
          });
        }
        // 处理视频
        else if (media.video) {
          console.log('处理视频项目');
          const fileId = media.video.file_id;
          
          // 下载视频
          const videoPath = await downloadVideo(fileId, bot);
          filesToCleanup.push(videoPath);
          
          // 直接添加到媒体组（不添加水印）
          mediaItems.push({
            type: 'video',
            media: { source: videoPath },
            caption: media.caption || '',
            ...(media.caption_entities ? { caption_entities: media.caption_entities } : { parse_mode: 'HTML' })
          });
        }
        // 处理其他类型媒体（如音频、文件等）
        else if (media.document) {
          console.log('处理文档项目');
          const fileId = media.document.file_id;
          
          // 下载文件
          const docPath = await downloadFile(fileId, bot);
          filesToCleanup.push(docPath);
          
          // 直接添加到媒体组
          mediaItems.push({
            type: 'document',
            media: { source: docPath },
            caption: media.caption || '',
            ...(media.caption_entities ? { caption_entities: media.caption_entities } : { parse_mode: 'HTML' })
          });
        }
        // 处理音频
        else if (media.audio) {
          console.log('处理音频项目');
          const fileId = media.audio.file_id;
          
          // 下载音频
          const audioPath = await downloadFile(fileId, bot);
          filesToCleanup.push(audioPath);
          
          // 直接添加到媒体组
          mediaItems.push({
            type: 'audio',
            media: { source: audioPath },
            caption: media.caption || '',
            ...(media.caption_entities ? { caption_entities: media.caption_entities } : { parse_mode: 'HTML' })
          });
        }
      } catch (err) {
        console.error(`处理媒体项出错:`, err);
      }
    }
    
    console.log(`处理完成，有 ${mediaItems.length} 个项目可发送`);
    
    // 如果有处理好的媒体项目，发送媒体组
    if (mediaItems.length > 0) {
      await ctx.replyWithMediaGroup(mediaItems);
    } else {
      await ctx.reply('没有找到可以处理的媒体项目。');
    }
    
    // 清理临时文件
    filesToCleanup.forEach(filePath => {
      fs.promises.unlink(filePath).catch(console.error);
    });
  } catch (error) {
    console.error('处理媒体组时出错:', error);
    await ctx.reply('处理媒体组时出错，请稍后再试。');
  }
}

// 检查用户是否有权限使用机器人
function checkUserPermission(ctx: Context): boolean {
  const userId = ctx.from?.id;
  // 只允许ID为5986862132的用户使用
  return userId === 5986862132;
}

// 开始命令处理器
bot.start((ctx) => {
  if (!checkUserPermission(ctx)) {
    return ctx.reply('抱歉，您没有权限使用此机器人。');
  }
  ctx.reply('欢迎使用水印机器人！发送图片或媒体组，我会为您添加水印。');
});

// 帮助命令处理器
bot.help((ctx) => {
  if (!checkUserPermission(ctx)) {
    return ctx.reply('抱歉，您没有权限使用此机器人。');
  }
  ctx.reply('我可以为您的图片添加水印，只需发送单张图片或图片组即可。');
});

// 存储媒体组
const mediaGroups = new Map<string, { items: any[], timer: NodeJS.Timeout }>();

// 处理单张照片
bot.on(message('photo'), async (ctx) => {
  if (!checkUserPermission(ctx)) {
    return ctx.reply('抱歉，您没有权限使用此机器人。');
  }
  
  const fileId = ctx.message.photo[ctx.message.photo.length - 1].file_id;
  const mediaGroupId = ctx.message.media_group_id;
  
  // 如果是单张照片（不是媒体组的一部分）
  if (!mediaGroupId) {
    await handleSinglePhoto(ctx, fileId);
    return;
  }
  
  // 如果是媒体组的一部分，调用处理媒体组函数
  await handleMediaGroupItem(ctx, mediaGroupId);
});

// 处理媒体组中的视频
bot.on(message('video'), async (ctx) => {
  if (!checkUserPermission(ctx)) {
    return ctx.reply('抱歉，您没有权限使用此机器人。');
  }
  
  const mediaGroupId = ctx.message.media_group_id;
  if (!mediaGroupId) {
    // 单个视频处理
    await ctx.reply('我只能为图片加水印，视频暂不支持单独处理。');
    return;
  }
  
  // 如果是媒体组的一部分，调用处理媒体组函数
  await handleMediaGroupItem(ctx, mediaGroupId);
});

// 处理媒体组中的文档
bot.on(message('document'), async (ctx) => {
  if (!checkUserPermission(ctx)) {
    return ctx.reply('抱歉，您没有权限使用此机器人。');
  }
  
  const mediaGroupId = ctx.message.media_group_id;
  if (!mediaGroupId) {
    // 单个文档处理
    await ctx.reply('我只能为图片加水印，其他文件类型暂不支持单独处理。');
    return;
  }
  
  // 如果是媒体组的一部分，调用处理媒体组函数
  await handleMediaGroupItem(ctx, mediaGroupId);
});

// 处理媒体组中的音频
bot.on(message('audio'), async (ctx) => {
  if (!checkUserPermission(ctx)) {
    return ctx.reply('抱歉，您没有权限使用此机器人。');
  }
  
  const mediaGroupId = ctx.message.media_group_id;
  if (!mediaGroupId) {
    // 单个音频处理
    await ctx.reply('我只能为图片加水印，音频暂不支持单独处理。');
    return;
  }
  
  // 如果是媒体组的一部分，调用处理媒体组函数
  await handleMediaGroupItem(ctx, mediaGroupId);
});

// 处理媒体组项目的通用函数
async function handleMediaGroupItem(ctx: Context, mediaGroupId: string): Promise<void> {
  // 检查消息类型
  let mediaType = '未知类型';
  if (ctx.message && 'photo' in ctx.message) mediaType = '图片';
  else if (ctx.message && 'video' in ctx.message) mediaType = '视频';
  else if (ctx.message && 'document' in ctx.message) mediaType = '文档';
  else if (ctx.message && 'audio' in ctx.message) mediaType = '音频';
  
  console.log(`收到媒体组项目: ${mediaGroupId}, 类型: ${mediaType}`);
  
  // 获取或创建媒体组收集器
  if (!mediaGroups.has(mediaGroupId)) {
    // 创建新的媒体组收集器
    const timer = setTimeout(() => {
      const group = mediaGroups.get(mediaGroupId);
      if (group) {
        console.log(`开始处理媒体组: ${mediaGroupId}, 收到 ${group.items.length} 个项目`);
        handleMediaGroup(ctx, group.items);
        mediaGroups.delete(mediaGroupId);
      }
    }, 3000); // 3秒后处理媒体组
    
    mediaGroups.set(mediaGroupId, { items: [ctx.message], timer });
  } else {
    // 添加到现有媒体组
    const group = mediaGroups.get(mediaGroupId)!;
    group.items.push(ctx.message);
    console.log(`添加到媒体组: ${mediaGroupId}, 当前项目数: ${group.items.length}`);
  }
}

// 启动机器人
bot.launch().then(() => {
  console.log('水印机器人已启动！');
}).catch(error => {
  console.error('启动机器人时出错:', error);
});

// 优雅地关闭
process.once('SIGINT', () => bot.stop('SIGINT'));
process.once('SIGTERM', () => bot.stop('SIGTERM'));