#!/bin/bash

# 将此行中的路径替换为您要操作的实际目录路径
search_dir="/Users/<USER>/Dropbox/works/dev/claude-engineer-test2/telegram-login-nextjs"

# 使用 find 命令遍历所有 .new 文件
find "$search_dir" -type f -name '*.*.new' | while read newfile; do
    # 获取去掉 .new 后缀的原始文件名
    originalfile="${newfile%.new}"

    # 检查原始文件是否存在，存在则删除
    if [ -f "$originalfile" ]; then
        echo "Deleting original file: $originalfile"
        rm "$originalfile"
    fi

    # 重命名 .new 文件为去掉 .new 的文件名
    echo "Renaming $newfile to $originalfile"
    mv "$newfile" "$originalfile"
done