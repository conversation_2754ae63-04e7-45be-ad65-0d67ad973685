import { createClient } from 'redis';

// Redis客户端配置
const client = createClient({
  url: process.env.REDIS_URL || 'redis://localhost:6379',
  socket: {
    connectTimeout: 60000,
    reconnectStrategy: (retries) => {
      if (retries > 3) {
        return new Error('Redis连接重试次数过多');
      }
      return Math.min(retries * 50, 500);
    }
  }
});

// 连接事件处理
client.on('error', (err) => {
  console.error('Redis连接错误:', err);
});

client.on('connect', () => {
  console.log('Redis连接成功');
});

client.on('ready', () => {
  console.log('Redis客户端就绪');
});

// 单例模式确保只有一个Redis连接
let isConnected = false;
let connectingPromise: Promise<any> | null = null;

export const getRedisClient = async () => {
  // 如果已经连接，直接返回
  if (isConnected) {
    return client;
  }
  
  // 如果正在连接中，等待连接完成
  if (connectingPromise) {
    await connectingPromise;
    return client;
  }
  
  // 开始新的连接
  connectingPromise = (async () => {
    try {
      // 检查客户端是否已经连接（防止重复连接）
      if (!client.isOpen) {
        await client.connect();
      }
      isConnected = true;
      console.log('Redis连接建立成功');
    } catch (error) {
      console.error('Redis连接失败:', error);
      isConnected = false;
      throw error;
    } finally {
      connectingPromise = null;
    }
  })();
  
  await connectingPromise;
  return client;
};

// 断开连接
export const disconnectRedis = async () => {
  if (isConnected && client.isOpen) {
    await client.disconnect();
    isConnected = false;
    connectingPromise = null;
  }
};

export default client;