import { clearGirlsCache, deleteCachePattern, CACHE_PREFIXES } from './cache';

// 缓存失效事件类型
export type CacheInvalidationEvent = 
  | 'girl-created'
  | 'girl-updated' 
  | 'girl-deleted'
  | 'user-created'
  | 'data-import'
  | 'manual-clear';

// 缓存失效处理器
export class CacheInvalidationHandler {
  
  // 处理Girls相关数据变更
  static async handleGirlDataChange(
    event: CacheInvalidationEvent, 
    girlId?: string,
    cityChanged?: boolean
  ): Promise<void> {
    console.log(`处理缓存失效事件: ${event}${girlId ? ` (girl_id: ${girlId})` : ''}`);

    try {
      switch (event) {
        case 'girl-created':
        case 'girl-updated':
          // 清除相关的列表缓存和统计缓存
          await Promise.all([
            deleteCachePattern(`${CACHE_PREFIXES.GIRLS}*`), // 清除所有Girls列表缓存
            deleteCachePattern(`${CACHE_PREFIXES.STATS}*`), // 清除统计缓存
            deleteCachePattern(`${CACHE_PREFIXES.COUNTS}*`), // 清除计数缓存
          ]);

          // 如果涉及城市变更，清除地理位置缓存
          if (cityChanged) {
            await Promise.all([
              deleteCachePattern(`${CACHE_PREFIXES.CITIES}*`),
              deleteCachePattern(`${CACHE_PREFIXES.DISTRICTS}*`),
            ]);
          }

          // 如果有具体的girl_id，清除该详情缓存
          if (girlId) {
            await deleteCachePattern(`${CACHE_PREFIXES.GIRL_DETAIL}${girlId}`);
          }
          break;

        case 'girl-deleted':
          // 清除所有相关缓存
          await clearGirlsCache();
          
          // 清除地理位置缓存（因为可能影响城市/地区列表）
          await Promise.all([
            deleteCachePattern(`${CACHE_PREFIXES.CITIES}*`),
            deleteCachePattern(`${CACHE_PREFIXES.DISTRICTS}*`),
          ]);
          break;

        case 'data-import':
        case 'manual-clear':
          // 清除所有Girls相关缓存
          await clearGirlsCache();
          break;

        default:
          console.warn(`未知的缓存失效事件: ${event}`);
      }

      console.log(`缓存失效处理完成: ${event}`);
    } catch (error) {
      console.error(`缓存失效处理失败 [${event}]:`, error);
      // 不抛出错误，避免影响主业务逻辑
    }
  }

  // 处理批量数据变更
  static async handleBulkDataChange(
    event: CacheInvalidationEvent,
    affectedCount?: number
  ): Promise<void> {
    console.log(`处理批量缓存失效: ${event} (影响 ${affectedCount || '未知'} 条记录)`);

    try {
      // 批量变更时，清除所有相关缓存
      await clearGirlsCache();
      
      // 清除地理位置缓存
      await Promise.all([
        deleteCachePattern(`${CACHE_PREFIXES.CITIES}*`),
        deleteCachePattern(`${CACHE_PREFIXES.DISTRICTS}*`),
      ]);

      console.log(`批量缓存失效处理完成: ${event}`);
    } catch (error) {
      console.error(`批量缓存失效处理失败 [${event}]:`, error);
    }
  }

  // 处理用户相关变更（如VIP状态变更可能影响数据访问）
  static async handleUserDataChange(event: CacheInvalidationEvent): Promise<void> {
    console.log(`处理用户缓存失效: ${event}`);
    
    try {
      // 目前用户变更不影响Girls数据缓存
      // 如果将来有用户相关的个性化缓存，在这里处理
      console.log(`用户缓存失效处理完成: ${event}`);
    } catch (error) {
      console.error(`用户缓存失效处理失败 [${event}]:`, error);
    }
  }

  // 智能缓存失效（根据数据变更类型自动判断）
  static async smartInvalidate(options: {
    type: 'girl' | 'user' | 'bulk';
    operation: 'create' | 'update' | 'delete';
    girlId?: string;
    cityChanged?: boolean;
    affectedCount?: number;
  }): Promise<void> {
    const { type, operation, girlId, cityChanged, affectedCount } = options;

    const eventMap: Record<string, CacheInvalidationEvent> = {
      'girl-create': 'girl-created',
      'girl-update': 'girl-updated',
      'girl-delete': 'girl-deleted',
      'user-create': 'user-created',
      'user-update': 'user-created', // 复用相同的处理逻辑
      'user-delete': 'user-created',
    };

    const eventKey = `${type}-${operation}`;
    const event = eventMap[eventKey];

    if (!event) {
      console.warn(`未知的智能缓存失效类型: ${eventKey}`);
      return;
    }

    if (type === 'bulk') {
      await this.handleBulkDataChange(event, affectedCount);
    } else if (type === 'girl') {
      await this.handleGirlDataChange(event, girlId, cityChanged);
    } else if (type === 'user') {
      await this.handleUserDataChange(event);
    }
  }
}

// 导出便利函数
export const invalidateGirlCache = (
  operation: 'create' | 'update' | 'delete',
  girlId?: string,
  cityChanged?: boolean
) => CacheInvalidationHandler.smartInvalidate({
  type: 'girl',
  operation,
  girlId,
  cityChanged
});

export const invalidateBulkCache = (
  operation: 'create' | 'update' | 'delete',
  affectedCount?: number
) => CacheInvalidationHandler.smartInvalidate({
  type: 'bulk',
  operation,
  affectedCount
});

export const invalidateUserCache = (
  operation: 'create' | 'update' | 'delete'
) => CacheInvalidationHandler.smartInvalidate({
  type: 'user',
  operation
});