// 添加接口定义
export interface Girl {
  id: string;
  girl_id: string;
  title: string;
  feature: string;
  city: string;
  district?: string;
  height?: string;
  age?: string;
  cup?: string;
  price: string;
  type: string[];
  service: string[];
  description?: string;
  media: string[];
  pusher?: string;
  message?: string;
  createdAt?: string;
  updatedAt?: string;
  photoAccuracy?: number;
  appearance?: number;
  attitude?: number;
  serviceQuality?: number;
  overallRating?: number;
  comments?: string;
}

interface GetGirlsResponse {
  girls: Girl[];
  totalPages: number;
  totalGirls: number;
}

interface Filters {
  city: string;
  district: string | string[];
  price?: string;
  cup?: string;
  type?: string;
  service?: string;
}

interface CountData {
  [key: string]: number;
}

// 添加统一的缓存配置
const CACHE_CONFIG = {
  revalidate: 60,
  headers: {
    'Cache-Control': 'public, s-maxage=60, stale-while-revalidate=30',
  }
};

// 修改 getBaseUrl 函数
const getBaseUrl = () => {
  // 浏览器端使用相对路径
  if (typeof window !== 'undefined') return '';

  // Vercel 环境（包括生产和预览）
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }

  // 本地开发环境
  return `http://localhost:${process.env.PORT || 3000}`;
}

const API_BASE_URL = '/api';

// 添加重试逻辑的通用 fetch 函数
const fetchWithRetry = async (url: string, options: RequestInit = {}, retries = 3): Promise<Response> => {
  try {
    // 添加随机参数避免缓存
    const separator = url.includes('?') ? '&' : '?';
    const cacheBreaker = `_=${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
    const urlWithCacheBreaker = `${url}${separator}${cacheBreaker}`;

    console.log(`Fetching ${urlWithCacheBreaker}...`);

    const response = await fetch(urlWithCacheBreaker, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'Pragma': 'no-cache',
        ...options.headers,
      },
    });

    if (!response.ok) {
      console.error(`API request failed: ${urlWithCacheBreaker}`, {
        status: response.status,
        statusText: response.statusText,
        body: await response.text().catch(() => 'Could not read response body')
      });
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response;
  } catch (error) {
    if (retries > 0) {
      console.log(`Retrying request to ${url}, ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return fetchWithRetry(url, options, retries - 1);
    }
    throw error;
  }
};

// 添加一个新的辅助函数来处理媒体 URL
export const transformMediaUrls = (urls: string[]): string[] => {
  return urls.map(url => url.replace('jianier.club', 'jianier.com'));
};

// 处理女孩数据中的媒体 URL
const transformImageUrls = (girl: Girl): Girl => {
  return {
    ...girl,
    media: transformMediaUrls(girl.media)
  };
};

// 修改 getGirls 函数
export const getGirls = async (page: number, perPage: number, filters: Filters): Promise<GetGirlsResponse> => {
  try {
    const baseUrl = getBaseUrl();
    const params: Record<string, string> = {
      page: page.toString(),
      perPage: perPage.toString(),
      _t: Date.now().toString() // 添加时间戳防止缓存
    };

    if (filters.city) params.city = filters.city;
    if (filters.district) {
      params.district = Array.isArray(filters.district)
        ? filters.district.join('|')
        : filters.district;
    }
    if (filters.cup) params.cup = filters.cup.toUpperCase();
    if (filters.price) params.price = filters.price;
    if (filters.type) params.type = filters.type;
    if (filters.service) params.service = filters.service;

    const queryParams = new URLSearchParams(params);
    const url = `${baseUrl}${API_BASE_URL}/girls?${queryParams}`;
    console.log('【DEBUG-获取女孩数据】发起请求:', url);

    const response = await fetchWithRetry(url, {
      cache: 'no-store',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log(`【DEBUG-获取女孩数据】获取了 ${data.girls.length} 个女孩，总页数: ${data.totalPages}，总数: ${data.totalGirls}`);

    // 转换所有女孩的图片 URL
    return {
      ...data,
      girls: data.girls.map(transformImageUrls)
    };
  } catch (error) {
    console.error('Error fetching girls:', error);
    return {
      girls: [],
      totalPages: 0,
      totalGirls: 0
    };
  }
};

export const getCities = async (): Promise<string[]> => {
  try {
    const baseUrl = getBaseUrl();
    const response = await fetch(`${baseUrl}${API_BASE_URL}/city`, {
      cache: 'no-store'
    });
    if (!response.ok) {
      throw new Error('Network response was not ok');
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching cities:', error);
    throw error;
  }
};

export const getDistricts = async (): Promise<{ [city: string]: string[] }> => {
  try {
    const baseUrl = getBaseUrl();
    const response = await fetch(`${baseUrl}${API_BASE_URL}/district`, {
      cache: 'no-store'
    });

    if (!response.ok) {
      throw new Error('Network response was not ok');
    }
    const data: { city: string; district: string }[] = await response.json();
    const districtsMap: { [city: string]: string[] } = {};
    data.forEach((item) => {
      if (!districtsMap[item.city]) {
        districtsMap[item.city] = [];
      }
      districtsMap[item.city].push(item.district);
    });
    return districtsMap;
  } catch (error) {
    console.error('Error fetching districts:', error);
    throw error;
  }
};

export const getCups = async (city?: string, price?: string): Promise<CountData> => {
  try {
    const baseUrl = getBaseUrl();
    // 添加时间戳防止缓存
    const timestamp = Date.now();
    const params = new URLSearchParams();
    if (city) params.append('city', city);
    if (price) params.append('price', price);
    params.append('_t', timestamp.toString());

    const url = `${baseUrl}${API_BASE_URL}/cups?${params.toString()}`;
    console.log('【DEBUG】发起getCups请求:', url);
    const response = await fetch(url, {
      cache: 'no-store',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
    if (!response.ok) {
      throw new Error('Network response was not ok');
    }
    const data = await response.json();
    console.log('【DEBUG】getCups请求结果:', data);
    return data;
  } catch (error) {
    console.error('Error fetching cups:', error);
    return {}; // 返回空对象而不是抛出错误
  }
};

export const getTypes = async (city?: string, price?: string): Promise<CountData> => {
  try {
    const baseUrl = getBaseUrl();
    // 添加时间戳防止缓存
    const timestamp = Date.now();
    const params = new URLSearchParams();
    if (city) params.append('city', city);
    if (price) params.append('price', price);
    params.append('_t', timestamp.toString());

    const url = `${baseUrl}${API_BASE_URL}/types?${params.toString()}`;
    console.log('【DEBUG】发起getTypes请求:', url);
    const response = await fetch(url, {
      cache: 'no-store',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
    if (!response.ok) {
      throw new Error('Network response was not ok');
    }
    const data = await response.json();
    console.log('【DEBUG】getTypes请求结果:', data);
    return data;
  } catch (error) {
    console.error('Error fetching types:', error);
    return {}; // 返回空对象而不是抛出错误
  }
};

export const getServices = async (city?: string, price?: string): Promise<CountData> => {
  try {
    const baseUrl = getBaseUrl();
    // 添加时间戳防止缓存
    const timestamp = Date.now();
    const params = new URLSearchParams();
    if (city) params.append('city', city);
    if (price) params.append('price', price);
    params.append('_t', timestamp.toString());

    const url = `${baseUrl}${API_BASE_URL}/services?${params.toString()}`;
    console.log('【DEBUG】发起getServices请求:', url);
    const response = await fetch(url, {
      cache: 'no-store',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
    if (!response.ok) {
      throw new Error('Network response was not ok');
    }
    const data = await response.json();
    console.log('【DEBUG】getServices请求结果:', data);
    return data;
  } catch (error) {
    console.error('Error fetching services:', error);
    return {}; // 返回空对象而不是抛出错误
  }
};

export const getCityCounts = async (price?: string | null): Promise<{ [city: string]: number }> => {
  try {
    const baseUrl = getBaseUrl();
    // 简化URL构建，避免复杂的替换逻辑
    let url = `${baseUrl}${API_BASE_URL}/city-counts`;

    // 构建查询参数
    const params = new URLSearchParams();
    if (price) params.append('price', price);

    // 添加时间戳防止缓存
    params.append('_nocache', `${Date.now()}`);

    // 拼接完整URL
    const fullUrl = `${url}${params.toString() ? `?${params.toString()}` : ''}`;

    console.log("【DEBUG-城市计数】发起请求: ", fullUrl);

    const response = await fetch(fullUrl, {
      cache: 'no-store',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache'
      }
    });

    if (!response.ok) {
      throw new Error(`Network response was not ok: ${response.status}`);
    }
    const data = await response.json();
    console.log("【DEBUG-城市计数】获取的原始数据: ", JSON.stringify(data));

    // 测试数据
    console.log("【DEBUG-城市计数】北京的数量: ", data["北京"]);

    return data;
  } catch (error) {
    console.error('Error fetching city counts:', error);
    throw error;
  }
};

export const getDistrictCounts = async (city: string): Promise<CountData> => {
  try {
    const baseUrl = getBaseUrl();
    const response = await fetch(`${baseUrl}${API_BASE_URL}/district-counts/${encodeURIComponent(city)}`, {
      cache: 'no-store'
    });
    if (!response.ok) {
      throw new Error('Network response was not ok');
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching district counts:', error);
    throw error;
  }
};

export const getPriceCounts = async (city?: string): Promise<CountData> => {
  try {
    const baseUrl = getBaseUrl();
    const url = `${baseUrl}${API_BASE_URL}/price-counts${city ? `?city=${encodeURIComponent(city)}` : ''}`;

    const response = await fetchWithRetry(url, {
      cache: 'no-store',
      headers: {
        'Cache-Control': 'no-cache'
      }
    });

    if (!response.ok) {
      throw new Error('Network response was not ok');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching price counts:', {
      error,
      city,
      baseUrl: getBaseUrl()
    });
    throw error;
  }
};

// 修改 getGirlById 函数
export const getGirlById = async (girl_id: string): Promise<Girl | null> => {
  try {
    const baseUrl = getBaseUrl();
    const url = `${baseUrl}${API_BASE_URL}/girls/by-girl-id/${encodeURIComponent(girl_id)}`;
    console.log('Fetching girl by ID from:', url);

    const response = await fetchWithRetry(url, {
      cache: 'no-store'
    });

    if (!response.ok) {
      console.log('Failed to fetch girl:', girl_id, 'Status:', response.status);
      return null;
    }

    const data: Girl = await response.json();
    // 转换单个女孩的图片 URL
    return transformImageUrls(data);
  } catch (error) {
    console.error('Error fetching girl by ID:', {
      error,
      girl_id,
      baseUrl: getBaseUrl(),
    });
    return null; // 返回 null 而不是抛出错误
  }
};

// 修改获取评论数据的函数
export const getFeedbackData = async (
  page: number = 1,
  perPage: number = 20,
  filters?: {
    city?: string;
    price?: string;
  }
): Promise<{ girls: Girl[]; totalPages: number }> => {
  try {
    const baseUrl = getBaseUrl();
    let url = `${baseUrl}${API_BASE_URL}/feedback?page=${page}&perPage=${perPage}`;

    // 添加过滤器参数
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          url += `&${key}=${encodeURIComponent(value)}`;
        }
      });
    }

    const response = await fetchWithRetry(url, {
      next: { revalidate: 60 },
      headers: {
        'Cache-Control': 'no-cache'
      }
    });

    if (!response.ok) {
      throw new Error('Failed to fetch feedback data');
    }

    const data = await response.json();
    return {
      girls: data.girls.map(transformImageUrls),
      totalPages: data.totalPages
    };
  } catch (error) {
    console.error('Error fetching feedback data:', error);
    return {
      girls: [],
      totalPages: 0
    };
  }
};
