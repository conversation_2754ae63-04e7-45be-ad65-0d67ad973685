export function normalizeDistrict(district: string): string {
  if (!district) return '';
  
  // 移除 "区", "新区", "县" 等后缀
  let normalized = district.replace(/(区|新区|县)$/, '');
  
  // 特殊情况处理
  const specialMappings: Record<string, string> = {
    // 上海
    '浦东': '浦东',
    '浦东新': '浦东',
    'pudong': '浦东',
    '徐汇': '徐汇',
    'xuhui': '徐汇',
    '黄浦': '黄浦',
    '静安': '静安',
    '长宁': '长宁',
    '普陀': '普陀',
    '虹口': '虹口',
    '杨浦': '杨浦',
    '闵行': '闵行',
    '宝山': '宝山',
    '嘉定': '嘉定',
    '金山': '金山',
    '松江': '松江',
    '青浦': '青浦',
    '奉贤': '奉贤',
    '崇明': '崇明',

    // 北京
    '东城': '东城',
    '西城': '西城',
    '朝阳': '朝阳',
    '海淀': '海淀',
    '丰台': '丰台',
    '石景山': '石景山',
    '门头沟': '门头沟',
    '房山': '房山',
    '通州': '通州',
    '顺义': '顺义',
    '昌平': '昌平',
    '大兴': '大兴',
    '怀柔': '怀柔',
    '平谷': '平谷',
    '密云': '密云',
    '延庆': '延庆',

    // 广州
    '越秀': '越秀',
    '海珠': '海珠',
    '荔湾': '荔湾',
    '天河': '天河',
    '白云': '白云',
    '黄埔': '黄埔',
    '番禺': '番禺',
    '花都': '花都',
    '南沙': '南沙',
    '从化': '从化',
    '增城': '增城',

    // 深圳
    '福田': '福田',
    '罗湖': '罗湖',
    '南山': '南山',
    '盐田': '盐田',
    '宝安': '宝安',
    '龙岗': '龙岗',
    '龙华': '龙华',
    '坪山': '坪山',
    '光明': '光明',
    '大鹏': '大鹏'
  };

  return specialMappings[normalized.toLowerCase()] || normalized;
}

export function getDisplayDistrict(district: string): string {
  // 用于显示的完整区域名称
  const displayMappings: Record<string, string> = {
    // 上海
    '浦东': '浦东新区',
    '徐汇': '徐汇区',
    '黄浦': '黄浦区',
    '静安': '静安区',
    '长宁': '长宁区',
    '普陀': '普陀区',
    '虹口': '虹口区',
    '杨浦': '杨浦区',
    '闵行': '闵行区',
    '宝山': '宝山区',
    '嘉定': '嘉定区',
    '金山': '金山区',
    '松江': '松江区',
    '青浦': '青浦区',
    '奉贤': '奉贤区',
    '崇明': '崇明区',

    // 北京
    '东城': '东城区',
    '西城': '西城区',
    '朝阳': '朝阳区',
    '海淀': '海淀区',
    '丰台': '丰台区',
    '石景山': '石景山区',
    '门头沟': '门头沟区',
    '房山': '房山区',
    '通州': '通州区',
    '顺义': '顺义区',
    '昌平': '昌平区',
    '大兴': '大兴区',
    '怀柔': '怀柔区',
    '平谷': '平谷区',
    '密云': '密云区',
    '延庆': '延庆区',

    // 广州
    '越秀': '越秀区',
    '海珠': '海珠区',
    '荔湾': '荔湾区',
    '天河': '天河区',
    '白云': '白云区',
    '黄埔': '黄埔区',
    '番禺': '番禺区',
    '花都': '花都区',
    '南沙': '南沙区',
    '从化': '从化区',
    '增城': '增城区',

    // 深圳
    '福田': '福田区',
    '罗湖': '罗湖区',
    '南山': '南山区',
    '盐田': '盐田区',
    '宝安': '宝安区',
    '龙岗': '龙岗区',
    '龙华': '龙华区',
    '坪山': '坪山区',
    '光明': '光明区',
    '大鹏': '大鹏新区'
  };

  const normalized = normalizeDistrict(district);
  return displayMappings[normalized] || district;
}

// 获取区域的所有可能变体
export function getDistrictVariants(district: string): string[] {
  const normalized = normalizeDistrict(district);
  if (!normalized) return [];

  const variants = new Set<string>();
  
  // 添加基本形式
  variants.add(normalized);
  
  // 添加带"区"的形式
  variants.add(normalized + '区');
  
  // 特殊处理浦东新区
  if (normalized === '浦东') {
    variants.add('浦东新区');
  }
  
  // 特殊处理大鹏新区
  if (normalized === '大鹏') {
    variants.add('大鹏新区');
  }

  return Array.from(variants);
}

// 判断两个区域是否相同
export function isSameDistrict(district1: string, district2: string): boolean {
  return normalizeDistrict(district1) === normalizeDistrict(district2);
}

// 合并区域计数
export function mergeDistrictCounts(counts: Record<string, number>): Record<string, number> {
  const mergedCounts: Record<string, number> = {};
  
  Object.entries(counts).forEach(([district, count]) => {
    const normalized = normalizeDistrict(district);
    if (!normalized) return;
    mergedCounts[normalized] = (mergedCounts[normalized] || 0) + count;
  });
  
  return mergedCounts;
} 