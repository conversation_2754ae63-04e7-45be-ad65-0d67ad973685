import crypto from 'crypto';

const INTERCOM_SECRET_KEY = "hLHTvKn35BngIOZ9dwx60ycl81D4ypsJuahQYzNw";

export function generateHash(userId: string | undefined): string {
    if (!INTERCOM_SECRET_KEY) {
        console.error('INTERCOM_SECRET_KEY is not set');
        return '';
    }

    if (!userId) {
        console.error('userId is undefined');
        return '';
    }

    return crypto
        .createHmac('sha256', INTERCOM_SECRET_KEY)
        .update(userId)
        .digest('hex');
}
