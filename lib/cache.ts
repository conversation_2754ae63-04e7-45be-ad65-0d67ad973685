import { getRedisClient } from './redis';

// 缓存键前缀
export const CACHE_PREFIXES = {
  GIRLS: 'girls:',
  GIRL_DETAIL: 'girl:',
  CITIES: 'cities:',
  DISTRICTS: 'districts:',
  STATS: 'stats:',
  COUNTS: 'counts:',
} as const;

// 缓存过期时间（秒）
export const CACHE_EXPIRY = {
  GIRLS_LIST: 300, // 5分钟 - 档案列表
  GIRL_DETAIL: 900, // 15分钟 - 单个档案详情
  LOCATION_DATA: 1800, // 30分钟 - 城市/地区数据
  STATS_DATA: 600, // 10分钟 - 统计数据
  COUNT_DATA: 300, // 5分钟 - 计数数据
} as const;

// 生成缓存键
export const generateCacheKey = (prefix: string, ...params: (string | number)[]): string => {
  return `${prefix}${params.join(':')}`;
};

// 通用缓存获取函数
export const getCache = async <T>(key: string): Promise<T | null> => {
  try {
    const client = await getRedisClient();
    const cached = await client.get(key);
    
    if (!cached) {
      return null;
    }
    
    return JSON.parse(cached) as T;
  } catch (error) {
    console.error(`缓存获取失败 [${key}]:`, error);
    return null;
  }
};

// 通用缓存设置函数
export const setCache = async <T>(
  key: string, 
  data: T, 
  expiry: number = CACHE_EXPIRY.STATS_DATA
): Promise<void> => {
  try {
    const client = await getRedisClient();
    await client.setEx(key, expiry, JSON.stringify(data));
  } catch (error) {
    console.error(`缓存设置失败 [${key}]:`, error);
  }
};

// 删除缓存
export const deleteCache = async (key: string): Promise<void> => {
  try {
    const client = await getRedisClient();
    await client.del(key);
  } catch (error) {
    console.error(`缓存删除失败 [${key}]:`, error);
  }
};

// 批量删除缓存（根据模式）
export const deleteCachePattern = async (pattern: string): Promise<void> => {
  try {
    const client = await getRedisClient();
    const keys = await client.keys(pattern);
    
    if (keys.length > 0) {
      await client.del(keys);
      console.log(`删除了 ${keys.length} 个缓存键，模式: ${pattern}`);
    }
  } catch (error) {
    console.error(`批量删除缓存失败 [${pattern}]:`, error);
  }
};

// 缓存装饰器函数
export const withCache = <T>(
  keyGenerator: (...args: any[]) => string,
  expiry: number = CACHE_EXPIRY.STATS_DATA
) => {
  return (target: any, propertyName: string, descriptor: PropertyDescriptor) => {
    const method = descriptor.value;
    
    descriptor.value = async function (...args: any[]): Promise<T> {
      const cacheKey = keyGenerator(...args);
      
      // 尝试从缓存获取
      const cached = await getCache<T>(cacheKey);
      if (cached !== null) {
        return cached;
      }
      
      // 缓存未命中，执行原函数
      const result = await method.apply(this, args);
      
      // 设置缓存
      await setCache(cacheKey, result, expiry);
      
      return result;
    };
  };
};

// Girls API专用缓存键生成器
export const generateGirlsApiKey = (
  page: number = 1,
  pageSize: number = 20,
  filters: Record<string, any> = {}
): string => {
  const filterStr = Object.keys(filters)
    .sort()
    .map(key => `${key}=${filters[key]}`)
    .join('&');
  
  return generateCacheKey(
    CACHE_PREFIXES.GIRLS,
    'list',
    page,
    pageSize,
    Buffer.from(filterStr).toString('base64')
  );
};

// 统计数据缓存键生成器
export const generateStatsKey = (type: string, ...params: string[]): string => {
  return generateCacheKey(CACHE_PREFIXES.STATS, type, ...params);
};

// 地区数据缓存键生成器
export const generateLocationKey = (type: 'cities' | 'districts', city?: string): string => {
  if (type === 'districts' && city) {
    return generateCacheKey(CACHE_PREFIXES.DISTRICTS, city);
  }
  return generateCacheKey(CACHE_PREFIXES.CITIES, 'all');
};

// 清除Girls相关的所有缓存
export const clearGirlsCache = async (): Promise<void> => {
  await Promise.all([
    deleteCachePattern(`${CACHE_PREFIXES.GIRLS}*`),
    deleteCachePattern(`${CACHE_PREFIXES.GIRL_DETAIL}*`),
    deleteCachePattern(`${CACHE_PREFIXES.STATS}*`),
    deleteCachePattern(`${CACHE_PREFIXES.COUNTS}*`),
  ]);
};

// 缓存预热函数
export const warmupCache = async (): Promise<void> => {
  console.log('开始缓存预热...');
  // 这里可以预加载一些热门数据
  console.log('缓存预热完成');
};