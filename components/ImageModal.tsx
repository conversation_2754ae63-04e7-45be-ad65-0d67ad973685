import React from "react";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { Dialog, DialogOverlay, DialogTitle, DialogPortal } from "./ui/dialog";
import { X } from "lucide-react";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { cn } from "../lib/utils";

interface ImageModalProps {
	isOpen: boolean;
	onClose: () => void;
	imageUrl: string;
	imageRef: HTMLImageElement | null;
}

// 自定义 DialogContent 组件，移除默认的关闭按钮
const CustomDialogContent = React.forwardRef<
	React.ElementRef<typeof DialogPrimitive.Content>,
	React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
	<DialogPortal>
		<DialogOverlay customStyle="bg-black/80 backdrop-blur-none" />
		<DialogPrimitive.Content
			ref={ref}
			className={cn(
				"fixed left-[50%] top-[50%] z-50 grid w-full max-w-[95vw] max-h-[95vh] translate-x-[-50%] translate-y-[-50%] border-none bg-transparent p-0 overflow-hidden duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]",
				className
			)}
			{...props}
		>
			{children}
			{/* 移除默认的 DialogPrimitive.Close 按钮 */}
		</DialogPrimitive.Content>
	</DialogPortal>
));
CustomDialogContent.displayName = "CustomDialogContent";

const ImageModal: React.FC<ImageModalProps> = ({ isOpen, onClose, imageUrl, imageRef }) => {
	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<CustomDialogContent>
				<VisuallyHidden>
					<DialogTitle>Enlarged Image View</DialogTitle>
				</VisuallyHidden>
				<div className="relative w-full h-full flex items-center justify-center" onClick={onClose}>
					<div className="relative">
						<img src={imageRef ? imageRef.src : imageUrl} alt="Enlarged view" className="max-w-full max-h-[calc(95vh-2rem)] object-contain cursor-pointer" />
						<button
							onClick={(e) => {
								e.stopPropagation();
								onClose();
							}}
							className="absolute top-4 right-4 text-white bg-black/60 hover:bg-black/80 rounded-full p-2 transition-colors"
							aria-label="Close"
						>
							<X size={24} />
						</button>
					</div>
				</div>
			</CustomDialogContent>
		</Dialog>
	);
};

export default ImageModal;
