'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { Girl, getGirls } from '../lib/api';
import { Skeleton } from "../components/ui/skeleton";

interface PopularResource extends Girl {
  score: number;
}

interface VerifiedProfile extends Girl {
  score: number;
  date: string;
}

export default function PopularResources() {
  const [popularResources, setPopularResources] = useState<PopularResource[]>([]);
  const [verifiedProfiles, setVerifiedProfiles] = useState<VerifiedProfile[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // 获取5K价格的资源
        const popularResponse = await getGirls(1, 6, {
          city: "",
          district: "",
          price: "5K"
        });
        const popularData = popularResponse.girls.map(girl => ({
          ...girl,
          score: Math.floor(Math.random() * 2) + 9 // 9-10分随机评分
        }));
        setPopularResources(popularData);

        // 获取认证资源，排除已经在 popularData 中的 girl_id
        const popularGirlIds = new Set(popularData.map(girl => girl.girl_id));
        let page = 1;
        let verifiedData: VerifiedProfile[] = [];
        
        while (verifiedData.length < 6) {
          const verifiedResponse = await getGirls(page, 10, {
            city: "",
            district: "",
            price: "5K"  // 添加价格筛选条件
          });
          
          const newVerifiedData = verifiedResponse.girls
            .filter(girl => !popularGirlIds.has(girl.girl_id))
            .map(girl => ({
              ...girl,
              score: (Math.floor(Math.random() * 3) + 8.5), // 8.5-10.5分随机评分
              date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
              })
            }));
          
          verifiedData = [...verifiedData, ...newVerifiedData].slice(0, 6);
          
          if (verifiedResponse.girls.length < 10 || page > 5) break; // 防止无限循环
          page++;
        }
        
        setVerifiedProfiles(verifiedData);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="w-full space-y-4 px-4 bg-dark-background text-white">
        {/* 标题骨架屏 */}
        <div className="flex items-center gap-2">
          <Skeleton className="h-10 w-10 rounded-full" />
          <Skeleton className="h-10 w-40" />
        </div>

        {/* 超人气选手区域骨架屏 */}
        <section>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-[#1a1a1a] rounded-xl overflow-hidden h-full flex flex-col">
                <Skeleton className="aspect-[4/5] w-full" />
                <div className="p-3 space-y-2">
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-8" />
                  </div>
                  <Skeleton className="h-12 w-full" />
                  <div className="flex flex-wrap gap-1">
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-6 w-16" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* 极品认证区域骨架屏 */}
        <section className="mt-6">
          <div className="flex items-center gap-2 mb-6">
            <Skeleton className="h-10 w-10 rounded-full" />
            <Skeleton className="h-10 w-40" />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-[#1a1a1a] rounded-xl overflow-hidden h-full flex flex-col">
                <Skeleton className="aspect-[4/5] w-full" />
                <div className="p-3 space-y-2">
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-8" />
                  </div>
                  <Skeleton className="h-12 w-full" />
                  <div className="flex flex-wrap gap-1">
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-6 w-16" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>
      </div>
    );
  }

  return (
    <div className="w-full space-y-4 px-4 bg-dark-background text-white">
      {/* 标题 */}
      <h2 className="text-4xl font-bold flex items-center gap-2">
        <span className="text-4xl">😻</span>
        超人气选手
      </h2>

      {/* 超人气选手区域 */}
      <section>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
          {popularResources.map((resource) => (
            <Link href={`/girl/${resource.girl_id}`} key={resource.id}>
              <div className="bg-[#1a1a1a] rounded-xl overflow-hidden cursor-pointer hover:bg-[#252525] transition-colors h-full flex flex-col">
                <div className="relative aspect-[4/5] overflow-hidden">
                  <Image
                    src={resource.media[0] || '/placeholder.jpg'}
                    alt={resource.title}
                    fill
                    sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                    className="object-cover"
                  />
                </div>
                <div className="p-3 flex flex-col flex-1 min-h-[140px] space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium truncate">{resource.title}</h3>
                    <div className="flex items-center gap-1">
                      <div className="w-1 h-1 rounded-full bg-green-500"></div>
                      <span className="text-xs text-gray-400">{resource.score}</span>
                    </div>
                  </div>
                  {resource.description && (
                    <p className="text-xs text-gray-400 line-clamp-3">{resource.description}</p>
                  )}
                  <div className="flex flex-wrap gap-1 mt-auto">
                    {resource.type?.map((tag) => (
                      <span key={tag} className="px-2 py-1 bg-[#2a2a2a] rounded-full text-xs text-gray-300">
                        {tag}
                      </span>
                    ))}
                    <span className="px-2 py-1 bg-[#2a2a2a] rounded-full text-xs text-gray-300">
                      {resource.city}
                    </span>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </section>

      {/* 极品认证区域 */}
      <section className="mt-6">
        <div className="flex items-center gap-2 mb-6">
          <div className="text-4xl" style={{ color: '#ff0000' }}>💯</div>
          <h2 className="text-4xl font-bold">极品认证</h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
          {verifiedProfiles.map((profile) => (
            <Link href={`/girl/${profile.girl_id}`} key={profile.id}>
              <div className="bg-[#1a1a1a] rounded-xl overflow-hidden cursor-pointer hover:bg-[#252525] transition-colors h-full flex flex-col">
                <div className="relative aspect-[4/5] overflow-hidden">
                  <Image
                    src={profile.media[0] || "/placeholder.jpg"}
                    alt={profile.title}
                    fill
                    sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                    className="object-cover"
                  />
                </div>
                <div className="p-3 flex flex-col flex-1 min-h-[140px] space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium truncate">{profile.title}</h3>
                    <div className="flex items-center gap-1">
                      <div className="w-1 h-1 rounded-full bg-green-500"></div>
                      <span className="text-xs text-gray-400">{profile.score}</span>
                    </div>
                  </div>
                  {profile.description && (
                    <p className="text-xs text-gray-400 line-clamp-3">{profile.description}</p>
                  )}
                  <div className="flex flex-wrap gap-1 mt-auto">
                    {profile.type?.map((tag) => (
                      <span key={tag} className="px-2 py-1 bg-[#2a2a2a] rounded-full text-xs text-gray-300">
                        {tag}
                      </span>
                    ))}
                    <span className="px-2 py-1 bg-[#2a2a2a] rounded-full text-xs text-gray-300">
                      {profile.city}
                    </span>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </section>
    </div>
  );
}
