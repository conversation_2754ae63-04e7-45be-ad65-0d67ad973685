"use client";

import React, { useState, useEffect, useCallback, useMemo } from "react";
import Masonry from "react-masonry-css";
import { ScrollArea } from "./ui/scroll-area";
import { Alert, AlertDescription, AlertTitle } from "./ui/alert";
import { ChevronRight } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Button } from "./ui/button";
import GirlCard from "./GirlCard";
import Pagination from "./Pagination";
import { getGirls, getCups, getTypes, getServices, getDistrictCounts, getPriceCounts } from "../lib/api";
import "../styles/masonry.css";
import SelectItemWrapper from "./ui/SelectItemWrapper";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { urlToCityName, cityNameToUrl } from "../lib/cityNameMapping";

interface HomePageClientProps {
	initialGirls: any;
	initialCities: string[];
	initialDistricts: { [city: string]: string[] };
	initialCityCounts: { [city: string]: number };
	cardsPerPage: number;
}

export default function HomePageClient({ initialGirls, initialCities, initialDistricts, initialCityCounts, cardsPerPage }: HomePageClientProps) {
	const [girlsData, setGirlsData] = useState<any[]>(initialGirls.girls);
	const [currentPage, setCurrentPage] = useState(1);
	const [totalPages, setTotalPages] = useState(initialGirls.totalPages);
	const [error, setError] = useState<string | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [filters, setFilters] = useState({
		city: "",
		district: "",
		cup: "",
		price: "",
		type: "",
		service: "",
	});
	const [cities] = useState<string[]>(initialCities);
	const [districts] = useState<{ [city: string]: string[] }>(initialDistricts);
	const [currentCityDistricts, setCurrentCityDistricts] = useState<string[]>([]);
	const [cups, setCups] = useState<{ [key: string]: number }>({});
	const [types, setTypes] = useState<{ [key: string]: number }>({});
	const [services, setServices] = useState<{ [key: string]: number }>({});
	const [selectedDistrict, setSelectedDistrict] = useState<string>("_all");
	const [currentCardsPerPage, setCurrentCardsPerPage] = useState(cardsPerPage);
	const [isMobile, setIsMobile] = useState(false);
	const [cityDataCounts] = useState<{ [city: string]: number }>(initialCityCounts);
	const [districtCounts, setDistrictCounts] = useState<{ [key: string]: number }>({});
	const [priceCounts, setPriceCounts] = useState<{ [key: string]: number }>({});
	const { data: session, status } = useSession();
	const router = useRouter();

	const breakpointColumnsObj = useMemo(
		() => ({
			default: 5,
			2000: 4,
			1600: 3,
			1200: 2,
			700: 1,
		}),
		[]
	);

	useEffect(() => {
		const handleResize = () => {
			setIsMobile(window.innerWidth <= 768);
		};
		handleResize();
		window.addEventListener("resize", handleResize);
		return () => window.removeEventListener("resize", handleResize);
	}, []);

	const fetchGirls = useCallback(async () => {
		setIsLoading(true);
		setError(null);
		try {
			const response = await getGirls(currentPage, currentCardsPerPage, filters);
			setGirlsData(response.girls);
			setTotalPages(response.totalPages);
		} catch (error) {
			setError("Failed to fetch data");
		} finally {
			setIsLoading(false);
		}
	}, [currentPage, currentCardsPerPage, filters]);

	useEffect(() => {
		fetchGirls();
	}, [fetchGirls]);

	useEffect(() => {
		const loadCitySpecificData = async () => {
			if (filters.city) {
				try {
					const [cupData, typeData, serviceData, priceData, districtData] = await Promise.all([
						getCups(filters.city, filters.price),
						getTypes(filters.city, filters.price),
						getServices(filters.city, filters.price),
						getPriceCounts(filters.city),
						getDistrictCounts(filters.city),
					]);
					setCups(cupData);
					setTypes(typeData);
					setServices(serviceData);
					setPriceCounts(priceData);
					setDistrictCounts(districtData);
					setCurrentCityDistricts(Object.keys(districtData));
				} catch (error) {
					console.error("Error loading city-specific data:", error);
					setError("Failed to load city-specific data");
				}
			} else {
				setCups({});
				setTypes({});
				setServices({});
				setPriceCounts({});
				setDistrictCounts({});
				setCurrentCityDistricts([]);
			}
		};
		loadCitySpecificData();
	}, [filters.city, filters.price]);

	const handleFilterChange = async (filterName: string, value: string) => {
		const newFilters = {
			...filters,
			[filterName]: value === "_all" ? "" : value,
		};
		setFilters(newFilters);
		setCurrentPage(1);
		setIsLoading(true);

		try {
			const response = await getGirls(
				1,
				currentCardsPerPage,
				newFilters
			);
			setGirlsData(response.girls);
			setTotalPages(response.totalPages);

			if (filterName === "city" && value !== "_all") {
				setCurrentCityDistricts(districts[value] || []);
				const districtCountsResponse = await getDistrictCounts(value);
				setDistrictCounts(districtCountsResponse);
			}
		} catch (err) {
			console.error("Failed to fetch filtered data:", err);
			setError("Failed to load filtered data. Please try again later.");
		} finally {
			setIsLoading(false);
		}
	};

	const handlePageChange = async (page: number) => {
		setCurrentPage(page);
		setIsLoading(true);
		try {
			const response = await getGirls(
				page,
				currentCardsPerPage,
				filters
			);
			setGirlsData(response.girls);
			setTotalPages(response.totalPages);
		} catch (err) {
			console.error("Failed to fetch girls:", err);
			setError("Failed to load data. Please try again later.");
		} finally {
			setIsLoading(false);
		}
	};

	const renderSelectOption = (value: string, label: string, count?: number) => (
		<SelectItemWrapper value={value}>
			{label} {count !== undefined && `(${count})`}
		</SelectItemWrapper>
	);

	const sortedOptions = useCallback((options: { [key: string]: number }) => {
		return Object.entries(options)
			.sort(([, countA], [, countB]) => countB - countA)
			.map(([value, count]) => ({ value, count }));
	}, []);

	const selectClassName = "w-full md:w-[180px] h-9 px-2 text-sm bg-dark-secondary text-dark-foreground border-dark-accent border-r-0 last:border-r";

	const handleCityClick = (city: string) => {
		if (status !== "authenticated") {
			router.push("/login");
		} else {
			router.push(`/city/${cityNameToUrl[city]}`);
		}
	};

	return (
		<div className="w-full">
			<main className="w-full">
				<div className="flex flex-wrap gap-4 mb-6">
					{/* 筛选器 */}
					{!isMobile && (
						<>
							<Select onValueChange={(value) => handleFilterChange("city", value)} value={filters.city || "_all"}>
								<SelectTrigger className="w-full md:w-[180px] h-9 px-2 text-sm bg-dark-secondary text-dark-foreground border-dark-accent">
									<SelectValue placeholder="选择城市" />
								</SelectTrigger>
								<SelectContent className="bg-dark-secondary text-dark-foreground">
									<SelectItem value="_all">所有城市</SelectItem>
									{cities.map((city) => (
										<SelectItemWrapper
											key={city}
											value={city}
										>
											{city} ({cityDataCounts[city] || 0})
										</SelectItemWrapper>
									))}
								</SelectContent>
							</Select>

							<Select onValueChange={(value) => handleFilterChange("district", value)} value={filters.district || "_all"}>
								<SelectTrigger className="w-full md:w-[180px] h-9 px-2 text-sm bg-dark-secondary text-dark-foreground border-dark-accent">
									<SelectValue placeholder="选择区域" />
								</SelectTrigger>
								<SelectContent className="bg-dark-secondary text-dark-foreground">
									<SelectItem value="_all">所有区域</SelectItem>
									{currentCityDistricts.map((district) => (
										<SelectItem key={district} value={district || "_unknown"}>
											{district || "未知"} ({districtCounts[district] || 0})
										</SelectItem>
									))}
								</SelectContent>
							</Select>

							<Select onValueChange={(value) => handleFilterChange("price", value)} value={filters.price || "_all"}>
								<SelectTrigger className="w-full md:w-[180px] h-9 px-2 text-sm bg-dark-secondary text-dark-foreground border-dark-accent">
									<SelectValue placeholder="选择档次" />
								</SelectTrigger>
								<SelectContent className="bg-dark-secondary text-dark-foreground">
									<SelectItem value="_all">所有档次</SelectItem>
									{Object.entries(priceCounts)
										.sort((a, b) => b[1] - a[1])
										.map(([price, count]) => (
											<SelectItem key={price} value={price || "_unknown"}>
												{price || "未知"} ({count.toString()})
											</SelectItem>
										))}
								</SelectContent>
							</Select>

							<Select onValueChange={(value) => handleFilterChange("cup", value)} value={filters.cup || "_all"}>
								<SelectTrigger className="w-full md:w-[180px] h-9 px-2 text-sm bg-dark-secondary text-dark-foreground border-dark-accent">
									<SelectValue placeholder="选择罩杯" />
								</SelectTrigger>
								<SelectContent className="bg-dark-secondary text-dark-foreground">
									<SelectItem value="_all">所有罩杯</SelectItem>
									{Object.entries(cups)
										.sort((a, b) => b[1] - a[1])
										.map(([cup, count]) => (
											<SelectItem key={cup} value={cup || "_unknown"}>
												{cup || "未知"} ({count.toString()})
											</SelectItem>
										))}
								</SelectContent>
							</Select>

							<Select onValueChange={(value) => handleFilterChange("type", value)} value={filters.type || "_all"}>
								<SelectTrigger className="w-full md:w-[180px] h-9 px-2 text-sm bg-dark-secondary text-dark-foreground border-dark-accent">
									<SelectValue placeholder="选择类型" />
								</SelectTrigger>
								<SelectContent className="bg-dark-secondary text-dark-foreground">
									<SelectItem value="_all">所有类型</SelectItem>
									{Object.entries(types)
										.sort((a, b) => b[1] - a[1])
										.map(([type, count]) => (
											<SelectItem key={type} value={type || "_unknown"}>
												{type || "未知"} ({count.toString()})
											</SelectItem>
										))}
								</SelectContent>
							</Select>

							<Select onValueChange={(value) => handleFilterChange("service", value)} value={filters.service || "_all"}>
								<SelectTrigger className="w-full md:w-[180px] h-9 px-2 text-sm bg-dark-secondary text-dark-foreground border-dark-accent">
									<SelectValue placeholder="选择服务" />
								</SelectTrigger>
								<SelectContent className="bg-dark-secondary text-dark-foreground">
									<SelectItem value="_all">所有服务</SelectItem>
									{Object.entries(services)
										.sort((a, b) => b[1] - a[1])
										.map(([service, count]) => (
											<SelectItem key={service} value={service || "_unknown"}>
												{service || "未知"} ({count.toString()})
											</SelectItem>
										))}
								</SelectContent>
							</Select>
						</>
					)}
				</div>
				{isLoading ? (
					<div className="flex justify-center items-center h-64">
						<div className="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-dark-accent"></div>
					</div>
				) : (
					<>
						<Masonry breakpointCols={breakpointColumnsObj} className="my-masonry-grid" columnClassName="my-masonry-grid_column">
							{girlsData.map((girl) => (
								<div key={girl.id} className="mb-2 sm:mb-3">
									<GirlCard data={girl} />
								</div>
							))}
						</Masonry>
						<div className={`mt-8 mb-4 ${isMobile ? 'pb-20' : ''}`}>
							<Pagination
								currentPage={currentPage}
								totalPages={totalPages}
								onPageChange={handlePageChange}
								onPageSizeChange={setCurrentCardsPerPage}
								isMobile={isMobile}
							/>
						</div>
					</>
				)}
			</main>
		</div>
	);
}
