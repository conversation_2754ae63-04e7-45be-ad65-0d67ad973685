"use client";

import { useEffect, useState } from "react";
import { Button } from "./ui/button";
import { ChevronUp, ChevronDown } from "lucide-react";

interface ScrollButtonsProps {
  onGoToNextPage?: () => void;
  isLastPage?: boolean;
}

export default function ScrollButtons({ onGoToNextPage, isLastPage = false }: ScrollButtonsProps) {
  const handleScroll = (direction: 'up' | 'down') => {
    const cards = Array.from(document.querySelectorAll('.girl-card'));
    if (!cards.length) return;

    const viewportHeight = window.innerHeight;
    const mobileNavHeight = 56; // 移动端底部导航栏高度
    const adjustedViewportHeight = viewportHeight - (window.innerWidth <= 768 ? mobileNavHeight : 0);
    const scrollY = window.scrollY;
    let targetCard: Element | null = null;

    if (direction === 'down') {
      for (const card of cards) {
        const rect = card.getBoundingClientRect();
        if (rect.top > 10) { // 只要卡片顶部刚露出就算作下一个
          targetCard = card;
          break;
        }
      }
      
      // 如果没有找到目标卡片且不是最后一页，说明已经到达当前页面底部
      if (!targetCard && onGoToNextPage && !isLastPage) {
        onGoToNextPage();
        return;
      }
      
      if (!targetCard) targetCard = cards[cards.length - 1];
    } else {
      for (let i = cards.length - 1; i >= 0; i--) {
        const card = cards[i];
        const rect = card.getBoundingClientRect();
        if (rect.bottom < adjustedViewportHeight - 10) {
          targetCard = cards[Math.max(0, i)];
          break;
        }
      }
      if (!targetCard) targetCard = cards[0];
    }

    if (targetCard) {
      targetCard.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <div className="fixed right-4 top-[60%] md:top-1/2 flex flex-col gap-2 z-50">
      <Button
        variant="secondary"
        size="icon"
        className="rounded-full bg-dark-secondary/30 hover:bg-dark-secondary/50 text-dark-foreground/70 shadow-lg backdrop-blur-sm"
        onClick={() => handleScroll('up')}
      >
        <ChevronUp className="h-4 w-4" />
      </Button>
      <Button
        variant="secondary"
        size="icon"
        className="rounded-full bg-dark-secondary/30 hover:bg-dark-secondary/50 text-dark-foreground/70 shadow-lg backdrop-blur-sm"
        onClick={() => handleScroll('down')}
      >
        <ChevronDown className="h-4 w-4" />
      </Button>
    </div>
  );
}