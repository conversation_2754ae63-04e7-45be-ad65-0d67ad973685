"use client";

import { useState } from "react";
import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import { Loader2, User, Lock } from "lucide-react";
import Link from "next/link";

export default function LoginForm() {
	const [username, setUsername] = useState("");
	const [password, setPassword] = useState("");
	const [error, setError] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const router = useRouter();

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setError("");
		setIsLoading(true);

		try {
			const result = await signIn("credentials", {
				username,
				password,
				redirect: false,
			});

			if (result?.error) {
				setError("登录失败，请检查您的俱乐部匿名ID和密码");
			} else {
				router.push("/");
			}
		} catch (error) {
			setError("登录过程中发生错误，请稍后重试");
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<form onSubmit={handleSubmit} className="space-y-6 mt-8">
			<div className="space-y-5">
				<div className="space-y-2.5">
					<Label htmlFor="username" className="text-sm font-semibold bg-gradient-to-r from-[#FFB800] to-[#FF8A00] bg-clip-text text-transparent flex items-center gap-1.5">
						俱乐部匿名ID
					</Label>
					<div className="relative group">
						<div className="absolute -inset-0.5 bg-gradient-to-r from-[#FFB800]/50 to-[#FF8A00]/50 rounded-lg blur opacity-0 group-hover:opacity-100 transition duration-500 group-focus-within:opacity-100"></div>
						<div className="relative">
							<User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-[#999999] group-hover:text-[#FFB800] transition-colors duration-300" />
							<Input
								type="text"
								id="username"
								value={username}
								onChange={(e) => setUsername(e.target.value)}
								placeholder="输入你的俱乐部匿名ID"
								required
								className="pl-10 bg-[#f8f8f8] hover:bg-white focus:bg-white text-[#222222] placeholder:text-[#999999] rounded-lg border-0 ring-1 ring-inset ring-[#E5E5E5] focus:ring-2 focus:ring-[#FFB800] shadow-sm transition-all duration-300 h-12"
							/>
						</div>
					</div>
				</div>

				<div className="space-y-2.5">
					<Label htmlFor="password" className="text-sm font-semibold bg-gradient-to-r from-[#FFB800] to-[#FF8A00] bg-clip-text text-transparent flex items-center gap-1.5">
						密码
					</Label>
					<div className="relative group">
						<div className="absolute -inset-0.5 bg-gradient-to-r from-[#FFB800]/50 to-[#FF8A00]/50 rounded-lg blur opacity-0 group-hover:opacity-100 transition duration-500 group-focus-within:opacity-100"></div>
						<div className="relative">
							<Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-[#999999] group-hover:text-[#FFB800] transition-colors duration-300" />
							<Input
								type="password"
								id="password"
								value={password}
								onChange={(e) => setPassword(e.target.value)}
								placeholder="输入你的密码"
								required
								className="pl-10 bg-[#f8f8f8] hover:bg-white focus:bg-white text-[#222222] placeholder:text-[#999999] rounded-lg border-0 ring-1 ring-inset ring-[#E5E5E5] focus:ring-2 focus:ring-[#FFB800] shadow-sm transition-all duration-300 h-12"
							/>
						</div>
					</div>
				</div>
			</div>

			{error && (
				<div className="animate-pulse bg-gradient-to-r from-red-500/5 to-red-600/5 border border-red-500/20 rounded-lg p-4 shadow-sm">
					<p className="text-red-500 text-sm flex items-center">
						<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-5 h-5 mr-2 text-red-500">
							<path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
						</svg>
						{error}
					</p>
				</div>
			)}

			<Button
				type="submit"
				disabled={isLoading}
				className="w-full h-12 bg-gradient-to-r from-[#E11D48] to-[#C01441] hover:from-[#CF1940] hover:to-[#B01038] text-white font-bold rounded-lg shadow-lg shadow-[#E11D48]/20 hover:shadow-xl hover:shadow-[#E11D48]/25 focus:ring-2 focus:ring-offset-2 focus:ring-[#E11D48] transform transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
			>
				{isLoading ? (
					<>
						<Loader2 className="mr-2 h-5 w-5 animate-spin" />
						登录中...
					</>
				) : (
					"登录"
				)}
			</Button>

			<div className="text-center mt-7">
				<Link
					href="/register"
					className="px-5 py-2.5 text-sm font-medium text-[#FFB800] hover:text-[#FF8A00] transition duration-300 hover:underline flex justify-center items-center gap-1.5 group"
				>
					<span>没有账号？</span>
					<span className="font-semibold group-hover:text-[#FF8A00] transition-all duration-300 group-hover:translate-x-0.5">点击注册</span>
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-4 h-4 opacity-0 -translate-x-2 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-300">
						<path fillRule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clipRule="evenodd" />
					</svg>
				</Link>
			</div>
		</form>
	);
}
