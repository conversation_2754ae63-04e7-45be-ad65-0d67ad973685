"use client";

import React, { useState, useEffect } from "react";
import dynamic from "next/dynamic";

// 使用动态导入来确保ChatBubble只在客户端渲染
const ChatBubble = dynamic(() => import("./ui/ChatBubble").then(mod => ({ default: mod.ChatBubble })), {
  ssr: false,
});

export function ChatBubbleWrapper() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  
  // 在客户端检查用户是否已登录
  useEffect(() => {
    const checkAuth = () => {
      // 检查localStorage中的登录状态
      if (typeof window !== 'undefined') {
        try {
          // 检查next-auth.session-token cookie
          const hasSessionCookie = document.cookie.split(';').some(item => 
            item.trim().startsWith('next-auth.session-token=') || 
            item.trim().startsWith('__Secure-next-auth.session-token=')
          );
          
          // 也检查localStorage中可能的会话数据
          const hasLocalStorage = localStorage.getItem('next-auth.session-token') !== null;
          
          // 获取当前用户状态的另一种方式 - 检查nextauth.message
          const nextauthMessages = Object.keys(localStorage).filter(key => 
            key.startsWith('nextauth.message')
          );
          const isSignedInFromMessages = nextauthMessages.length > 0 && 
            !localStorage.getItem(nextauthMessages[0])?.includes('signed-out');
          
          const isLoggedIn = hasSessionCookie || hasLocalStorage || isSignedInFromMessages;
          
          console.log('聊天按钮检测: 用户登录状态 =', isLoggedIn);
          console.log('- Cookie检查:', hasSessionCookie);
          console.log('- LocalStorage检查:', hasLocalStorage);
          console.log('- Message检查:', isSignedInFromMessages);
          
          setIsAuthenticated(isLoggedIn);
        } catch (error) {
          console.error('检查登录状态时出错:', error);
          setIsAuthenticated(false);
        }
      }
    };
    
    // 立即检查
    checkAuth();
    
    // 设置定期检查，每5秒检查一次登录状态
    const interval = setInterval(checkAuth, 5000);
    
    // 添加事件监听，以便在用户登录/登出时更新状态
    window.addEventListener('storage', checkAuth);
    
    return () => {
      clearInterval(interval);
      window.removeEventListener('storage', checkAuth);
    };
  }, []);
  
  // 直接显示状态，方便调试
  useEffect(() => {
    console.log('聊天按钮组件: 认证状态 =', isAuthenticated);
  }, [isAuthenticated]);
  
  return <ChatBubble isAuthenticated={isAuthenticated} />;
} 