'use client';

import { useEffect } from 'react';

export default function DisableDevtool() {
  useEffect(() => {
    // 只在生产环境下初始化
      const initDisableDevtool = async () => {
        try {
          // 动态导入disable-devtool库
          const disableDevtool = (await import('disable-devtool')).default;
          
          // 初始化disable-devtool，禁用开发者工具
          disableDevtool({
            // 检测到开发者工具时直接关闭网页
            ondevtoolopen: (type) => {
              // 关闭当前窗口
              window.close();
              
              // 如果window.close()不起作用（某些浏览器可能会阻止），则重定向到空白页
              window.location.href = 'about:blank';
            },
            // 是否在URL中添加随机字符串，防止被缓存
            clearLog: true,
            // 禁用右键菜单
            disableMenu: false,
            // 禁用选择
            disableSelect: false,
            // 禁用复制
            disableCopy: true,
            // 禁用剪切
            disableCut: true,
            // 禁用粘贴
            disablePaste: true,
          });
          
          console.log('开发者工具已禁用');
        } catch (error) {
          console.error('禁用开发者工具失败:', error);
        }
      };

      // 调用初始化函数
      // initDisableDevtool();


    return () => {
      // 清理函数（如果需要）
    };
  }, []);

  return null; // 这个组件不渲染任何内容
} 