"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { AspectRatio } from './ui/aspect-ratio';
import { Building, MapPin, Users } from 'lucide-react';

// 定义城市数据接口
interface CityData {
  city: string;
  count: number;
}

interface FormattedCity {
  name: string;
  pinyin: string;
  image: string | null;
  count: string;
  icon: string;
  character: string;
}

// 主要城市列表，这些城市将使用图片背景，其余使用动态背景
const mainCities = ['北京', '上海', '广州', '深圳', '杭州'];

// 一线城市固定数据
const mainCitiesData: FormattedCity[] = [
  {
    name: '北京',
    pinyin: 'beijing',
    image: '/images/cities/beijing.png',
    count: '100+',
    icon: '🏙️',
    character: '京'
  },
  {
    name: '上海',
    pinyin: 'shanghai',
    image: '/images/cities/shanghai.png',
    count: '100+',
    icon: '🌃',
    character: '沪'
  },
  {
    name: '广州',
    pinyin: 'guangzhou',
    image: '/images/cities/guangzhou.png',
    count: '100+',
    icon: '🌆',
    character: '穗'
  },
  {
    name: '深圳',
    pinyin: 'shenzhen',
    image: '/images/cities/shenzhen.png',
    count: '100+',
    icon: '🏢',
    character: '深'
  },
  {
    name: '杭州',
    pinyin: 'hangzhou',
    image: '/images/cities/hangzhou.png',
    count: '100+',
    icon: '🌉',
    character: '杭'
  }
];

// 城市图片映射
const cityImages: Record<string, string> = {
  '北京': '/images/cities/beijing.png',
  '上海': '/images/cities/shanghai.png',
  '广州': '/images/cities/guangzhou.png',
  '深圳': '/images/cities/shenzhen.png',
  '杭州': '/images/cities/hangzhou.png',
};

// 城市字符映射
const cityCharacters: Record<string, string> = {
  '北京': '京',
  '上海': '沪',
  '广州': '穗',
  '深圳': '深',
  '杭州': '杭',
  '成都': '蓉',
  '重庆': '渝',
  '南京': '宁',
  '武汉': '汉',
  '西安': '秦',
  '苏州': '苏',
  '天津': '津',
  '郑州': '郑',
  '长沙': '长',
  '青岛': '青',
  '宁波': '甬',
  '东莞': '莞',
  '无锡': '锡',
  '厦门': '厦',
  '福州': '福',
};

// 城市拼音映射
const cityPinyin: Record<string, string> = {
  '北京': 'beijing',
  '上海': 'shanghai',
  '广州': 'guangzhou',
  '深圳': 'shenzhen',
  '杭州': 'hangzhou',
  '成都': 'chengdu',
  '重庆': 'chongqing',
  '南京': 'nanjing',
  '武汉': 'wuhan',
  '西安': 'xian',
  '苏州': 'suzhou',
  '天津': 'tianjin',
  '郑州': 'zhengzhou',
  '长沙': 'changsha',
  '青岛': 'qingdao',
  '宁波': 'ningbo',
  '东莞': 'dongguan',
  '无锡': 'wuxi',
  '厦门': 'xiamen',
  '福州': 'fuzhou',
};

// 生成随机颜色，用于动态背景
const getRandomGradient = () => {
  const gradients = [
    'from-purple-700/60 to-indigo-700/60',
    'from-violet-700/60 to-blue-700/60',
    'from-indigo-800/60 to-blue-600/60',
    'from-blue-800/60 to-purple-600/60',
    'from-purple-800/60 to-violet-600/60',
    'from-indigo-700/60 to-violet-600/60',
    'from-violet-800/60 to-indigo-600/60',
    'from-purple-900/60 to-indigo-700/60',
  ];
  return gradients[Math.floor(Math.random() * gradients.length)];
};

// 获取城市字符，如果映射中没有则返回城市名的第一个字
const getCityCharacter = (cityName: string): string => {
  return cityCharacters[cityName] || cityName.charAt(0);
};

// 获取城市拼音，如果映射中没有则返回城市名
const getCityPinyin = (cityName: string): string => {
  return cityPinyin[cityName] || cityName;
};

// 骨架屏城市卡片组件
const CityCardSkeleton = ({ index }: { index: number }) => {
  const gradients = [
    'bg-gradient-to-br from-gray-700/30 to-gray-800/30',
    'bg-gradient-to-br from-gray-800/30 to-gray-700/30',
    'bg-gradient-to-br from-gray-800/30 to-gray-900/30',
    'bg-gradient-to-br from-gray-900/30 to-gray-800/30',
  ];

  const gradient = gradients[index % gradients.length];

  return (
    <div className="relative overflow-hidden rounded-xl bg-[#2a2a2a]/80 border border-[#404040]/50 h-full min-h-[100px] md:min-h-[150px]">
      <div className={`absolute inset-0 w-full h-full ${gradient} animate-pulse`}>
        {/* 光晕效果 */}
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(255,255,255,0.05),transparent_80%)]"></div>
      </div>

      <div className="p-2 md:p-4 flex flex-col h-full justify-between relative z-10">
        <div className="flex items-center justify-between mb-1 md:mb-2">
          <div className="w-14 h-4 bg-gray-600/50 rounded-full animate-pulse"></div>
        </div>

        <div className="mt-auto flex items-center gap-1 md:gap-2">
          <div className="w-4 h-4 bg-gray-600/50 rounded-full animate-pulse"></div>
          <div className="w-16 h-6 bg-gray-600/50 rounded-md animate-pulse"></div>
        </div>
      </div>
    </div>
  );
};

export function CityGrid() {
  const [cities, setCities] = useState<FormattedCity[]>(mainCitiesData);
  const [otherCities, setOtherCities] = useState<FormattedCity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAllCities, setShowAllCities] = useState(false);

  // 存储过滤后的主要城市
  const [filteredMainCities, setFilteredMainCities] = useState<FormattedCity[]>(mainCitiesData);

  // 监听showAllCities状态变化，更新显示的城市列表

  // 更新显示的城市列表
  useEffect(() => {
    if (!loading && otherCities.length > 0) {
      if (showAllCities) {
        // 在移动端点击了"查看更多"时，显示所有城市
        setCities([...filteredMainCities, ...otherCities]);
      } else {
        // 移动端未点击"查看更多"或PC端时，只显示前5个其他城市
        const displayedOtherCities = otherCities.slice(0, 5);
        setCities([...filteredMainCities, ...displayedOtherCities]);
      }
    }
  }, [showAllCities, loading, otherCities, filteredMainCities]);

  useEffect(() => {
    // 初始状态即展示一线城市（不需要等待API）
    setCities(mainCitiesData);

    // 获取其他城市数据
    const fetchOtherCities = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/city-counts');

        if (!response.ok) {
          throw new Error(`网络请求失败: ${response.status}`);
        }

        const responseData = await response.json();
        console.log('API返回数据:', responseData); // 调试日志

        // 检查数据格式并提取数组
        let citiesData: CityData[] = [];

        if (Array.isArray(responseData)) {
          citiesData = responseData;
        } else if (responseData && typeof responseData === 'object') {
          // 尝试从不同的常见属性名称中获取数组
          if (Array.isArray(responseData.data)) {
            citiesData = responseData.data;
          } else if (Array.isArray(responseData.cities)) {
            citiesData = responseData.cities;
          } else if (Array.isArray(responseData.results)) {
            citiesData = responseData.results;
          } else if (Array.isArray(responseData.items)) {
            citiesData = responseData.items;
          } else {
            // 尝试从对象中提取城市数据
            // 假设格式可能是 { "北京": 100, "上海": 80, ... }
            const extractedData: CityData[] = [];
            for (const [city, count] of Object.entries(responseData)) {
              if (typeof count === 'number' || typeof count === 'string') {
                extractedData.push({
                  city,
                  count: typeof count === 'string' ? parseInt(count) : count
                });
              }
            }

            if (extractedData.length > 0) {
              citiesData = extractedData;
            }
          }
        }

        // 处理主要城市数据 - 更新主要城市的数量
        const updatedMainCities = [...mainCitiesData];
        for (const cityData of citiesData) {
          if (mainCities.includes(cityData.city)) {
            // 找到对应的主要城市索引
            const index = updatedMainCities.findIndex(c => c.name === cityData.city);
            if (index !== -1) {
              // 更新城市数量
              updatedMainCities[index] = {
                ...updatedMainCities[index],
                count: `${cityData.count}+`
              };
            }
          }
        }

        // 过滤主要城市 - 移除数量小于5的城市
        const filteredMainCitiesList = updatedMainCities.filter(city => {
          // 解析数量（移除"+"符号）
          const count = parseInt(city.count);
          // 过滤掉数量小于5的城市
          return !isNaN(count) && count >= 5;
        });

        // 更新过滤后的主要城市状态
        setFilteredMainCities(filteredMainCitiesList);

        // 处理非一线城市的数据
        const otherCitiesData = citiesData
          .filter((city: CityData) => {
            // 过滤掉主要城市
            if (mainCities.includes(city.city)) return false;
            // 过滤掉城市名为"未知"的城市
            if (city.city === "未知") return false;
            // 过滤掉数量小于5的城市
            if (city.count < 5) return false;
            // 其他城市保留
            return true;
          })
          .map((city: CityData) => ({
            name: city.city,
            pinyin: getCityPinyin(city.city),
            image: null, // 非一线城市不使用图片
            count: `${city.count}+`,
            icon: ['🌆', '🏯', '🌁', '🏛️', '🌊'][Math.floor(Math.random() * 5)],
            character: getCityCharacter(city.city)
          }))
          .sort((a: FormattedCity, b: FormattedCity) => {
            const countA = parseInt(a.count);
            const countB = parseInt(b.count);
            return countB - countA; // 降序排列
          });

        setOtherCities(otherCitiesData);

        // 无论PC端还是移动端，默认都只显示前5个其他城市
        const displayedOtherCities = otherCitiesData.slice(0, 5);
        // 合并过滤后的一线城市和显示的其他城市
        setCities([...filteredMainCitiesList, ...displayedOtherCities]);
      } catch (error) {
        console.error('获取城市数据失败:', error);
        setError(error instanceof Error ? error.message : '获取城市数据失败');
        // 仍然显示一线城市数据（默认所有主要城市都有足够数量）
        setFilteredMainCities(mainCitiesData);
        setCities(mainCitiesData);
      } finally {
        setLoading(false);
      }
    };

    fetchOtherCities();
  }, []);

  // 如果出错但至少有一线城市数据，不显示错误信息，只在控制台打印错误
  const shouldShowError = error && cities.length <= mainCities.length;

  // 错误处理
  if (shouldShowError) {
    return (
      <div className="w-full py-8 text-center">
        <p className="text-red-500 mb-2">请求失败: {error}</p>
        <button
          className="px-4 py-2 bg-[#e50049] text-white rounded-md hover:bg-[#d00040] transition-colors"
          onClick={() => window.location.reload()}
        >
          重试
        </button>
      </div>
    );
  }

  // 渲染城市卡片的函数
  const renderCityCard = (city: FormattedCity) => {
    // 判断是否是主要城市（使用图片背景）
    const isMainCity = mainCities.includes(city.name);
    const cityGradient = getRandomGradient();

    return (
      <Link
        key={city.name}
        href={`/city/${city.pinyin}`}
        className="block"
      >
        <div className="relative group overflow-hidden rounded-xl bg-[#2a2a2a] border border-[#404040] h-full min-h-[100px] md:min-h-[150px] transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:shadow-[#e50049]/10 hover:border-[#e50049]/30">
          {isMainCity ? (
            // 背景图片 - 固定使用图片（一线城市）
            <div className="absolute inset-0 w-full h-full opacity-85 group-hover:opacity-95 transition-opacity duration-300">
              <Image
                src={city.image || ''}
                alt={city.name}
                fill
                sizes="(max-width: 768px) 50vw, 20vw"
                className="object-cover"
                priority={isMainCity}
              />
            </div>
          ) : (
            // 动态生成的背景 - 用于其他城市
            <div className={`absolute inset-0 w-full h-full bg-gradient-to-br ${cityGradient}`}>
              {/* 统一柔和纹理 */}
              <div className="absolute inset-0 w-full h-full bg-[radial-gradient(ellipse_at_center,rgba(255,255,255,0.07),transparent_80%)]"></div>
              {/* 光晕效果 */}
              <div className="absolute inset-0 w-full h-full bg-[radial-gradient(circle_at_30%_30%,rgba(255,255,255,0.1),transparent_60%)]"></div>
              {/* 渐变叠加 */}
              <div className="absolute inset-0 w-full h-full opacity-40 bg-gradient-to-t from-black/20 to-transparent"></div>

              {/* 大型城市汉字水印 */}
              <div className="absolute inset-0 w-full h-full flex items-center justify-center select-none pointer-events-none">
                <span className="text-[90px] md:text-[140px] font-bold text-shadow-lg flex items-center justify-center"
                      style={{
                        textShadow: '0 0 15px rgba(0,0,0,0.3), 0 0 30px rgba(0,0,0,0.2)',
                        color: 'rgba(255,255,255,0.85)',
                        opacity: 0.9,
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)'
                      }}>{city.character}</span>
              </div>
            </div>
          )}

          {/* 渐变叠加层 */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/15 to-transparent group-hover:from-black/5 group-hover:to-transparent transition-all duration-300"></div>
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,rgba(255,255,255,0.25),transparent_70%)] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

          <div className="p-2 md:p-4 flex flex-col h-full justify-between relative z-10">
            <div className="flex items-center justify-between mb-1 md:mb-2">
              <span className="text-[10px] md:text-xs text-white/80 px-1.5 md:px-2 py-0.5 bg-[#e50049]/10 backdrop-blur-sm rounded-full flex items-center gap-1">
                <Users className="w-2.5 h-2.5 md:w-3 md:h-3" />
                {city.count}
              </span>
            </div>

            <div className="mt-auto flex items-center gap-1 md:gap-2">
              <MapPin className="w-3 h-3 md:w-4 md:h-4 text-[#e50049]" />
              <h3 className="text-base md:text-lg font-bold text-white">{city.name}</h3>
            </div>
          </div>
        </div>
      </Link>
    );
  };

  return (
    <div className="w-full">
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 w-full max-w-full overflow-hidden">
        {/* 渲染所有城市卡片 */}
        {cities.map(renderCityCard)}

        {/* 加载中的骨架屏 */}
        {loading &&
          [...Array(5)].map((_, index) => (
            <CityCardSkeleton key={`skeleton-${index}`} index={index} />
          ))
        }
      </div>

      {/* 查看更多按钮 - 只有在有更多城市可显示时才显示，且只在移动端显示 */}
      {!loading && otherCities.length > 5 && (
        <div className="w-full flex justify-center mt-6 md:hidden">
          <button
            onClick={() => setShowAllCities(!showAllCities)}
            className="px-6 py-2 bg-gradient-to-r from-[#e50049] to-[#c4103e] text-white rounded-full hover:shadow-lg hover:shadow-[#e50049]/20 transition-all duration-300 flex items-center gap-2 text-sm font-medium"
          >
            {showAllCities ? (
              <>
                收起
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-chevron-up">
                  <path d="m18 15-6-6-6 6"/>
                </svg>
              </>
            ) : (
              <>
                查看更多城市
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-chevron-down">
                  <path d="m6 9 6 6 6-6"/>
                </svg>
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );
}
