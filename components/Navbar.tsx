"use client";

import { <PERSON><PERSON> } from "./ui/button";
import Link from "next/link";
import { <PERSON><PERSON>, User, LogOut, MessageCircle, MessageCircle as MessageCircleIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { showNewMessage } from "@intercom/messenger-js-sdk";
import { signOut } from "next-auth/react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import "../styles/navigation.css";

const TelegramIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 24 24" className="text-[#0088cc]">
    <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
  </svg>
);

interface NavbarProps {
  user: {
    username: string;
    avatar: string;
    isVIP: boolean;
  } | null;
  status: string;
  isMobile: boolean;
  isSidebarOpen: boolean;
  setIsSidebarOpen: (isOpen: boolean) => void;
}

export default function Navbar({
  user,
  status,
  isMobile,
  isSidebarOpen,
  setIsSidebarOpen,
}: NavbarProps) {
  const router = useRouter();

  const handleLogout = async () => {
    await signOut({ redirect: false });
    router.push("/");
  };

  return (
    <nav className="nav-container">
      <div className="bg-dark-background backdrop-blur-sm bg-opacity-90 h-full">
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-3 flex justify-between items-center h-full">
          <div className="flex items-center w-1/4">
            {isMobile && (
              <div className="flex items-center justify-center">
                <Button
                  variant="ghost"
                  onClick={() => setIsSidebarOpen(!isSidebarOpen)}
                  className="w-8 h-8 p-0 flex items-center justify-center hover:bg-[#292929] rounded-lg"
                >
                  <Menu className="h-6 w-6 text-[#ffa31a]" />
                </Button>
              </div>
            )}
            <span className="text-base font-bold whitespace-nowrap">
              <Link href="/">
                佳妮 ☀️ 俱乐部
              </Link>
            </span>
          </div>

          <div className="flex-1 flex justify-center">
            <div className="hidden md:flex items-center space-x-12">
              <Link href="/" className="text-white hover:text-[#ffa31a] transition-colors duration-200 text-lg font-medium">
                主页
              </Link>
              <Link href="/city" className="text-white hover:text-[#ffa31a] transition-colors duration-200 text-lg font-medium">
                ❤️‍🔥 资源库
              </Link>
              <Link href="/guide" className="text-white hover:text-[#ffa31a] transition-colors duration-200 text-lg font-medium">
                约会指南
              </Link>
              <Link href="/jianier5kw" className="text-white hover:text-[#ffa31a] transition-colors duration-200 text-lg font-medium">
                💎 ⁵ꪝ极品营
              </Link>
              <Link href="/feedback" className="text-white hover:text-[#ffa31a] transition-colors duration-200 text-lg font-medium">
                🦋 至真园™
              </Link>
              <Link href="/linktree" className="text-white hover:text-[#ffa31a] transition-colors duration-200 text-lg font-medium">
                导航
              </Link>
            </div>
          </div>

          <div className="flex items-center w-1/4 justify-end">
            {status === "authenticated" && user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="relative h-8 w-8 rounded-full hover:ring-2 hover:ring-[#ffa31a] transition-all duration-200 bg-[#1a1a1a] p-0 flex items-center justify-center"
                  >
                    <User className="h-5 w-5 text-[#ffa31a]" />
                    {user.isVIP && (
                      <div className="absolute -top-0.5 -right-0.5 bg-[#e50049] rounded-full w-2.5 h-2.5 border border-dark-background" />
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-56 bg-dark-background border border-[#292929] rounded-lg shadow-lg"
                  align="end"
                  forceMount
                >
                  <DropdownMenuLabel className="px-3 py-2 flex items-center gap-2 border-b border-[#292929]">
                    <div className="h-8 w-8 rounded-full bg-[#1a1a1a] flex items-center justify-center">
                      <User className="h-5 w-5 text-[#ffa31a]" />
                    </div>
                    <div className="flex flex-col">
                      <p className="text-sm font-medium text-white">
                        {user.username}
                      </p>
                      <p className="text-xs text-[#ffa31a]">
                        {user.isVIP ? "VIP会员" : "普通会员"}
                      </p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuItem className="px-3 py-2 hover:bg-[#292929] hover:text-[#ffa31a] transition-colors duration-200">
                    <Link href="/profile" className="flex items-center w-full">
                      <User className="mr-2 h-5 w-5 text-[#ffa31a]" />
                      <span className="text-sm text-white">个人资料</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator className="bg-[#292929]" />
                  <DropdownMenuItem
                    className="px-3 py-2 hover:bg-[#292929] hover:text-[#ffa31a] transition-colors duration-200"
                    onClick={() => showNewMessage("我需要帮助")}
                  >
                    <span className="text-sm text-white">💬 网页客服</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="px-3 py-2 hover:bg-[#292929] hover:text-[#ffa31a] transition-colors duration-200"
                  >
                    <Link href="https://t.me/jianierbot" target="_blank" className="flex items-center w-full">
                      <span className="text-sm text-white">✈️ Telegram 客服</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator className="bg-[#292929]" />
                  <DropdownMenuItem
                    className="px-3 py-2 hover:bg-[#292929] hover:text-[#ffa31a] transition-colors duration-200"
                    onClick={handleLogout}
                  >
                    <LogOut className="mr-2 h-4 w-4 text-[#ffa31a]" />
                    <span className="text-sm text-white">登出</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Button
                onClick={() => router.push("/login")}
                className="bg-[#e50049] text-white hover:bg-[#c8003f] transition-colors duration-200 text-sm px-4 py-1.5"
                size="sm"
              >
                🌟 入会
              </Button>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}