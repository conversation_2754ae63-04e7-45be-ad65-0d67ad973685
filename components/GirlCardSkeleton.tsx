import React from "react";
import { Card, CardContent } from "./ui/card";
import { AspectRatio } from "./ui/aspect-ratio";
import { Skeleton } from "./ui/skeleton";

const GirlCardSkeleton = () => {
  return (
    <Card className="overflow-hidden bg-card/50 backdrop-blur-sm transform transition-all duration-300 hover:scale-[1.02] max-w-full">
      <CardContent className="p-0">
        {/* 图片区域骨架屏 */}
        <AspectRatio ratio={3/4} className="overflow-hidden">
          <Skeleton className="w-full h-full" />
        </AspectRatio>

        {/* 内容区域骨架屏 */}
        <div className="p-3 space-y-2 sm:space-y-3">
          {/* 标题 */}
          <div className="space-y-1 sm:space-y-2">
            <Skeleton className="h-4 sm:h-5 w-2/3" />
          </div>

          {/* 标签 */}
          <div className="flex flex-wrap gap-1 sm:gap-1.5">
            {[1, 2, 3, 4].map((i) => (
              <Skeleton key={i} className="h-3 sm:h-4 w-10 sm:w-12 rounded-full" />
            ))}
          </div>

          {/* 个人信息 */}
          <div className="flex items-center gap-1 sm:gap-2">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-3 sm:h-3.5 w-10 sm:w-14" />
            ))}
          </div>

          {/* 评分和价格 */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-0.5 sm:gap-1">
              {[1, 2, 3, 4, 5].map((i) => (
                <Skeleton key={i} className="h-2.5 sm:h-3 w-2.5 sm:w-3" />
              ))}
            </div>
            <Skeleton className="h-3.5 sm:h-4 w-14 sm:w-16" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default GirlCardSkeleton; 