"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState, useRef } from "react";
import { Button } from "./ui/button";
import { motion } from "framer-motion";
import { Diamond, Star, Crown, Check } from "lucide-react";
import { toast } from "react-hot-toast";
import Intercom, { showNewMessage } from "@intercom/messenger-js-sdk";

type VIPLevel = {
  name: string;
  price: number;
  benefits: string[];
  icon: any;
  description: string;
  color: string;
  recommended?: boolean;
}

const vipLevels: VIPLevel[] = [
  {
    name: "星级 🌟 会员",
    price: 200,
    benefits: [
      "首次约会抵扣",
      "3K精选资源库",
      "建立会员档案",
      "内部会员群资格",
      "任意城市安排(差旅)",
      "优惠活动资格",

    ],
    icon: Star,
    description: "解锁基础特权，开启您的高端约会之旅",
    color: "#ffa31a"
  },
  {
    name: "钻石 💎 会员",
    price: 588,
    benefits: [
	  "5K/W起 ⁵ꪝ极品营 顶级资源库",
      "包含星级会员",
	  "高端隐藏资源",
      "满10免1特权",
      "海外资源",
	  "含验证视频",
      "专属客服服务",
    ],
    icon: Diamond,
    description: "尊享更多特权，体验顶级约会服务",
    color: "#00a6ff",
    recommended: true
  },
  {
    name: "金主 🌞 爸爸",
    price: 1688,
    benefits: [
      "包含钻石会员",
      "包养资源",
      "伴游资源",
      "网红明星资源",
      "处女资源",
      "24/7专属管家",
    ],
    icon: Crown,
    description: "至尊VIP待遇，独享顶级资源",
    color: "#e50049"
  },
];

export default function VIPClient() {
	const { data: session, status } = useSession();
	const router = useRouter();
	const [selectedVIP, setSelectedVIP] = useState(vipLevels[1]);
	const toastShown = useRef(false);
	
	useEffect(() => {
		if (session?.user?.isVIP && !toastShown.current) {
			toastShown.current = true;
			toast.success("您已经开通VIP，跳转到资源库页面", {
				duration: 3000,
				position: "top-center",
			});
			setTimeout(() => {
				router.push("/city");
			}, 1000);
		}
	}, [session, router]);
	
	if (status === "loading") {
		return <div className="flex items-center justify-center min-h-screen">
			<div className="text-xl text-[#e50049]">加载中...</div>
		</div>;
	}

	return (
		<div className="relative min-h-screen bg-background pt-16 px-4 sm:px-6 md:px-8 overflow-hidden">
			{/* Animated Background Gradient */}
			<div className="absolute inset-0 w-full h-full bg-gradient-to-b from-background to-background">
				<div className="absolute inset-0 w-full h-full bg-[radial-gradient(ellipse_at_top_right,rgba(229,0,73,0.15),rgba(229,0,73,0)_50%)]"></div>
				<div className="absolute inset-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(229,0,73,0.1),rgba(229,0,73,0)_50%)]"></div>
			</div>

			{/* Soft Pattern Background */}
			<div className="absolute inset-0 w-full h-full opacity-30">
				<div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(229,0,73,0.05)_1px,transparent_1px)] bg-[size:32px_32px]"></div>
				<div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(229,0,73,0.05)_1px,transparent_1px)] bg-[size:24px_24px] rotate-15"></div>
			</div>

			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5 }}
				className="relative container mx-auto max-w-6xl px-3 sm:px-6"
			>
				{/* Floating Elements */}
				<div className="absolute -top-20 -left-20 w-60 h-60 bg-[#e50049]/5 rounded-full filter blur-3xl animate-pulse"></div>
				<div className="absolute -top-20 -right-20 w-60 h-60 bg-[#e50049]/5 rounded-full filter blur-3xl animate-pulse delay-1000"></div>

				<div className="text-center mb-12 sm:mb-16">

					<h1 className="text-[36px] md:text-[60px] font-bold tracking-tight mb-3 sm:mb-4">
						<span className="relative inline-block">
							<span className="absolute -inset-2 bg-gradient-to-r from-[#e50049]/20 to-[#ff1464]/20 blur-xl group-hover:blur-2xl transition-all duration-500"></span>
							<span className="relative z-10 inline-block text-white">开通 佳妮 ☀️ 俱乐部 VIP</span>
						</span>
					</h1>
					<p className="text-lg sm:text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto mb-6 sm:mb-8">
						选择适合您的会员等级，开启精彩约会之旅
					</p>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-3 gap-3 sm:gap-6 md:gap-8 mb-12 sm:mb-16">
					{vipLevels.map((level, index) => (
						<motion.div
							key={index}
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.5, delay: index * 0.1 }}
							whileHover={{ scale: 1.02 }}
							whileTap={{ scale: 0.98 }}
							className={`
								relative overflow-hidden rounded-2xl border cursor-pointer
								${level.recommended 
									? "border-[#e50049] bg-[#e50049]/5 scale-105 shadow-xl shadow-[#e50049]/20 z-10" 
									: selectedVIP === level 
										? "border-muted bg-muted/50"
										: "border-muted/50 hover:border-muted bg-background/50"
								}
								transition-all duration-300
							`}
							onClick={() => setSelectedVIP(level)}
						>
							{/* Card Background Effects */}
							<div className="absolute inset-0">
								<div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/5"></div>
								<div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,rgba(255,255,255,0.05),transparent_50%)]"></div>
							</div>

							{level.recommended && (
								<div className="absolute -right-7 top-1 rotate-45 bg-[#e50049] text-white text-sm py-1 px-8 shadow-lg">
									推荐
								</div>
							)}

							<div className="relative p-6 sm:p-6 md:p-8 flex flex-col items-center text-center">
								<div className="flex flex-col items-center mb-6 sm:mb-7">
									<div className={`w-12 h-12 sm:w-14 sm:h-14 rounded-xl flex items-center justify-center bg-gradient-to-br mb-3`} style={{ backgroundColor: `${level.color}20` }}>
										<level.icon className={`w-6 h-6 sm:w-7 sm:h-7`} style={{ color: level.color }} />
									</div>
									<h2 className="text-xl sm:text-2xl font-bold" style={{ color: level.color }}>{level.name}</h2>
									<p className="text-xs sm:text-sm text-muted-foreground mt-1 sm:mt-2 max-w-[250px]">{level.description}</p>
								</div>
								
								<div className="flex items-baseline mb-7 sm:mb-8 justify-center">
									<span className="text-lg mr-1">¥</span>
									<span className="text-3xl sm:text-4xl font-bold" style={{ color: level.color }}>{level.price}</span>
									<span className="text-muted-foreground ml-2">/永久</span>
								</div>
								
								<div className="grid grid-cols-2 gap-x-3 gap-y-4 sm:gap-y-4 w-full mb-3 sm:mb-4">
									{level.benefits.map((benefit, i) => (
										<div 
											key={i} 
											className={`flex items-start gap-2 text-xs sm:text-sm ${benefit.length > 8 ? 'col-span-2' : ''}`}
										>
											<div className={`w-4 h-4 sm:w-5 sm:h-5 rounded-full flex-shrink-0 flex items-center justify-center mt-0.5`} style={{ backgroundColor: `${level.color}20` }}>
												<Check className={`w-2.5 h-2.5 sm:w-3 sm:h-3`} style={{ color: level.color }} />
											</div>
											<span className="text-white text-left">{benefit}</span>
										</div>
									))}
								</div>
							</div>
						</motion.div>
					))}
				</div>

				{/* 联系客服开通会员 */}
				<div className="relative max-w-xl mx-auto mt-10 sm:mt-12">
					<div className="absolute inset-0 bg-gradient-to-r from-[#e50049]/20 via-transparent to-[#e50049]/20 rounded-2xl blur-lg"></div>
					<div className="relative bg-muted/30 backdrop-blur-sm p-5 sm:p-6 rounded-2xl border border-muted text-center">
						<h3 className="text-base sm:text-lg font-semibold mb-2 sm:mb-3 text-[#e50049]">联系客服开通会员</h3>
						<p className="text-xs sm:text-sm text-muted-foreground mb-4 sm:mb-5 max-w-lg mx-auto">
							选择以下任意方式联系客服，我们将为您提供专业的开通服务
						</p>
						
						<div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 max-w-md mx-auto">
							<motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
								<Button 
									onClick={() => window.open('https://t.me/Jianier1314', '_blank')}
									className="w-full bg-[#0088cc] hover:bg-[#0099dd] text-white transition-all duration-300 text-sm sm:text-base py-3 sm:py-4 rounded-xl flex items-center justify-center gap-2 shadow-lg shadow-[#0088cc]/20 hover:shadow-xl hover:shadow-[#0088cc]/30"
								>
									<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 24 24">
										<path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
									</svg>
									Telegram 客服
								</Button>
							</motion.div>

							<motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
								<Button 
									onClick={() => showNewMessage(`开通 ${selectedVIP.name}`)}
									className="w-full bg-[#e50049] hover:bg-[#ff1464] text-white transition-all duration-300 text-sm sm:text-base py-3 sm:py-4 rounded-xl flex items-center justify-center gap-2 shadow-lg shadow-[#e50049]/20 hover:shadow-xl hover:shadow-[#e50049]/30"
								>
									<svg className="w-4 h-4 sm:w-5 sm:h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
										<path strokeLinecap="round" strokeLinejoin="round" d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"/>
									</svg>
									在线客服
								</Button>
							</motion.div>
						</div>
					</div>
				</div>
			</motion.div>
		</div>
	);
}
