"use client";

import React, { useState } from "react";
import Image from "next/image";
import { Card, CardContent } from "./ui/card";
import { Progress } from "./ui/progress";
import { Girl } from "../lib/api";
import { useSession } from "next-auth/react";
import { Button } from "./ui/button";
import { Diamond, Star, Crown, Check } from "lucide-react";
import { toast } from "react-hot-toast";
import { AspectRatio } from "./ui/aspect-ratio";
import { useRouter } from "next/navigation";
import Intercom, { show } from "@intercom/messenger-js-sdk";

interface GirlDetailProps {
	girl: Girl;
}

const GirlDetail: React.FC<GirlDetailProps> = ({ girl }) => {
	const { data: session } = useSession();
	const router = useRouter();
	const [selectedImage, setSelectedImage] = useState<string | null>(null);

	const hasRatings = girl.photoAccuracy || girl.appearance || girl.attitude || girl.serviceQuality || girl.overallRating;

	const renderRatingStars = (rating: number | undefined) => {
		if (!rating) return null;
		
		// 将10分制转换为5星制
		const starRating = (rating / 2);
		const fullStars = Math.floor(starRating);
		const hasHalfStar = starRating % 1 >= 0.5;
		
		return (
			<div className="flex items-center">
				{[...Array(5)].map((_, index) => {
					const isFull = index < fullStars;
					const isHalf = !isFull && index === fullStars && hasHalfStar;
					
					return (
						<div key={index} className="relative">
							<svg
								className={`w-4 h-4 transition-all duration-300 ${
									isFull || isHalf
										? 'text-yellow-400 drop-shadow-glow scale-110'
										: 'text-gray-600'
								}`}
								fill={isFull ? "currentColor" : "none"}
								stroke="currentColor"
								strokeWidth={isFull ? "0" : "1.5"}
								viewBox="0 0 20 20"
							>
								<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
							</svg>
							{isHalf && (
								<div className="absolute inset-0 overflow-hidden w-[50%]">
									<svg
										className="w-4 h-4 text-yellow-400 drop-shadow-glow scale-110"
										fill="currentColor"
										viewBox="0 0 20 20"
									>
										<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
									</svg>
								</div>
							)}
						</div>
					);
				})}
				<span className="ml-2 text-sm font-medium bg-gradient-to-r from-yellow-400/90 to-yellow-500/90 bg-clip-text text-transparent">
					{rating.toFixed(1)}
				</span>
			</div>
		);
	};

	const handleArrange = () => {
		if (!session) {
			toast.error("请登录后联系在线客服");
			router.push("/login");
		} else {
			show();
		}
	};

	return (
		<div className="relative min-h-screen bg-background/95 pt-16 overflow-hidden">
			{/* Main Background */}
			<div className="absolute inset-0 bg-gradient-to-b from-background to-background/90">
				<div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,rgba(229,0,73,0.1),transparent_70%)]"></div>
				<div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_left,rgba(229,0,73,0.1),transparent_70%)]"></div>
			</div>

			{/* Dot Pattern */}
			<div className="absolute inset-0 opacity-10">
				<div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(229,0,73,0.2)_1px,transparent_1px)] bg-[size:24px_24px]"></div>
			</div>

			<div className="relative container mx-auto max-w-6xl px-4 md:px-6">
				{/* Accent Lights */}
				<div className="absolute -top-40 -left-40 w-80 h-80 bg-[#e50049]/5 rounded-full filter blur-3xl"></div>
				<div className="absolute -top-40 -right-40 w-80 h-80 bg-[#e50049]/5 rounded-full filter blur-3xl"></div>

				<div className="text-center mb-16">
					<h1 className="text-[48px] md:text-[60px] font-bold tracking-tight mb-4">
						<span className="relative inline-block">
							<span className="absolute -inset-2 bg-gradient-to-r from-[#e50049]/20 to-[#ff1464]/20 blur-xl group-hover:blur-2xl transition-all duration-500"></span>
							<span className="relative z-10 inline-block text-white">{girl.title}</span>
						</span>
					</h1>
					<div className="flex flex-wrap justify-center gap-2 mb-6">
						{[girl.price, girl.city, ...girl.type, ...girl.service].map((tag, index) => (
							<span
								key={index}
								className="px-4 py-1.5 text-sm bg-dark-accent/10 text-dark-accent rounded-full border border-dark-accent/20 hover:bg-dark-accent/20 transition-colors duration-300"
							>
								{tag}
							</span>
						))}
					</div>
				</div>

				<div className="max-w-3xl mx-auto space-y-8 mb-16">
					{/* 基本信息卡片 */}
					<div>
						<Card className="bg-dark-background/30 backdrop-blur-xl overflow-hidden rounded-xl">
							<CardContent className="p-6 space-y-6">
								<div className="space-y-4">
									<h2 className="text-2xl font-semibold text-white flex items-center gap-2">
										<Crown className="w-6 h-6 text-dark-accent" />
										<span>基本资料</span>
									</h2>
									<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
										<InfoItem label="专属ID" value={girl.girl_id} icon="id" />
										<InfoItem label="身高" value={girl.height ? `${girl.height}cm` : undefined} icon="height" />
										<InfoItem label="罩杯" value={girl.cup} icon="cup" />
										<InfoItem label="地区" value={girl.city} icon="location" />
									</div>
								</div>

								{girl.description && (
									<div className="space-y-2 pt-4 border-t border-dark-accent/5">
										<h3 className="text-lg font-semibold text-white flex items-center gap-2">
											<Star className="w-5 h-5 text-dark-accent" />
											<span>个人介绍</span>
										</h3>
										<div className="relative">
											<div className="absolute -left-1 -top-2 text-3xl text-dark-accent opacity-20 font-serif">&ldquo;</div>
											<div className="absolute -right-1 -bottom-2 text-3xl text-dark-accent opacity-20 font-serif rotate-180">&rdquo;</div>
											<p className="text-gray-300 text-lg leading-relaxed px-4 py-2">{girl.description}</p>
										</div>
									</div>
								)}
							</CardContent>
						</Card>
					</div>

					{/* 评分卡片 */}
					{hasRatings && (
						<div>
							<Card className="bg-dark-background/30 backdrop-blur-xl overflow-hidden rounded-xl">
								<CardContent className="p-6">
									<div className="space-y-4">
										<h2 className="text-2xl font-semibold text-white flex items-center gap-2">
											<Diamond className="w-6 h-6 text-dark-accent" />
											<span>会员评分</span>
										</h2>
										<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
											{girl.photoAccuracy && (
												<div className="space-y-2">
													<span className="text-sm text-gray-400">照片准确度</span>
													{renderRatingStars(girl.photoAccuracy)}
												</div>
											)}
											{girl.appearance && (
												<div className="space-y-2">
													<span className="text-sm text-gray-400">外貌评分</span>
													{renderRatingStars(girl.appearance)}
												</div>
											)}
											{girl.attitude && (
												<div className="space-y-2">
													<span className="text-sm text-gray-400">态度评分</span>
													{renderRatingStars(girl.attitude)}
												</div>
											)}
											{girl.serviceQuality && (
												<div className="space-y-2">
													<span className="text-sm text-gray-400">服务质量</span>
													{renderRatingStars(girl.serviceQuality)}
												</div>
											)}
										</div>
										{girl.overallRating && (
											<div className="mt-6 pt-4 border-t border-dark-accent/5">
												<div className="flex items-center justify-between">
													<span className="text-sm text-gray-400">整体评分</span>
													{renderRatingStars(girl.overallRating)}
												</div>
											</div>
										)}
										{girl.comments && (
											<div className="mt-4 pt-4 border-t border-dark-accent/5">
												<div className="relative">
													<div className="absolute -left-1 -top-2 text-3xl text-dark-accent opacity-20 font-serif">&ldquo;</div>
													<div className="absolute -right-1 -bottom-2 text-3xl text-dark-accent opacity-20 font-serif rotate-180">&rdquo;</div>
													<p className="text-base md:text-lg text-white leading-relaxed px-4 py-2 font-medium">
														{girl.comments}
													</p>
												</div>
											</div>
										)}
									</div>
								</CardContent>
							</Card>
						</div>
					)}

					{/* 联系方式 */}
					<div className="relative">
						<div className="relative bg-gradient-to-r from-[#e50049]/5 via-background/50 to-[#e50049]/5 backdrop-blur-xl p-6 rounded-xl border border-[#e50049]/10">
							<h3 className="text-lg font-semibold mb-2 text-[#e50049]">联系客服安排约会</h3>
							<p className="text-sm text-muted-foreground mb-4">
								选择以下任意方式联系客服，我们将为您提供专业的安排服务
							</p>
							
							<div className="grid grid-cols-1 md:grid-cols-2 gap-3">
								<div>
									<Button 
										onClick={() => window.open('https://t.me/Jianier1314', '_blank')}
										className="w-full bg-[#0088cc] hover:bg-[#0099dd] text-white transition-all duration-300 text-base py-4 rounded-xl flex items-center justify-center gap-2 shadow-lg shadow-[#0088cc]/20 hover:shadow-xl hover:shadow-[#0088cc]/30"
									>
										<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
											<path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
										</svg>
										Telegram 客服
									</Button>
								</div>

								<div>
									<Button 
										onClick={handleArrange}
										className="w-full bg-[#e50049] hover:bg-[#ff1464] text-white transition-all duration-300 text-base py-4 rounded-xl flex items-center justify-center gap-2 shadow-lg shadow-[#e50049]/20 hover:shadow-xl hover:shadow-[#e50049]/30"
									>
										<svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
											<path strokeLinecap="round" strokeLinejoin="round" d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"/>
										</svg>
										在线客服
									</Button>
								</div>
							</div>
						</div>
					</div>

					{/* 媒体展示 */}
					{girl.media.map((mediaUrl, index) => (
						<div
							key={index}
							className="w-full px-1 md:px-0"
						>
							{mediaUrl.endsWith(".mp4") ? (
								<video
									src={mediaUrl}
									controls
									className="w-full object-contain rounded-lg"
									title={`${girl.title} - 视频 ${index + 1}`}
								/>
							) : (
								<div className="w-full">
									<Image
										src={mediaUrl}
										alt={`${girl.title} - 图片 ${index + 1}`}
										width={1200}
										height={800}
										className="w-full rounded-lg"
									/>
								</div>
							)}
						</div>
					))}
				</div>
			</div>
		</div>
	);
};

const InfoItem: React.FC<{ label: string; value?: string; icon: string }> = ({ label, value, icon }) => {
	const getIcon = () => {
		switch (icon) {
			case 'id':
				return (
					<svg className="w-4 h-4 text-dark-accent/70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
					</svg>
				);
			case 'height':
				return (
					<svg className="w-4 h-4 text-dark-accent/70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 6v12m12-12v12M6 12h12" />
					</svg>
				);
			case 'cup':
				return (
					<svg className="w-4 h-4 text-dark-accent/70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
					</svg>
				);
			case 'location':
				return (
					<svg className="w-4 h-4 text-dark-accent/70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
						<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
					</svg>
				);
			default:
				return null;
		}
	};

	return (
		<div className="space-y-1.5">
			<div className="text-gray-400 text-sm flex items-center gap-1.5">
				{getIcon()}
				{label}
			</div>
			<div className="text-lg font-medium">{value || '-'}</div>
		</div>
	);
};

export default GirlDetail;
