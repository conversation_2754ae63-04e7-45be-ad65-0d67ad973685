"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import ProfileContent from "./ProfileContent";
import ProfileSkeleton from "./ProfileSkeleton";

export default function ProfileClient() {
	const { data: session, status } = useSession();
	const router = useRouter();

	useEffect(() => {
		if (status === "unauthenticated") {
			router.push("/login");
		}
	}, [status, router]);

	if (status === "loading") {
		return <ProfileSkeleton />;
	}

	if (!session) {
		return null;
	}

	return <ProfileContent />;
}
