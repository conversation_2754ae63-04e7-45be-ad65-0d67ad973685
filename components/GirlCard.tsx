"use client";

import React, { useState, useRef, useCallback, useEffect, useMemo, useReducer, memo } from "react";
import Link from "next/link";
import Image from "next/image";
import { Card, CardContent } from "./ui/card";
import { AspectRatio } from "./ui/aspect-ratio";
import ImageModal from "./ImageModal";
import { X, Play, Pause, Volume2, VolumeX } from "lucide-react";
import Intercom, { show } from "@intercom/messenger-js-sdk";
import { Button } from "./ui/button";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { toast } from "react-hot-toast";

// 辅助函数：判断URL是否为视频
const isVideo = (url: string) => {
	return url.includes('.mp4') || url.includes('.mov') || url.includes('.webm');
};

// 从媒体URL中获取文件ID
const getFileIdFromUrl = (url: string) => {
	// 假设URL格式为 "/api/media/fileId" 或 "https://domain.com/path/fileId"
	const segments = url.split('/');
	const fileId = segments[segments.length - 1];
	// 移除可能存在的文件扩展名（如.mp4）
	return fileId.split('.')[0];
};

// 获取视频缩略图URL
const getThumbnailUrl = (mediaUrl: string) => {
	const fileId = getFileIdFromUrl(mediaUrl);
	
	// 解析URL以获取baseURL
	try {
		// 对于绝对URL和相对URL都有效的处理方式
		const url = new URL(mediaUrl.startsWith('http') ? mediaUrl : `http://example.com${mediaUrl}`);
		
		// 获取URL的各个部分
		const protocol = mediaUrl.startsWith('http') ? `${url.protocol}//` : '';
		const host = mediaUrl.startsWith('http') ? url.host : '';
		
		// 检查URL是否包含proxy路径，如果有则直接替换为thumbnail路径
		if (url.pathname.includes('/proxy2/')) {
			// 去掉proxy路径，改为thumbnail路径
			const basePath = `${protocol}${host}`;
			return `${basePath}/thumbnail/${fileId}`;
		}
		
		// 获取路径部分，并将"media"替换为"thumbnail"
		let path = url.pathname;
		const pathParts = path.split('/');
		const mediaIndex = pathParts.findIndex(part => part === 'media');
		
		if (mediaIndex !== -1) {
			pathParts[mediaIndex] = 'thumbnail';
			// 移除最后一部分(文件名)，添加fileId
			pathParts.pop();
			path = `${pathParts.join('/')}/${fileId}`;
		} else {
			// 如果路径中没有"media"，找到最后一个斜杠位置
			const lastSlashIndex = path.lastIndexOf('/');
			if (lastSlashIndex !== -1) {
				// 替换最后部分为"thumbnail/fileId"
				path = `${path.substring(0, lastSlashIndex)}/thumbnail/${fileId}`;
			}
		}
		
		return `${protocol}${host}${path}`;
	} catch (e) {
		// 如果解析失败，尝试简单的字符串替换
		if (mediaUrl.includes('/proxy2/')) {
			const urlParts = mediaUrl.split('/proxy2/');
			return `${urlParts[0]}/thumbnail/${fileId}`;
		}
		return mediaUrl.replace(/\/media\/([^/]+)$/, `/thumbnail/${fileId}`);
	}
};

interface GirlCardProps {
	data: {
		id: string;
		girl_id: string;
		title: string;
		feature: string;
		city: string;
		district?: string;
		height?: string;
		age?: string;
		cup?: string;
		price: string;
		type: string[];
		service: string[];
		description?: string;
		media: string[];
		pusher?: string;
		message?: string;
		createdAt?: string;
		updatedAt?: string;
		photoAccuracy?: number;
		appearance?: number;
		attitude?: number;
		serviceQuality?: number;
		overallRating?: number;
		comments?: string;
	};
	forceShowContent?: boolean;
}

interface ImageComponentProps {
	mediaUrl: string;
	index: number;
	isLoaded: boolean;
	onClick: () => void;
	onRef: (el: HTMLImageElement | null) => void;
}

interface PlayButtonProps {
	onClick: (e: React.MouseEvent) => void;
}

const PlayButton: React.FC<PlayButtonProps> = ({ onClick }) => (
	<div
		className="absolute inset-0 cursor-pointer z-10 flex items-center justify-center transition-opacity duration-300"
		onClick={onClick}
	>
		<Play className="text-white opacity-40 hover:opacity-70 transition-opacity" size={48} />
	</div>
);

interface State {
	selectedImage: string | null;
	expandedVideoUrl: string | null;
	expandedVideoIndex: number | null;
	loadedMedia: { [key: string]: boolean };
	copySuccess: boolean;
	videoReady: { [key: number]: boolean };
	isAudioEnabled: boolean;
	imagesLoaded: boolean;
	thumbnailsLoaded: { [key: number]: boolean };
	videosLoading: { [key: number]: boolean };
}

interface Action {
	type: string;
	payload: any;
}

const reducer = (state: State, action: Action) => {
	switch (action.type) {
		case 'SET_MEDIA_LOADED':
			return {
				...state,
				loadedMedia: {
					...state.loadedMedia,
					[action.payload.mediaUrl]: true,
				},
			};
		case 'SET_IMAGES_LOADED':
			return {
				...state,
				imagesLoaded: action.payload.imagesLoaded,
			};
		case 'SET_COPY_SUCCESS':
			return {
				...state,
				copySuccess: action.payload.copySuccess,
			};
		case 'SET_AUDIO_ENABLED':
			return {
				...state,
				isAudioEnabled: action.payload.isAudioEnabled,
			};
		case 'SET_EXPANDED_VIDEO':
			return {
				...state,
				expandedVideoUrl: action.payload.expandedVideoUrl,
				expandedVideoIndex: action.payload.expandedVideoIndex,
			};
		case 'SET_SELECTED_IMAGE':
			return {
				...state,
				selectedImage: action.payload.selectedImage,
			};
		case 'SET_VIDEO_READY':
			return {
				...state,
				videoReady: {
					...state.videoReady,
					[action.payload.index]: true,
				},
			};
		case 'SET_THUMBNAIL_LOADED':
			return {
				...state,
				thumbnailsLoaded: {
					...state.thumbnailsLoaded,
					[action.payload.index]: true,
				},
			};
		case 'SET_VIDEO_LOADING':
			return {
				...state,
				videosLoading: {
					...state.videosLoading,
					[action.payload.index]: action.payload.isLoading,
				},
			};
		case 'TOGGLE_AUDIO':
			return {
				...state,
				isAudioEnabled: !state.isAudioEnabled,
			};
		default:
			return state;
	}
};

const ImageComponent = memo(({ mediaUrl, index, isLoaded, onClick, onRef }: ImageComponentProps) => {
	const { data: session } = useSession();
	const isVIP = session?.user?.isVIP;
	return (
		<div className="relative w-full h-full group">
			<Image
				ref={onRef}
				src={mediaUrl}
				alt={`media-${index}`}
				width={400}
				height={400}
				className={`cursor-pointer transition-all duration-300 object-cover w-full h-full ${
					isLoaded ? 'opacity-100' : 'opacity-0'
				} ${!isVIP ? 'blur-[8px]' : ''}`}
				onClick={onClick}
				priority={index < 4}
				unoptimized={mediaUrl.startsWith('http://') || mediaUrl.includes('/api/')}
			/>
		</div>
	);
});

ImageComponent.displayName = 'ImageComponent';

const GirlCard: React.FC<GirlCardProps> = React.memo(({ data, forceShowContent = false }) => {
	const [state, dispatch] = useReducer(reducer, {
		selectedImage: null,
		expandedVideoUrl: null,
		expandedVideoIndex: null,
		loadedMedia: {},
		copySuccess: false,
		videoReady: {},
		isAudioEnabled: false,
		imagesLoaded: false,
	
		thumbnailsLoaded: {},
		videosLoading: {},
	});

	// 视频播放状态控制
	const [videoPlayState, setVideoPlayState] = useState({
		hasPlayAttempt: false,
		playError: false,
	});

	// 视频元素引用
	const expandedVideoRef = useRef<HTMLVideoElement | null>(null);

	const refs = useRef({
		videos: {} as { [key: number]: HTMLVideoElement },
		images: {} as { [key: string]: HTMLImageElement },
		videoContainer: null as HTMLDivElement | null,
	});

	// 直接预加载所有媒体
	const preloadMedia = useCallback((mediaUrl: string) => {
		if (state.loadedMedia[mediaUrl] || isVideo(mediaUrl)) return;

		const img = document.createElement('img');
		const onLoad = () => {
			dispatch({ type: 'SET_MEDIA_LOADED', payload: { mediaUrl } });
			img.removeEventListener('load', onLoad);
		};
		img.addEventListener('load', onLoad);
		img.src = mediaUrl;
	}, [state.loadedMedia, dispatch]);

	useEffect(() => {
		// 立即加载所有媒体，不进行懒加载
		data.media.forEach((mediaUrl) => {
			preloadMedia(mediaUrl);
		});
	}, [data.media, preloadMedia]);

	const filteredTags = useMemo(() => {
		return [`#${data.city}`, `#${data.district}`, `#${data.price}`, ...data.type.map((t) => `#${t}`), "#上新"].filter((tag) => tag !== "#" && tag !== "##上新");
	}, [data.city, data.district, data.price, data.type]);

	const getPersonalInfo = useCallback(() => {
		const info = [];
		if (data.age) info.push(`${data.age}岁`);
		if (data.height) info.push(data.height);
		if (data.cup) info.push(data.cup);
		return info.join(" | ");
	}, [data.age, data.height, data.cup]);

	const personalInfo = useMemo(() => getPersonalInfo(), [getPersonalInfo]);

	const handleCopyId = useCallback(() => {
		if (typeof navigator !== "undefined" && navigator.clipboard) {
			navigator.clipboard
				.writeText(`#${data.girl_id}`)
				.then(() => {
					dispatch({ type: 'SET_COPY_SUCCESS', payload: { copySuccess: true } });
					setTimeout(() => dispatch({ type: 'SET_COPY_SUCCESS', payload: { copySuccess: false } }), 2000);
				})
				.catch((err) => {
					console.error("Failed to copy: ", err);
				});
		} else {
			const textArea = document.createElement("textarea");
			textArea.value = `#${data.girl_id}`;
			document.body.appendChild(textArea);
			textArea.select();
			try {
				document.execCommand("copy");
				dispatch({ type: 'SET_COPY_SUCCESS', payload: { copySuccess: true } });
				setTimeout(() => dispatch({ type: 'SET_COPY_SUCCESS', payload: { copySuccess: false } }), 2000);
			} catch (err) {
				console.error("Failed to copy: ", err);
			}
			document.body.removeChild(textArea);
		}
	}, [data.girl_id]);

	// 缩略图加载处理
	const handleThumbnailLoad = useCallback((index: number) => {
		dispatch({ type: 'SET_THUMBNAIL_LOADED', payload: { index } });
	}, [dispatch]);

	// 开始加载视频
	const startVideoLoading = useCallback((index: number) => {
		dispatch({ type: 'SET_VIDEO_LOADING', payload: { index, isLoading: true } });
	}, [dispatch]);

	const handleImageClick = useCallback(
		(mediaUrl: string) => {
			if (!isVideo(mediaUrl)) {
				dispatch({ type: 'SET_SELECTED_IMAGE', payload: { selectedImage: mediaUrl } });
			}
		},
		[isVideo]
	);

	const handleVideoExpand = useCallback((mediaUrl: string, index: number) => {
		// 开始加载视频
		startVideoLoading(index);
		
		dispatch({ 
			type: 'SET_EXPANDED_VIDEO', 
			payload: { 
				expandedVideoUrl: mediaUrl,
				expandedVideoIndex: index
			} 
		});
	}, [startVideoLoading]);

	const handleVideoCollapse = useCallback(() => {
		dispatch({ 
			type: 'SET_EXPANDED_VIDEO', 
			payload: { 
				expandedVideoUrl: null,
				expandedVideoIndex: null
			} 
		});
		
		// 重置视频播放状态
		setVideoPlayState({
			hasPlayAttempt: false,
			playError: false,
		});
	}, []);

	// 视频节点设置
	const setVideoRef = useCallback((element: HTMLVideoElement | null, index: number) => {
		if (!element) return;
		
		refs.current.videos[index] = element;
		
		// 只有当用户点击播放时才开始加载视频
		if (state.videosLoading[index]) {
			// 设置视频只读取元数据以获取第一帧作为预览
			element.addEventListener('loadedmetadata', function() {
				try {
					// 尝试获取视频第一帧
					element.currentTime = 0.1;
				} catch (e) {
					console.error('无法设置视频当前时间:', e);
				}
			});
			
			// 当时间更新后，标记视频已准备好显示第一帧
			element.addEventListener('timeupdate', function onTimeUpdate() {
				if (element.currentTime > 0) {
					// 暂停视频，防止继续播放
					element.pause();
					dispatch({ type: 'SET_VIDEO_READY', payload: { index } });
					dispatch({ 
						type: 'SET_MEDIA_LOADED', 
						payload: { mediaUrl: element.src } 
					});
					element.removeEventListener('timeupdate', onTimeUpdate);
				}
			});
		}
	}, [state.videosLoading, dispatch]);

	const router = useRouter();
	const { data: session, status } = useSession();
	const isVIP = session?.user?.isVIP;

	const handleArrange = useCallback(() => {
		if (!session) {
			toast.error("请登录后联系在线客服");
			router.push("/login");
		} else {
			show();
		}
	}, [session, router]);

	const renderMedia = useCallback((mediaUrl: string, index: number) => {
		const isMediaLoaded = state.loadedMedia[mediaUrl];
		const isVideoReady = state.videoReady[index];
		const isVideoMedia = isVideo(mediaUrl);
		const isThumbnailLoaded = state.thumbnailsLoaded[index];
		const isVideoLoading = state.videosLoading[index];
		const shouldBlur = !isVIP && !forceShowContent;
		const canInteract = isVIP || forceShowContent;

		return (
			<AspectRatio key={`${mediaUrl}-${index}`} ratio={1} className="overflow-hidden group relative">
				{
					isVideoMedia ? (
						<div className={`relative w-full h-full bg-black/20 ${isVIP ? 'group-hover:scale-110' : ''} transition-transform duration-300`}>
							{/* 视频缩略图 */}
							{!isVideoLoading && (
								<div className="absolute inset-0 flex items-center justify-center overflow-hidden">
									<Image 
										src={getThumbnailUrl(mediaUrl)} 
										alt={`video-thumbnail-${index}`}
										width={400}
										height={400}
										className={`min-w-full min-h-full object-cover transition-opacity duration-300 ${
											isThumbnailLoaded ? 'opacity-100' : 'opacity-0'
										} ${shouldBlur ? 'blur-[8px]' : ''}`}
										onLoad={() => handleThumbnailLoad(index)}
										onClick={(e) => {
											e.preventDefault();
											e.stopPropagation();
											if (canInteract) {
												startVideoLoading(index);
												handleVideoExpand(mediaUrl, index);
											}
										}}
										unoptimized={getThumbnailUrl(mediaUrl).startsWith('http://') || getThumbnailUrl(mediaUrl).includes('/api/')}
									/>
								</div>
							)}
							
							{/* 只有当用户点击了视频，才加载实际视频 */}
							{isVideoLoading && (
								<div className="absolute inset-0 flex items-center justify-center overflow-hidden">
									<video 
										ref={(el) => setVideoRef(el, index)}
										src={mediaUrl} 
										className={`min-w-full min-h-full object-cover transition-opacity duration-300 ${
											isVideoReady ? 'opacity-100' : 'opacity-0'
										} ${shouldBlur ? 'blur-[8px]' : ''}`}
										muted 
										autoPlay
										preload="metadata"
										playsInline
										data-webkit-playsinline="true"
										data-x-webkit-airplay="allow"
										data-x5-video-player-type="h5-page"
										data-x5-video-player-fullscreen="false"
										data-x5-video-orientation="portraint"
										controlsList="nodownload nofullscreen"
										disablePictureInPicture
										onClick={(e) => {
											e.preventDefault();
											e.stopPropagation();
											if (canInteract) {
												handleVideoExpand(mediaUrl, index);
											}
										}}
									/>
								</div>
							)}
							
							<div className="absolute inset-0 flex items-center justify-center pointer-events-none">
								<Play className="text-white opacity-80" size={32} />
							</div>
						</div>
					) : (
						<div className={`relative w-full h-full transition-transform duration-300 ${isVIP ? 'group-hover:scale-110' : ''}`}>
							<div className="absolute inset-0 flex items-center justify-center overflow-hidden">
								<Image
									ref={(el) => {
										if (el) {
											refs.current.images[mediaUrl] = el;
										}
									}}
									src={mediaUrl}
									alt={`media-${index}`}
									width={400}
									height={400}
									className={`min-w-full min-h-full object-cover transition-opacity duration-300 ${
										isMediaLoaded ? 'opacity-100' : 'opacity-0'
									} ${shouldBlur ? 'blur-[8px]' : ''}`}
									onClick={() => {
										if (canInteract) {
											handleImageClick(mediaUrl);
										}
									}}
									priority={index < 4}
									unoptimized={mediaUrl.startsWith('http://') || mediaUrl.includes('/api/')}
								/>
							</div>
						</div>
					)
				}
			</AspectRatio>
		);
	}, [state, handleVideoExpand, handleImageClick, isVIP, handleThumbnailLoad, startVideoLoading, setVideoRef, forceShowContent]);

	const renderRatingStars = useCallback((rating: number | undefined) => {
		if (!rating) return null;
		
		const starRating = (rating / 2);
		const fullStars = Math.floor(starRating);
		const hasHalfStar = starRating % 1 >= 0.5;
		
		return (
			<div className="flex items-center">
				{[...Array(5)].map((_, index) => {
					const isFull = index < fullStars;
					const isHalf = !isFull && index === fullStars && hasHalfStar;
					
					return (
						<div key={index} className="relative">
							<svg
								className={`w-4 h-4 transition-all duration-300 ${
									isFull || isHalf
										? 'text-yellow-400 drop-shadow-glow scale-110'
										: 'text-gray-600'
								}`}
								fill={isFull ? "currentColor" : "none"}
								stroke="currentColor"
								strokeWidth={isFull ? "0" : "1.5"}
								viewBox="0 0 20 20"
							>
								<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
							</svg>
							{isHalf && (
								<div className="absolute inset-0 overflow-hidden w-[50%]">
									<svg
										className="w-4 h-4 text-yellow-400 drop-shadow-glow scale-110"
										fill="currentColor"
										viewBox="0 0 20 20"
									>
										<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
									</svg>
								</div>
							)}
						</div>
					);
				})}
				<span className="ml-2 text-sm font-medium bg-gradient-to-r from-yellow-400/90 to-yellow-500/90 bg-clip-text text-transparent">
					{rating.toFixed(1)}
				</span>
			</div>
		);
	}, []);

	// 尝试播放视频
	const attemptAutoPlay = useCallback(() => {
		const videoElement = expandedVideoRef.current;
		if (!videoElement) return;

		setVideoPlayState(prev => ({ ...prev, hasPlayAttempt: true }));

		// 使用 Promise API 检测是否可以播放
		const playPromise = videoElement.play();
		
		if (playPromise !== undefined) {
			playPromise
				.then(() => {
					// 自动播放成功
					setVideoPlayState(prev => ({ ...prev, playError: false }));
				})
				.catch(error => {
					// 自动播放失败 - 通常是移动浏览器策略导致的
					console.warn('视频自动播放失败:', error);
					setVideoPlayState(prev => ({ ...prev, playError: true }));
					// 将视频设置为初始状态，准备用户手动交互
					videoElement.currentTime = 0;
					videoElement.pause();
				});
		}
	}, []);

	// 处理视频元素引用
	const setExpandedVideoRef = useCallback((node: HTMLVideoElement | null) => {
		expandedVideoRef.current = node;
		
		if (node) {
			// 只有在视频元素刚刚挂载时才尝试自动播放
			// 这避免了重复尝试播放
			if (!videoPlayState.hasPlayAttempt) {
				// 短暂延迟确保视频元素已完全加载
				setTimeout(attemptAutoPlay, 100);
			}
		}
	}, [videoPlayState.hasPlayAttempt, attemptAutoPlay]);

	// 手动播放视频的函数
	const handleManualPlay = useCallback(() => {
		const videoElement = expandedVideoRef.current;
		if (!videoElement) return;

		if (videoElement.paused) {
			attemptAutoPlay();
		} else {
			videoElement.pause();
		}
	}, [attemptAutoPlay]);

	// 添加Escape键关闭视频的功能
	useEffect(() => {
		const handleKeyDown = (event: KeyboardEvent) => {
			if (event.key === "Escape") {
				handleVideoCollapse();
			}
		};

		if (state.expandedVideoUrl) {
			document.addEventListener("keydown", handleKeyDown);
		}

		return () => {
			document.removeEventListener("keydown", handleKeyDown);
		};
	}, [state.expandedVideoUrl, handleVideoCollapse]);

	return (
		<>
			<Card className="overflow-hidden shadow-lg bg-[#1a1a1a] relative">
				<CardContent className="p-0 relative">
					<div className="relative z-0">
						<div className="absolute inset-0 w-full h-full">
							<div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,rgba(229,0,73,0.08),rgba(229,0,73,0)_50%)]"></div>
							<div className="absolute inset-0 bg-[radial-gradient(circle_at_bottom_left,rgba(229,0,73,0.05),rgba(229,0,73,0)_50%)]"></div>
						</div>

						<div className="grid grid-cols-2 gap-0 mb-2">
							{data.media.map((mediaUrl, index) => (
								<div 
									key={`${mediaUrl}-${index}`} 
									className={`group/media relative overflow-hidden cursor-pointer ${(!isVIP && !forceShowContent) ? 'pointer-events-none' : ''}`}
									onClick={() => {
										if (isVIP || forceShowContent) {
											isVideo(mediaUrl) ? handleVideoExpand(mediaUrl, index) : handleImageClick(mediaUrl);
										}
									}}
								>
									{renderMedia(mediaUrl, index)}
									<div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 group-hover/media:opacity-100 transition-opacity duration-300"></div>
								</div>
							))}
						</div>

						<div className="px-6 pt-4 pb-6">
							<div className="mb-4 flex flex-wrap gap-1.5">
								{filteredTags.map((tag, index) => (
									<div
										key={`tag-${index}`}
										className="group/tag relative"
										style={{ animationDelay: `${index * 100}ms` }}
									>
										<div className="absolute inset-0 bg-gradient-to-r from-dark-accent/10 to-dark-accent/10 rounded-full blur-lg opacity-0 group-hover/tag:opacity-100 transition-all duration-300"></div>
										<span className={`relative inline-flex items-center gap-1.5 px-2.5 py-1 text-xs font-medium bg-gradient-to-r from-dark-accent/[0.08] to-dark-accent/[0.06] text-dark-accent/70 rounded-full transition-all duration-300 group-hover/tag:scale-105 group-hover/tag:from-dark-accent/[0.12] group-hover/tag:to-dark-accent/[0.10] group-hover/tag:text-dark-accent/90 ${!isVIP && !forceShowContent ? 'blur-[8px]' : ''}`}>
											<span className="w-1 h-1 bg-dark-accent/50 rounded-full"></span>
											{tag}
										</span>
									</div>
								))}
								{data.service && data.service.map((service, index) => (
									<div
										key={`service-${index}`}
										className="group/service relative"
										style={{ animationDelay: `${(index + filteredTags.length) * 100}ms` }}
									>
										<div className="absolute inset-0 bg-gradient-to-r from-dark-accent/10 to-dark-accent/10 rounded-full blur-lg opacity-0 group-hover/service:opacity-100 transition-all duration-300"></div>
										<span className={`relative inline-flex items-center gap-1.5 px-2.5 py-1 text-xs font-medium bg-gradient-to-r from-dark-accent/[0.08] to-dark-accent/[0.06] text-dark-accent/70 rounded-full transition-all duration-300 group-hover/service:scale-105 group-hover/service:from-dark-accent/[0.12] group-hover/service:to-dark-accent/[0.10] group-hover/service:text-dark-accent/90 ${!isVIP && !forceShowContent ? 'blur-[8px]' : ''}`}>
											<span className="w-1 h-1 bg-dark-accent/50 rounded-full"></span>
											#{service}
										</span>
									</div>
								))}
							</div>

							<div className={`${!isVIP && !forceShowContent ? 'pointer-events-none' : ''}`}>
								<Link 
									href={`/girl/${data.girl_id}`}
									className={`relative block text-2xl md:text-xl lg:text-2xl font-bold mb-3 hover:text-dark-accent transition-colors duration-300 cursor-pointer group/title ${!isVIP && !forceShowContent ? 'blur-[8px]' : ''}`}
									shallow={true}
								>
									<span className="relative inline-block">
										{data.title}
										<span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-dark-accent to-dark-accent group-hover/title:w-full transition-all duration-700"></span>
									</span>
									<svg 
										className="inline-block ml-2 w-5 h-5 text-dark-accent/40 group-hover/title:text-dark-accent/70 transition-all duration-300 group-hover/title:translate-x-1" 
										fill="none" 
										stroke="currentColor" 
										viewBox="0 0 24 24"
									>
										<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
									</svg>
								</Link>
							</div>

							<div className={`flex items-center justify-between gap-2 mb-4`}>
								<div className={`flex items-center gap-3 text-sm text-gray-400 ${!isVIP && !forceShowContent ? 'blur-[8px]' : ''}`}>
									{data.age && (
										<span className="inline-flex items-center gap-1.5">
											<svg className="w-4 h-4 text-dark-accent/70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
											</svg>
											{data.age}岁
										</span>
									)}
									{data.height && (
										<span className="inline-flex items-center gap-1.5">
											<svg className="w-4 h-4 text-dark-accent/70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 6v12m12-12v12M6 12h12" />
											</svg>
											{data.height}
										</span>
									)}
									{data.cup && (
										<span className="inline-flex items-center gap-1.5">
											<svg className="w-4 h-4 text-dark-accent/70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
											</svg>
											{data.cup}
										</span>
									)}
								</div>
								<div className="flex items-center gap-2 z-10">
									<div 
										className={`flex items-center gap-1.5 cursor-pointer hover:bg-dark-accent/5 rounded-md px-2 py-1 transition-colors duration-300 ${!isVIP && !forceShowContent ? 'blur-[8px]' : ''}`}
										onClick={(e) => {
											e.preventDefault();
											e.stopPropagation();
											if (isVIP || forceShowContent) {
												handleCopyId();
											}
										}}
										title="点击复制ID"
									>
										<span className="text-sm text-dark-accent/70">专属ID:</span>
										<span className="text-sm font-medium text-dark-accent">#{data.girl_id}</span>
									</div>
									<button
										type="button"
										className={`inline-flex items-center justify-center w-6 h-6 rounded-md bg-dark-accent/10 hover:bg-dark-accent/20 transition-colors duration-300 cursor-pointer ${!isVIP && !forceShowContent ? 'blur-[8px]' : ''}`}
										title="点击复制ID"
										onClick={(e) => {
											e.preventDefault();
											e.stopPropagation();
											if (isVIP || forceShowContent) {
												handleCopyId();
											}
										}}
									>
										{state.copySuccess ? (
											<svg className="w-3.5 h-3.5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
											</svg>
										) : (
											<svg className="w-3.5 h-3.5 text-dark-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
											</svg>
										)}
									</button>
								</div>
							</div>

							{data.description && (
								<div className={`relative mb-6 ${!isVIP && !forceShowContent ? 'blur-[8px]' : ''}`}>
									<div className="absolute -left-2 top-0 text-4xl text-dark-accent/20 font-serif">&ldquo;</div>
									<p className="text-gray-400 pl-4 relative z-10">{data.description}</p>
									<div className="absolute -right-2 bottom-0 text-4xl text-dark-accent/20 font-serif rotate-180">&rdquo;</div>
								</div>
							)}

							{(data.photoAccuracy || data.appearance || data.attitude || data.serviceQuality || data.overallRating) && (
								<div className="mt-3 mb-4 p-4 bg-gradient-to-br from-dark-secondary/60 via-dark-secondary/40 to-dark-secondary/30 backdrop-blur-lg rounded-2xl border border-dark-accent/15 shadow-lg relative overflow-hidden pointer-events-none">
									<div className="absolute inset-0 bg-gradient-to-br from-yellow-400/5 to-transparent pointer-events-none"></div>
									<h3 className="text-lg font-semibold mb-3 text-dark-accent flex items-center gap-2">
										<span className="text-yellow-400 animate-pulse">💬</span>
										<span className="bg-gradient-to-r from-dark-accent to-yellow-400 bg-clip-text text-transparent">会员反馈</span>
									</h3>
									{data.comments && (
										<div className="mt-3 pt-3 border-t border-dark-accent/10">
											<div className="relative">
												<div className="absolute -left-1 -top-2 text-3xl text-dark-accent opacity-30 font-serif">&ldquo;</div>
												<div className="absolute -right-1 -bottom-2 text-3xl text-dark-accent opacity-30 font-serif rotate-180">&rdquo;</div>
												<p className="text-sm text-white leading-relaxed px-4 py-1.5 font-bold">
													{data.comments}
												</p>
											</div>
											<div className="w-full h-[1px] bg-dark-accent/10 mt-3" />
										</div>
									)}
									<div className="grid grid-cols-2 gap-3 mt-4">
										{data.photoAccuracy && (
											<div className="flex flex-col p-1.5 rounded-lg hover:bg-dark-secondary/30 transition-all duration-300">
												<span className="text-sm text-gray-400 mb-1 font-medium">照片准确度</span>
												{renderRatingStars(data.photoAccuracy)}
											</div>
										)}
										{data.appearance && (
											<div className="flex flex-col p-1.5 rounded-lg hover:bg-dark-secondary/30 transition-all duration-300">
												<span className="text-sm text-gray-400 mb-1 font-medium">外貌评分</span>
												{renderRatingStars(data.appearance)}
											</div>
										)}
										{data.attitude && (
											<div className="flex flex-col p-1.5 rounded-lg hover:bg-dark-secondary/30 transition-all duration-300">
												<span className="text-sm text-gray-400 mb-1 font-medium">态度评分</span>
												{renderRatingStars(data.attitude)}
											</div>
										)}
										{data.serviceQuality && (
											<div className="flex flex-col p-1.5 rounded-lg hover:bg-dark-secondary/30 transition-all duration-300">
												<span className="text-sm text-gray-400 mb-1 font-medium">服务质量</span>
												{renderRatingStars(data.serviceQuality)}
											</div>
										)}
									</div>
									{data.overallRating && (
										<div className="mt-3 pt-3 border-t border-dark-accent/10">
											<div className="flex items-center gap-32 p-1.5 rounded-lg hover:bg-dark-secondary/30 transition-all duration-300">
												<span className="text-sm text-gray-400 font-medium">整体评分</span>
												<div className="flex items-center">
													{renderRatingStars(data.overallRating)}
												</div>
											</div>
										</div>
									)}
								</div>
							)}

							<div className="flex mt-6 gap-4 relative z-[25]">
								<Button 
									className="flex-1 bg-dark-accent hover:bg-dark-accent-hover text-white shadow-lg shadow-dark-accent/20 hover:shadow-xl hover:shadow-dark-accent/30 transition-all duration-300 hover:scale-105"
									onClick={(e) => {
										e.preventDefault();
										e.stopPropagation();
										window.open('https://t.me/jianier1314', '_blank');
									}}
								>
									<span className="flex items-center gap-2">
										<svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
											<path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.041-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
										</svg>
										电报客服
									</span>
								</Button>
								<Button 
									onClick={(e) => {
										e.preventDefault();
										e.stopPropagation();
										handleArrange();
									}}
									className="flex-1 bg-white/10 hover:bg-white/20 text-dark-accent border border-dark-accent hover:border-dark-accent-hover shadow-lg shadow-dark-accent/10 hover:shadow-xl hover:shadow-dark-accent/20 transition-all duration-300 hover:scale-105"
								>
									<span className="flex items-center gap-2">
										<svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
										</svg>
										在线客服
									</span>
								</Button>
							</div>
						</div>
					</div>

					{(!isVIP && !forceShowContent && status !== 'loading') && (
						<div className="absolute inset-0 z-[20]">
							<div className="absolute inset-0 bg-gradient-to-t backdrop-blur-[1px] pointer-events-none" />
							<div className="absolute inset-0 flex items-center justify-center">
								<div className="px-8 py-6 backdrop-blur-[5px] rounded-3xl transform hover:scale-105 transition-all duration-300">
									<div className="flex items-center gap-3 mb-4">
										<div className="w-12 h-12 bg-dark-accent rounded-full flex items-center justify-center">
											<svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
											</svg>
										</div>
										<div className="text-dark-accent font-bold text-2xl">会员专享内容</div>
									</div>
									<div className="text-white/90 font-medium text-lg mb-6">
										{session ? '开通VIP，解锁全部极品资源' : '注册后解锁全网最强资源库'}
									</div>
									<Button 
										onClick={() => router.push(session ? '/vip' : '/register')}
										className="w-full bg-dark-accent hover:bg-dark-accent-hover text-white font-bold py-3 rounded-xl shadow-lg shadow-dark-accent/30 hover:shadow-xl hover:shadow-dark-accent/40 transition-all duration-300 hover:scale-105"
									>
										{session ? '立即开通VIP会员' : '立即注册登录'}
									</Button>
								</div>
							</div>
						</div>
					)}
				</CardContent>
			</Card>
			<ImageModal
				isOpen={!!state.selectedImage}
				onClose={() => dispatch({ type: 'SET_SELECTED_IMAGE', payload: { selectedImage: null } })}
				imageUrl={state.selectedImage || ""}
				imageRef={state.selectedImage ? refs.current.images[state.selectedImage] : null}
			/>
			{state.expandedVideoUrl && (
				<div 
					className="fixed inset-0 z-[10000] flex items-center justify-center bg-black bg-opacity-90 backdrop-blur-sm" 
					onClick={handleVideoCollapse}
				>
					{/* 使用固定比例的容器，根据设备调整尺寸 */}
					<div className="relative w-[95vw] sm:w-[85vw] h-[85vh] flex items-center justify-center">
						{/* 填充整个容器的视频/图片区域 */}
						<div className="w-full h-full flex items-center justify-center">
							{state.expandedVideoIndex !== null && (
								<div className="absolute inset-0 w-full h-full flex justify-center">
									<Image 
										src={getThumbnailUrl(state.expandedVideoUrl)}
										alt="视频预览"
										width={800}
										height={600}
										className={`max-h-full w-full sm:w-auto max-w-full object-contain sm:object-cover transition-opacity duration-500 ${
											state.videoReady[state.expandedVideoIndex] ? 'opacity-0' : 'opacity-100'
										}`}
										unoptimized={getThumbnailUrl(state.expandedVideoUrl).startsWith('http://') || getThumbnailUrl(state.expandedVideoUrl).includes('/api/')}
									/>
								</div>
							)}
							
							<div className="relative w-full h-full flex items-center justify-center">
								<video 
									ref={setExpandedVideoRef}
									src={state.expandedVideoUrl} 
									className={`max-h-full w-full sm:w-auto max-w-full object-contain sm:object-cover transition-opacity duration-500 ${
										state.expandedVideoIndex !== null && state.videoReady[state.expandedVideoIndex] ? 'opacity-100' : 'opacity-0'
									}`}
									playsInline
									autoPlay
									muted={false}
									preload="metadata"
									data-webkit-playsinline="true"
									data-x-webkit-airplay="allow"
									data-x5-video-player-type="h5-page"
									data-x5-video-player-fullscreen="false"
									data-x5-video-orientation="portraint"
									controlsList="nodownload nofullscreen"
									disablePictureInPicture
									onLoadStart={() => {
										const index = state.expandedVideoIndex;
										if (index !== null) {
											startVideoLoading(index);
										}
									}}
									onLoadedData={() => {
										const index = state.expandedVideoIndex;
										if (index !== null) {
											dispatch({ type: 'SET_VIDEO_READY', payload: { index } });
										}
									}}
								/>
							</div>
						</div>
						
						<button
							className="absolute top-4 right-4 text-white bg-black/60 hover:bg-black/80 rounded-full p-2 z-[10001] transition-colors"
							onClick={(e) => {
								e.stopPropagation();
								handleVideoCollapse();
							}}
						>
							<X size={24} />
						</button>
					</div>
				</div>
			)}
		</>
	);
});

GirlCard.displayName = "GirlCard";

export default GirlCard;
