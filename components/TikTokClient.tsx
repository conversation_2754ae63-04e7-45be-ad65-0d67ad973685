"use client";

import React, { useState, useEffect, useRef, useCallback, useMemo } from "react";
import { getGirls, getCities, getCityCounts, transformMediaUrls } from "../lib/api";
import { Girl } from "../types/girl";
import { firstTierCities } from "../lib/cityNameMapping";
import { useSession } from "next-auth/react";
import VideoCard from "./VideoCard";
import { ChevronLeft } from "lucide-react";
import { useRouter } from "next/navigation";

export default function TikTokClient() {
  const [videos, setVideos] = useState<{ girl: Girl; videoUrl: string }[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [cities, setCities] = useState<string[]>([]);
  const [selectedCity, setSelectedCity] = useState<string>("");
  const [isPremiumCamp, setIsPremiumCamp] = useState(false); // 是否选择了"极品营"
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  // 检测是否为PC端（屏幕宽度大于768px）
  const [isDesktop, setIsDesktop] = useState(false);
  // 预加载状态管理
  const [preloadedVideos, setPreloadedVideos] = useState<Set<string>>(new Set());

  const { data: session } = useSession();
  const router = useRouter();
  const containerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadingRef = useRef<HTMLDivElement>(null);

  // 获取视频数据
  const getVideos = useCallback(async (pageNum: number, cityFilter: string = "", isPremium: boolean = false) => {
    try {
      setIsLoadingMore(true);
      const filters: {
        city: string;
        district: string;
        price?: string;
      } = {
        city: cityFilter,
        district: "",
      };

      // 如果选择了"极品营"，只显示5K和W价格等级的女孩视频
      if (isPremium) {
        filters.price = "5K|W";
      }

      const response = await getGirls(pageNum, 10, filters);

      // 过滤出包含视频的女孩数据
      const girlsWithVideos = response.girls.flatMap(girl => {
        // 找出所有视频媒体
        const videoUrls = girl.media.filter(url =>
          url.includes('.mp4') || url.includes('.mov') || url.includes('.webm')
        );

        // 处理视频URL
        const processedVideoUrls = transformMediaUrls(videoUrls);

        // 为每个视频创建一个条目，包含女孩信息和视频URL
        return processedVideoUrls.map(videoUrl => ({
          girl,
          videoUrl
        }));
      });

      if (pageNum === 1) {
        setVideos(girlsWithVideos);
      } else {
        setVideos(prev => [...prev, ...girlsWithVideos]);
      }

      setHasMore(girlsWithVideos.length > 0 && pageNum < response.totalPages);
      setPage(pageNum);
    } catch (error) {
      console.error("Error fetching videos:", error);
    } finally {
      setLoading(false);
      setIsLoadingMore(false);
    }
  }, []);

  // 获取城市列表
  const fetchCities = useCallback(async () => {
    try {
      // 同时获取城市列表和城市数量
      const [citiesData, cityCountsData] = await Promise.all([
        getCities(),
        getCityCounts()
      ]);

      // 过滤掉"未知"城市、空城市和数量少于2的城市
      const filteredCities = citiesData.filter(city => {
        const count = cityCountsData[city] || 0;
        return city !== "未知" && city !== "" && count >= 2;
      });

      // 分离一线城市和其他城市
      const firstTierInFiltered = firstTierCities.filter(city => filteredCities.includes(city));
      const otherCities = filteredCities.filter(city => !firstTierCities.includes(city));

      // 其他城市按女孩数量降序排列
      const sortedOtherCities = otherCities.sort((a, b) => {
        const countA = cityCountsData[a] || 0;
        const countB = cityCountsData[b] || 0;
        return countB - countA;
      });

      // 最终排序：一线城市（固定顺序）+ 其他城市（按数量降序）
      const sortedCities = [...firstTierInFiltered, ...sortedOtherCities];

      console.log("【DEBUG-城市排序】一线城市:", firstTierInFiltered);
      console.log("【DEBUG-城市排序】其他城市（按数量排序）:", sortedOtherCities.map(city => `${city}(${cityCountsData[city]})`));
      console.log("【DEBUG-城市排序】最终城市列表:", sortedCities);

      setCities(sortedCities);
    } catch (error) {
      console.error("Error fetching cities:", error);
    }
  }, []);

  // PC端检测
  useEffect(() => {
    const checkIsDesktop = () => {
      setIsDesktop(window.innerWidth > 768);
    };

    checkIsDesktop();
    window.addEventListener('resize', checkIsDesktop);

    return () => {
      window.removeEventListener('resize', checkIsDesktop);
    };
  }, []);

  // 初始加载
  useEffect(() => {
    fetchCities();
    getVideos(1);
  }, [fetchCities, getVideos]);

  // 无限滚动
  useEffect(() => {
    if (!loadingRef.current || isLoadingMore || !hasMore) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !isLoadingMore && hasMore) {
          getVideos(page + 1, selectedCity, isPremiumCamp);
        }
      },
      { threshold: 0.1 }
    );

    observer.observe(loadingRef.current);
    observerRef.current = observer;

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [getVideos, hasMore, isLoadingMore, page, selectedCity, isPremiumCamp]);

  // 城市筛选
  const handleCityChange = (city: string) => {
    setSelectedCity(city);
    setIsPremiumCamp(false); // 选择城市时，取消"极品营"选择
    setPage(1);
    setLoading(true);
    getVideos(1, city, false);
  };

  // 极品营筛选
  const handlePremiumCampClick = () => {
    setSelectedCity(""); // 选择"极品营"时，清除城市选择
    setIsPremiumCamp(true);
    setPage(1);
    setLoading(true);
    getVideos(1, "", true);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen bg-black">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
      </div>
    );
  }

  // 计算导航栏的高度（用于布局计算）
  const topNavHeight = 60; // PC端顶部导航栏高度（像素）
  const bottomNavHeight = 56; // 移动端底部导航栏高度（像素）

  // 计算容器的顶部偏移和高度
  const containerTopOffset = isDesktop ? topNavHeight : 0;
  const containerHeight = isDesktop
    ? `calc(100vh - ${topNavHeight}px)`
    : `calc(100vh - ${bottomNavHeight}px)`;

  return (
    <div className={isDesktop ? "relative w-full h-full bg-black overflow-hidden" : "fixed inset-0 bg-black overflow-hidden"}>
      {/* 视频容器 - 响应式布局，PC端适配侧边栏，移动端全屏 */}
      <div
        ref={containerRef}
        className={isDesktop
          ? "relative w-full overflow-y-auto snap-y snap-mandatory no-scrollbar"
          : "absolute inset-0 overflow-y-auto snap-y snap-mandatory no-scrollbar"
        }
        style={{
          top: '0px', // 视频容器从顶部开始，占据全屏
          height: isDesktop
            ? `calc(100vh - ${topNavHeight}px)` // PC端填充剩余空间
            : `calc(100vh - ${bottomNavHeight}px)`, // 移动端只为底部导航栏留空间
          scrollSnapType: "y mandatory",
          overflowX: "hidden",
          bottom: isDesktop ? '0px' : `${bottomNavHeight}px`
        }}
      >
        {videos.length > 0 ? (
          videos.map((video, index) => (
            <div
              key={`${video.girl.id}-${index}`}
              className="snap-start w-full" // 使用snap-start确保视频从顶部开始对齐
              style={{
                height: isDesktop
                  ? `calc(100vh - ${topNavHeight}px)` // PC端视频填充整个可用空间
                  : `calc(100vh - ${bottomNavHeight}px)`, // 移动端视频占据全屏高度，只为底部导航栏留空间
                scrollSnapAlign: "start", // 确保滚动时从顶部对齐
                position: "relative" // 使子元素可以相对于此容器定位
              }}
            >
              <VideoCard
                video={video.videoUrl}
                girl={video.girl}
                isActive={index === currentIndex}
                shouldPreload={index > currentIndex && index <= currentIndex + 2}
                preloaded={preloadedVideos.has(video.videoUrl)}
                bottomNavHeight={isDesktop ? 0 : bottomNavHeight}
                isDesktop={isDesktop}
                onPreloaded={(url) => {
                  setPreloadedVideos(prev => {
                    const newSet = new Set(prev);
                    newSet.add(url);
                    return newSet;
                  });
                }}
                onVideoInView={() => setCurrentIndex(index)}
              />
            </div>
          ))
        ) : (
          <div className="flex items-center justify-center h-full">
            <p className="text-white text-lg">没有找到视频</p>
          </div>
        )}

        {/* 加载更多指示器 */}
        {hasMore && (
          <div ref={loadingRef} className="h-20 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white"></div>
          </div>
        )}
      </div>

      {/* 顶部城市筛选 - 覆盖在视频上方 */}
      <div
        className={isDesktop
          ? "absolute left-0 right-0 top-0 z-50 no-scrollbar"
          : "fixed left-0 right-0 top-0 z-50 no-scrollbar"
        }
        style={{
          overflowX: 'hidden',
          background: 'transparent'
        }}
      >
        <div
          className="flex items-center p-3 overflow-x-auto no-scrollbar"
          style={{
            msOverflowStyle: 'none',
            scrollbarWidth: 'none',
            WebkitOverflowScrolling: 'touch',
            background: 'transparent'
          }}
        >

          <button
            className={`px-3 py-1 mx-1 text-sm whitespace-nowrap relative ${
              selectedCity === "" && !isPremiumCamp ? "text-white font-bold" : "text-white/80"
            }`}
            style={{ textShadow: '0 1px 2px rgba(0,0,0,0.5)' }}
            onClick={() => handleCityChange("")}
          >
            全部
            {selectedCity === "" && !isPremiumCamp && <div className="absolute bottom-0 left-0 w-full h-0.5 bg-white"></div>}
          </button>

          {/* 极品营选项 */}
          <button
            className={`px-3 py-1 mx-1 text-sm whitespace-nowrap relative ${
              isPremiumCamp ? "text-white font-bold" : "text-white/80"
            }`}
            style={{ textShadow: '0 1px 2px rgba(0,0,0,0.5)' }}
            onClick={handlePremiumCampClick}
          >
            ⁵ꪝ极品营
            {isPremiumCamp && <div className="absolute bottom-0 left-0 w-full h-0.5 bg-white"></div>}
          </button>

          {cities.map((city) => (
            <button
              key={city}
              className={`px-3 py-1 mx-1 text-sm whitespace-nowrap relative ${
                selectedCity === city && !isPremiumCamp ? "text-white font-bold" : "text-white/80"
              }`}
              style={{ textShadow: '0 1px 2px rgba(0,0,0,0.5)' }}
              onClick={() => handleCityChange(city)}
            >
              {city}
              {selectedCity === city && !isPremiumCamp && <div className="absolute bottom-0 left-0 w-full h-0.5 bg-white"></div>}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
