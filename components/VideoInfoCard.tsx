"use client";

import React, { useMemo, useEffect, useState } from "react";
import { Girl } from "../types/girl";
import { useRouter } from "next/navigation";

interface VideoInfoCardProps {
  girl: Girl;
  isActive: boolean;
  bottomNavHeight?: number;
  isDesktop?: boolean;
  onMuteChange?: (muted: boolean) => void; // 声音状态变化回调
  initialMuted?: boolean; // 初始声音状态
}

export default function VideoInfoCard({
  girl,
  isActive,
  bottomNavHeight = 56,
  isDesktop = false,
  onMuteChange,
  initialMuted = false
}: VideoInfoCardProps) {
  // 使用Next.js的路由器进行客户端导航
  const router = useRouter();
  // 检测是否为iOS设备 - 使用useEffect内部的状态来避免服务器端渲染问题
  const [isIOS, setIsIOS] = useState(false);

  // 声音控制状态 - 使用传入的初始值
  const [isMuted, setIsMuted] = useState(initialMuted);

  // 在客户端初始化时检测设备类型
  useEffect(() => {
    const isiOSDevice = /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream;
    setIsIOS(isiOSDevice);
  }, []);

  // 当initialMuted属性变化时更新状态
  useEffect(() => {
    setIsMuted(initialMuted);
  }, [initialMuted]);

  // 处理iOS Safari地址栏高度变化的问题
  const [safeAreaBottom, setSafeAreaBottom] = useState(0);
  const [viewportHeight, setViewportHeight] = useState(0);
  const [bottomPadding, setBottomPadding] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // 处理声音开关
  const toggleMute = () => {
    const newMutedState = !isMuted;
    setIsMuted(newMutedState);

    // 通知父组件声音状态变化
    if (onMuteChange) {
      onMuteChange(newMutedState);
    }

    // 同时发送自定义事件，让其他组件也能接收到声音状态变化
    if (typeof window !== 'undefined') {
      const event = new CustomEvent('videoMuteToggle', {
        detail: { muted: newMutedState }
      });
      window.dispatchEvent(event);
    }
  };

  // 监听视口高度变化，处理iOS Safari地址栏的问题
  useEffect(() => {
    // 使用组件级别的isIOS变量，不需要在这里重新定义

    // 底部导航栏高度（来自TikTokClient.tsx）
    const bottomNavHeight = 56;

    // 额外的安全距离，确保内容不被遮挡
    const safetyMargin = 12;

    // 检测是否为iPhone X或更新的机型（有底部安全区域的设备）
    const isIPhoneWithNotch = isIOS && (
      // 检测是否有安全区域 - 使用CSS变量检测
      window.innerHeight >= 812 || // iPhone X/XS/11 Pro 及更高的设备
      (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) // PWA模式
    );

    // 为了调试，输出设备信息
    console.log(`设备信息: iOS=${isIOS}, iPhoneWithNotch=${isIPhoneWithNotch}`);

    // 检测是否处于全屏模式
    const checkFullscreen = () => {
      if (!isIOS) return false;

      // 在iOS上，我们可以通过多种方式检测是否处于全屏模式

      // 1. 通过比较窗口高度和屏幕高度
      const windowHeight = window.innerHeight;
      const screenHeight = window.screen.height;

      // 2. 检测是否有地址栏
      // 在iOS Safari中，非全屏模式下通常会显示地址栏，导致窗口高度小于屏幕高度
      // 全屏模式下地址栏会隐藏，窗口高度接近屏幕高度

      // 3. 检测是否处于独立应用模式（PWA）
      const isPWA = window.matchMedia('(display-mode: standalone)').matches;

      // 4. 检测是否有底部工具栏
      // 在iOS Safari中，非全屏模式下通常会显示底部工具栏
      // 全屏模式下底部工具栏会隐藏

      // 综合判断
      // 根据设备类型使用不同的阈值
      let fullscreenThreshold;

      if (isPWA) {
        // PWA模式下，窗口高度通常非常接近屏幕高度
        fullscreenThreshold = 0.98;
      } else if (isIPhoneWithNotch) {
        // 带刘海的iPhone（X及以上）在全屏模式下，窗口高度与屏幕高度比例通常在0.92以上
        fullscreenThreshold = 0.92;
      } else {
        // 其他iOS设备
        fullscreenThreshold = 0.95;
      }

      // 判断是否处于全屏模式
      const heightRatio = windowHeight / screenHeight;
      const isFullscreenMode = heightRatio > fullscreenThreshold;

      // 调试信息
      console.log(`窗口高度: ${windowHeight}, 屏幕高度: ${screenHeight}, 比例: ${heightRatio.toFixed(2)}, 阈值: ${fullscreenThreshold}, 全屏: ${isFullscreenMode}`);

      return isFullscreenMode;
    };

    // 获取底部安全区域高度
    const getSafeAreaBottom = () => {
      if (!isIOS) return 0;

      // 尝试从CSS变量获取安全区域高度
      let safeAreaValue = 0;

      try {
        // 尝试获取CSS环境变量 - 底部安全区域
        const cssEnv = getComputedStyle(document.documentElement).getPropertyValue('--sab');
        if (cssEnv && !isNaN(parseInt(cssEnv))) {
          safeAreaValue = parseInt(cssEnv);
        }
      } catch (e) {
        console.log('无法获取CSS安全区域变量');
      }

      // 如果无法获取CSS变量，使用设备检测
      if (safeAreaValue === 0) {
        if (isIPhoneWithNotch) {
          // iPhone X及更新机型的底部安全区域约34px
          safeAreaValue = 34;
        } else if (isIOS) {
          // 较老的iPhone机型
          safeAreaValue = 0;
        }
      }

      return safeAreaValue;
    };

    const handleResize = () => {
      // 获取视口高度
      const vh = window.innerHeight;
      setViewportHeight(vh);

      // 检测是否处于全屏模式
      const fullscreenMode = checkFullscreen();
      setIsFullscreen(fullscreenMode);

      // 获取底部安全区域高度
      const safeArea = getSafeAreaBottom();
      setSafeAreaBottom(safeArea);

      // 计算总的底部填充
      // 根据是否处于全屏模式使用不同的填充策略
      let totalPadding;

      // 无论是否全屏模式，都使用足够大的底部填充
      // 这样可以确保内容不会被底部导航栏遮挡

      // 基础填充值 - 所有设备都使用
      let basePadding = 60; // 使用一个较大的基础值

      // 根据设备类型和视口高度调整
      if (isIOS) {
        // iOS设备需要更大的底部填充
        if (isIPhoneWithNotch) {
          // 带刘海的iPhone（X及以上）需要考虑底部安全区域
          basePadding = 80; // 使用更大的基础值
        }

        // 根据是否全屏模式调整
        if (fullscreenMode) {
          // 全屏模式下可以使用较小的填充
          basePadding = Math.max(20, basePadding / 2);
        } else {
          // 非全屏模式下，根据视口高度进一步调整
          if (viewportHeight < 700) {
            basePadding += 20; // 小屏幕设备增加更多填充
          } else if (viewportHeight < 800) {
            basePadding += 10; // 中等屏幕设备增加填充
          }
        }
      } else {
        // 非iOS设备
        if (fullscreenMode) {
          // 全屏模式下使用较小的填充
          basePadding = 10;
        } else {
          // 非全屏模式下使用中等填充
          basePadding = 30;
        }
      }

      // 最终的底部填充 = 基础填充 + 安全区域
      totalPadding = basePadding + safeAreaBottom;

      setBottomPadding(totalPadding);

      // 调试信息
      console.log(`视口高度: ${vh}, 全屏模式: ${fullscreenMode}, 底部填充: ${totalPadding}px`);
    };

    // 初始设置
    // 添加CSS变量以检测安全区域
    if (isIOS) {
      const style = document.createElement('style');
      style.innerHTML = `
        :root {
          --sat: env(safe-area-inset-top);
          --sar: env(safe-area-inset-right);
          --sab: env(safe-area-inset-bottom);
          --sal: env(safe-area-inset-left);
        }
      `;
      document.head.appendChild(style);
    }

    // 初始计算
    handleResize();

    // 监听resize事件
    window.addEventListener('resize', handleResize);

    // 监听iOS Safari特有的视口高度变化
    window.addEventListener('orientationchange', handleResize);

    // 监听滚动事件，在iOS Safari上滚动时地址栏可能会隐藏/显示
    if (isIOS) {
      window.addEventListener('scroll', handleResize);

      // 在iOS上，还需要监听touchmove事件，因为滚动可能不会触发resize
      window.addEventListener('touchmove', handleResize);

      // 监听visibilitychange事件，处理应用切换到前台时的情况
      document.addEventListener('visibilitychange', handleResize);
    }

    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
      if (isIOS) {
        window.removeEventListener('scroll', handleResize);
        window.removeEventListener('touchmove', handleResize);
        document.removeEventListener('visibilitychange', handleResize);
      }
    };
  }, [bottomNavHeight]); // 添加bottomNavHeight作为依赖，当它变化时重新计算

  // 处理所有信息的组合显示
  const combinedInfo = useMemo(() => {
    // 收集所有需要显示的信息
    const allInfo = [];

    // 位置信息
    const locationParts = [];
    if (girl.city) locationParts.push(girl.city);
    if (girl.district) locationParts.push(girl.district);
    if (locationParts.length > 0) {
      allInfo.push(locationParts.join(" · "));
    }

    // 个人信息
    const personalParts = [];
    if (girl.height) personalParts.push(`${girl.height}cm`);
    if (girl.weight) personalParts.push(`${girl.weight}kg`);
    if (girl.age) personalParts.push(`${girl.age}岁`);
    if (girl.cup) personalParts.push(`${girl.cup}罩杯`);
    if (personalParts.length > 0) {
      allInfo.push(personalParts.join(" · "));
    }

    // 返回组合后的信息
    return allInfo.join(" · ");
  }, [girl.city, girl.district, girl.height, girl.weight, girl.age, girl.cup]);

  // 处理价格信息 - 单独处理，因为它的样式不同
  const priceInfo = useMemo(() => {
    return girl.price || "";
  }, [girl.price]);

  // 为了兼容现有代码，保留这些变量
  const locationInfo = "";
  const personalInfo = "";

  return (
    <div
      className={`${isDesktop ? 'absolute bottom-4 left-4 right-4' : 'absolute bottom-0 left-0 right-0'} transition-opacity duration-300 ${
        isActive ? 'opacity-100' : 'opacity-0'
      }`}
      style={{
        // PC端不需要底部填充，移动端使用计算出的底部填充
        paddingBottom: isDesktop ? '0px' : `${bottomPadding}px`,
        // 确保在PC端有足够高的z-index，在移动端使用默认值
        zIndex: isDesktop ? 30 : 10,
      }}
    >

      {/* 底部渐变背景 - 仅移动端显示 */}
      {!isDesktop && (
        <div
          className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent pointer-events-none"
          style={{
            height: isFullscreen ? '180px' : '350px', // 大幅增加非全屏模式下的高度，确保覆盖所有内容
            // 确保渐变背景足够高，覆盖所有内容包括底部填充区域
            bottom: `-${bottomPadding + (isFullscreen ? 4 : 40)}px` // 大幅增加非全屏模式下的额外覆盖
          }}
        ></div>
      )}

      {/* 信息内容 - 响应式设计 */}
      <div
        className={`relative text-white ${isDesktop ? 'px-6 py-4' : 'px-3 sm:px-4'}`}
        style={isDesktop ? {
          // PC端：确保有足够的空间和清晰的布局，无背景
          minHeight: 'auto',
          display: 'flex',
          flexDirection: 'column',
          gap: '8px'
        } : {
          // 移动端：根据是否全屏模式调整上下内边距
          paddingTop: isFullscreen ? '10px' : '12px',
          paddingBottom: isFullscreen ?
            `${Math.max(8, bottomPadding + 4)}px` : // 全屏模式下使用较小的底部内边距
            `${Math.max(24, bottomPadding + 16)}px`,  // 非全屏模式下大幅增加底部内边距，确保内容不被遮挡
        }}
      >
        {/* 标题、价格和声音控制按钮 - 响应式设计 */}
        <div
          className={`flex items-center justify-between ${isDesktop ? 'mb-0' : 'mb-1'}`}
          style={isDesktop ? {
            // PC端：适中的文本阴影，确保在模糊背景上清晰可读
            textShadow: '0 2px 4px rgba(0,0,0,0.7), 0 1px 2px rgba(0,0,0,0.5)',
            minHeight: '32px' // 确保有足够的高度
          } : {
            // 移动端：添加轻微的文本阴影，增强在各种背景上的可读性
            textShadow: '0 1px 2px rgba(0,0,0,0.5)'
          }}
        >
          <div className={`flex items-center flex-1 min-w-0 ${isDesktop ? 'gap-3' : ''}`}>
            <h3
              className={`${isDesktop ? 'text-xl font-bold' : (isFullscreen ? 'text-base' : 'text-lg sm:text-xl')} font-bold line-clamp-1 ${isDesktop ? 'flex-shrink-0' : 'mr-2'}`}
              style={isDesktop ? {
                textShadow: '0 2px 4px rgba(0,0,0,0.7), 0 1px 2px rgba(0,0,0,0.5)',
                marginRight: '12px'
              } : {}}
            >
              {girl.title}
            </h3>
            {priceInfo && (
              <span
                className={`font-medium text-white whitespace-nowrap ${isDesktop ? 'text-base flex-shrink-0' : 'text-sm'} ${isDesktop ? '' : 'mr-2'}`}
                style={isDesktop ? {
                  textShadow: '0 2px 4px rgba(0,0,0,0.7), 0 1px 2px rgba(0,0,0,0.5)',
                  marginRight: '12px'
                } : {}}
              >
                {priceInfo}
              </span>
            )}

            {/* 查看资料按钮 - 放在价格信息右侧 */}
            <button
              className={`px-3 py-1 font-medium text-white whitespace-nowrap bg-transparent rounded transition-opacity duration-200 hover:opacity-80 hover:bg-white/10 flex-shrink-0 ${isDesktop ? 'text-sm' : 'text-xs sm:text-sm'}`}
              style={isDesktop ? {
                textShadow: '0 2px 4px rgba(0,0,0,0.7), 0 1px 2px rgba(0,0,0,0.5)',
                marginRight: '8px'
              } : {
                textShadow: '0 1px 2px rgba(0,0,0,0.5)',
                lineHeight: '1.5'
              }}
              onClick={(e) => {
                // 阻止事件冒泡，避免触发视频点击事件
                e.stopPropagation();

                // 在新标签页打开详情页面
                window.open(`/girl/${girl.girl_id}`, '_blank');
              }}
            >
              查看资料
            </button>
          </div>

          {/* 声音控制按钮 - 与标题在同一行 */}
          <button
            onClick={toggleMute}
            className="ml-2 p-1.5 rounded-full bg-transparent transition-opacity duration-200 hover:opacity-80 flex-shrink-0"
            aria-label={isMuted ? "开启声音" : "关闭声音"}
          >
            {isMuted ? (
              // 静音图标
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M11 5L6 9H2v6h4l5 4V5z"></path>
                <line x1="23" y1="9" x2="17" y2="15"></line>
                <line x1="17" y1="9" x2="23" y2="15"></line>
              </svg>
            ) : (
              // 有声图标
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M11 5L6 9H2v6h4l5 4V5z"></path>
                <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"></path>
              </svg>
            )}
          </button>
        </div>

        {/* 位置信息和个人信息 - 响应式设计 */}
        <div
          className={`text-white/90 ${isDesktop ? 'text-base' : `text-xs sm:text-sm ${isFullscreen ? 'mb-1' : 'mb-1.5 sm:mb-2'}`}`}
          style={isDesktop ? {
            fontSize: '16px',
            lineHeight: '1.6',
            // PC端：适中的文本阴影，确保在模糊背景上清晰可读
            textShadow: '0 2px 4px rgba(0,0,0,0.7), 0 1px 2px rgba(0,0,0,0.5)',
            marginBottom: '4px'
          } : {
            // 移动端：添加轻微的文本阴影，增强在各种背景上的可读性
            textShadow: '0 1px 2px rgba(0,0,0,0.5)',
            // 使用更大的字体和行高，确保文本清晰可见
            fontSize: '14px',
            lineHeight: '1.5'
          }}
        >
          {/* 显示组合后的信息 */}
          {combinedInfo}
        </div>

        {/* 描述信息 - 响应式显示 */}
        {girl.description && (isDesktop || (!isFullscreen || viewportHeight > 700)) && (
          <p
            className={`text-white/80 ${isDesktop ? 'text-sm line-clamp-2' : `text-xs sm:text-sm ${isFullscreen ? 'mb-1' : 'mb-6'} truncate`}`}
            style={isDesktop ? {
              fontSize: '15px',
              lineHeight: '1.5',
              // PC端：适中的文本阴影，确保在模糊背景上清晰可读
              textShadow: '0 2px 4px rgba(0,0,0,0.7), 0 1px 2px rgba(0,0,0,0.5)',
              maxHeight: '3em', // 限制最大高度为2行
              overflow: 'hidden'
            } : {
              // 移动端：添加轻微的文本阴影，增强在各种背景上的可读性
              textShadow: '0 1px 2px rgba(0,0,0,0.5)',
              // 使用更大的字体和行高，确保文本清晰可见
              fontSize: '14px',
              lineHeight: '1.5',
              // 添加底部填充，确保内容不被导航栏遮挡
              paddingBottom: '16px'
            }}
          >
            {girl.description}
          </p>
        )}
      </div>
    </div>
  );
}
