"use client";

import React, { useState, useMemo, useCallback } from "react";
import { Button } from "./ui/button";
import { ScrollArea } from "./ui/scroll-area";
import { ChevronDown, MapPin, X } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import { cityNameToUrl, firstTierCities } from "../lib/cityNameMapping";
import Link from "next/link";
import { motion, AnimatePresence } from "framer-motion";
import { toast } from "react-hot-toast";

interface SidebarProps {
	cities?: string[];
	selectedCity?: string;
	cityDataCounts?: { [city: string]: number };
	loading?: boolean;
	isMobile: boolean;
	isOpen: boolean;
	onClose: () => void;
	isAuthenticated: boolean;
	isVIP?: boolean;
}

const Sidebar = React.memo(({
	cities = [],
	selectedCity = "",
	cityDataCounts = {},
	loading = false,
	isMobile,
	isOpen,
	onClose,
	isAuthenticated,
	isVIP = false
}: SidebarProps): JSX.Element => {
	const [isTierOneExpanded, setIsTierOneExpanded] = useState(true);
	const [isTierTwoExpanded, setIsTierTwoExpanded] = useState(true);
	const pathname = usePathname();
	const router = useRouter();

	const availableCities = useMemo(() => Object.keys(cityNameToUrl), []);

	const filterCitiesWithCount = useCallback((cityList: string[]) => {
		return cityList.filter((city) => (cityDataCounts[city] || 0) > 0);
	}, [cityDataCounts]);

	const tierOneCities = useMemo(() => {
		const filteredCities = firstTierCities.filter((city) => availableCities.includes(city));
		return filterCitiesWithCount(filteredCities);
	}, [availableCities, filterCitiesWithCount]);

	const tierTwoCities = useMemo(() => {
		const filteredCities = availableCities.filter((city) => !firstTierCities.includes(city));
		return filterCitiesWithCount(filteredCities).sort((a, b) => (cityDataCounts[b] || 0) - (cityDataCounts[a] || 0));
	}, [availableCities, filterCitiesWithCount, cityDataCounts]);

	const handleCityClick = useCallback((city: string) => {
		const cityUrl = `/city/${cityNameToUrl[city]}`;
		if (pathname !== cityUrl) {
			router.push(cityUrl);
			if (isMobile) {
				onClose();
			}
		}
	}, [router, pathname, isMobile, onClose]);

	const renderCityButton = useMemo(() => {
		const CityButton = (city: string) => {
			const isSelected = pathname === `/city/${cityNameToUrl[city]}` || selectedCity === city;
			const count = cityDataCounts[city] || 0;
			return (
				<motion.div
					whileHover={{ scale: 1.02 }}
					whileTap={{ scale: 0.98 }}
					key={city}
				>
					<Button
						variant={isSelected ? "secondary" : "ghost"}
						className={`w-full justify-between items-center text-sm py-2.5 px-3 rounded-lg transition-all duration-200 ${
							isSelected
								? "bg-gradient-to-r from-[#ffa31a]/20 to-[#ffa31a]/10 text-[#ffa31a] shadow-inner"
								: "text-white hover:bg-white/5 hover:text-[#ffa31a]"
						}`}
						onClick={() => handleCityClick(city)}
					>
						<div className="flex items-center gap-2">
							<MapPin className={`h-4 w-4 ${isSelected ? "text-[#ffa31a]" : "text-gray-400"}`} />
							<span>{city}</span>
						</div>
						<span className={`text-xs px-2 py-0.5 rounded-full ${
							isSelected ? "bg-[#ffa31a]/20 text-[#ffa31a]" : "bg-white/5 text-gray-400"
						}`}>
							{count}
						</span>
					</Button>
				</motion.div>
			);
		};
		CityButton.displayName = 'CityButton';
		return CityButton;
	}, [pathname, selectedCity, cityDataCounts, handleCityClick]);

	const renderCityList = (cityList: string[], isExpanded: boolean) => (
		<AnimatePresence initial={false}>
			{isExpanded && (
				<motion.div
					initial="collapsed"
					animate="expanded"
					exit="collapsed"
					variants={{
						expanded: {
							opacity: 1,
							height: "auto",
							transition: {
								duration: 0.4,
								ease: [0.4, 0, 0.2, 1]
							}
						},
						collapsed: {
							opacity: 0,
							height: 0,
							transition: {
								duration: 0.3,
								ease: [0.4, 0, 0.2, 1]
							}
						},
					}}
					className="flex flex-col gap-2 overflow-hidden"
				>
					{cityList.map(renderCityButton)}
				</motion.div>
			)}
		</AnimatePresence>
	);

	const citiesList = (
		<div className={`${isMobile ? "mt-4" : "mt-2"}`}>
			<ScrollArea className="h-[calc(100vh-200px)] pr-3 mr-[-0.75rem]">
				{loading ? (
					<div className="space-y-6">
						<div className="space-y-2">
							<div className="flex items-center justify-between h-10 px-3 rounded-lg bg-gradient-to-r from-gray-700/30 to-gray-700/10 animate-pulse">
								<div className="h-4 w-16 bg-gray-600/30 rounded" />
								<div className="h-4 w-4 bg-gray-600/30 rounded" />
							</div>
							<div className="space-y-2">
								{[...Array(4)].map((_, index) => (
									<div key={index} className="flex items-center justify-between h-12 px-3 rounded-lg bg-gradient-to-r from-gray-700/30 to-gray-700/10 animate-pulse">
										<div className="flex items-center gap-2">
											<div className="h-4 w-4 bg-gray-600/30 rounded" />
											<div className="h-4 w-16 bg-gray-600/30 rounded" />
										</div>
										<div className="h-4 w-8 bg-gray-600/30 rounded-full" />
									</div>
								))}
							</div>
						</div>
						<div className="space-y-2">
							<div className="flex items-center justify-between h-10 px-3 rounded-lg bg-gradient-to-r from-gray-700/30 to-gray-700/10 animate-pulse">
								<div className="h-4 w-16 bg-gray-600/30 rounded" />
								<div className="h-4 w-4 bg-gray-600/30 rounded" />
							</div>
							<div className="space-y-2">
								{[...Array(6)].map((_, index) => (
									<div key={index} className="flex items-center justify-between h-12 px-3 rounded-lg bg-gradient-to-r from-gray-700/30 to-gray-700/10 animate-pulse">
										<div className="flex items-center gap-2">
											<div className="h-4 w-4 bg-gray-600/30 rounded" />
											<div className="h-4 w-20 bg-gray-600/30 rounded" />
										</div>
										<div className="h-4 w-8 bg-gray-600/30 rounded-full" />
									</div>
								))}
							</div>
						</div>
					</div>
				) : (
					<div className="space-y-6">
						<div className="space-y-2">
							<Button
								variant="ghost"
								className="w-full justify-between items-center text-base py-2 px-3 text-white hover:text-[#ffa31a] hover:bg-white/5 rounded-lg"
								onClick={() => setIsTierOneExpanded(!isTierOneExpanded)}
							>
								<span className="font-medium">一线城市</span>
								<motion.div
									animate={{ rotate: isTierOneExpanded ? 180 : 0 }}
									transition={{ duration: 0.3 }}
								>
									<ChevronDown className="h-5 w-5" />
								</motion.div>
							</Button>
							{renderCityList(tierOneCities, isTierOneExpanded)}
						</div>
						<div className="space-y-2">
							<Button
								variant="ghost"
								className="w-full justify-between items-center text-base py-2 px-3 text-white hover:text-[#ffa31a] hover:bg-white/5 rounded-lg"
								onClick={() => setIsTierTwoExpanded(!isTierTwoExpanded)}
							>
								<span className="font-medium">其他城市</span>
								<motion.div
									animate={{ rotate: isTierTwoExpanded ? 180 : 0 }}
									transition={{ duration: 0.3 }}
								>
									<ChevronDown className="h-5 w-5" />
								</motion.div>
							</Button>
							{renderCityList(tierTwoCities, isTierTwoExpanded)}
						</div>
					</div>
				)}
			</ScrollArea>
		</div>
	);

	const renderNavLinks = () => {
		const navItems = [
			{ href: "/city", icon: "❤️‍🔥", text: "资源库" },
			{ href: "/guide", icon: "📖", text: "约会指南" },
			{ href: "/linktree", icon: "🧭", text: "导航" },
			{ href: "/feedback", icon: "🦋", text: "至真园™" },
			{ href: "/jianier5kw", icon: "💎", text: "⁵ꪝ极品营" },
			{ href: "/tiktok", icon: "🧚🏼‍♀️", text: "抖妮" }
		];

		const NavButton = ({ href, icon, text }: { href: string; icon: string; text: string }) => (
			<Button
				variant="ghost"
				className={`w-full justify-start items-center text-base py-2.5 px-3 rounded-lg transition-all duration-200 ${
					pathname === href
						? "bg-gradient-to-r from-[#ffa31a]/20 to-[#ffa31a]/10 text-[#ffa31a]"
						: "text-white hover:bg-white/5 hover:text-[#ffa31a]"
				}`}
				asChild
			>
				<Link href={href}>
					<span className="mr-2">{icon}</span>
					{text}
				</Link>
			</Button>
		);

		if (isMobile) {
			return (
				<div className="flex flex-col space-y-2">
					{navItems.map((item) => (
						<NavButton key={item.href} {...item} />
					))}
				</div>
			);
		}

		return (
			<div className="flex flex-col space-y-2">
				{navItems.slice(-3).map((item) => (
					<NavButton key={item.href} {...item} />
				))}
			</div>
		);
	};

	const sidebarContent = (
		<>
			{renderNavLinks()}
			{citiesList}
		</>
	);

	if (isMobile) {
		return (
			<>
				<motion.div
					className="fixed inset-y-0 left-0 z-40 w-56 bg-dark-background overflow-hidden"
					initial={{ x: "-100%" }}
					animate={{
						x: isOpen ? 0 : "-100%",
						transition: {
							duration: 0.3,
							ease: [0.4, 0, 0.2, 1]
						}
					}}
					style={{
						pointerEvents: isOpen ? 'auto' : 'none'
					}}
				>
					<motion.div
						className="h-full p-4 flex flex-col overflow-hidden"
						animate={{
							opacity: isOpen ? 1 : 0,
							transition: {
								duration: 0.2,
								ease: "easeInOut"
							}
						}}
					>
						<div className="flex items-center justify-between mb-6">
							<h2 className="text-xl font-bold text-[#ffa31a]">菜单</h2>
							<Button
								variant="ghost"
								size="icon"
								onClick={onClose}
								className="hover:bg-[#292929]"
							>
								<X className="h-5 w-5 text-[#ffa31a]" />
							</Button>
						</div>
						{sidebarContent}
					</motion.div>
				</motion.div>
				<AnimatePresence>
					{isOpen && (
						<motion.div
							className="fixed inset-0 bg-black bg-opacity-50 z-30"
							onClick={onClose}
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							exit={{ opacity: 0 }}
							transition={{ duration: 0.2 }}
						/>
					)}
				</AnimatePresence>
			</>
		);
	}

	return (
		<div className="w-48 min-w-48 max-w-48 bg-dark-background p-4 ml-2 flex flex-col h-full overflow-hidden">
			{loading ? (
				<div className="space-y-6">
					<div className="space-y-2">
						<div className="h-10 bg-gradient-to-r from-gray-700/30 to-gray-700/10 rounded-lg animate-pulse" />
						<div className="space-y-2">
							{[...Array(3)].map((_, index) => (
								<div key={index} className="h-12 bg-gradient-to-r from-gray-700/30 to-gray-700/10 rounded-lg animate-pulse" />
							))}
						</div>
					</div>
					<div className="space-y-2">
						<div className="h-10 bg-gradient-to-r from-gray-700/30 to-gray-700/10 rounded-lg animate-pulse" />
						<div className="space-y-2">
							{[...Array(5)].map((_, index) => (
								<div key={index} className="h-12 bg-gradient-to-r from-gray-700/30 to-gray-700/10 rounded-lg animate-pulse" />
							))}
						</div>
					</div>
				</div>
			) : (
				sidebarContent
			)}
		</div>
	);
});

Sidebar.displayName = "Sidebar";

export default Sidebar;
