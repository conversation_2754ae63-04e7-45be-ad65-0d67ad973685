"use client";

import React, { useEffect, useRef, useState } from "react";
import { Girl } from "../types/girl";
import { useInView } from "react-intersection-observer";
import { Play, Crown } from "lucide-react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import VideoInfoCard from "./VideoInfoCard";

// 从媒体URL中获取文件ID
const getFileIdFromUrl = (url: string) => {
  // 假设URL格式为 "/api/media/fileId" 或 "https://domain.com/path/fileId"
  const segments = url.split('/');
  const fileId = segments[segments.length - 1];
  // 移除可能存在的文件扩展名（如.mp4）
  return fileId.split('.')[0];
};

// 获取视频缩略图URL
const getThumbnailUrl = (mediaUrl: string) => {
  const fileId = getFileIdFromUrl(mediaUrl);

  // 解析URL以获取baseURL
  try {
    // 对于绝对URL和相对URL都有效的处理方式
    const url = new URL(mediaUrl.startsWith('http') ? mediaUrl : `http://example.com${mediaUrl}`);

    // 获取URL的各个部分
    const protocol = mediaUrl.startsWith('http') ? `${url.protocol}//` : '';
    const host = mediaUrl.startsWith('http') ? url.host : '';

    // 检查URL是否包含proxy路径，如果有则直接替换为thumbnail路径
    if (url.pathname.includes('/proxy2/')) {
      // 去掉proxy路径，改为thumbnail路径
      const basePath = `${protocol}${host}`;
      return `${basePath}/thumbnail/${fileId}`;
    }

    // 获取路径部分，并将"media"替换为"thumbnail"
    let path = url.pathname;
    const pathParts = path.split('/');
    const mediaIndex = pathParts.findIndex(part => part === 'media');

    if (mediaIndex !== -1) {
      pathParts[mediaIndex] = 'thumbnail';
      // 移除最后一部分(文件名)，添加fileId
      pathParts.pop();
      path = `${pathParts.join('/')}/${fileId}`;
    } else {
      // 如果路径中没有"media"，找到最后一个斜杠位置
      const lastSlashIndex = path.lastIndexOf('/');
      if (lastSlashIndex !== -1) {
        // 替换最后部分为"thumbnail/fileId"
        path = `${path.substring(0, lastSlashIndex)}/thumbnail/${fileId}`;
      }
    }

    return `${protocol}${host}${path}`;
  } catch (e) {
    // 如果解析失败，尝试简单的字符串替换
    if (mediaUrl.includes('/proxy2/')) {
      const urlParts = mediaUrl.split('/proxy2/');
      return `${urlParts[0]}/thumbnail/${fileId}`;
    }

    // 如果所有方法都失败，返回原始URL
    console.error('无法生成缩略图URL:', e);
    return mediaUrl;
  }
};

interface VideoCardProps {
  video: string;
  girl: Girl;
  isActive: boolean;
  shouldPreload?: boolean;
  preloaded?: boolean;
  bottomNavHeight?: number;
  isDesktop?: boolean;
  onPreloaded?: (url: string) => void;
  onVideoInView: () => void;
}

export default function VideoCard({
  video,
  girl,
  isActive,
  shouldPreload = false,
  preloaded = false,
  bottomNavHeight = 56,
  isDesktop = false,
  onPreloaded,
  onVideoInView
}: VideoCardProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const preloadVideoRef = useRef<HTMLVideoElement | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false); // 默认不静音，有声音
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [showVideo, setShowVideo] = useState(false);
  const [thumbnailLoaded, setThumbnailLoaded] = useState(false);
  const [isPreloading, setIsPreloading] = useState(false);
  const [isPreloaded, setIsPreloaded] = useState(preloaded);
  const [hideThumbnail, setHideThumbnail] = useState(false);
  const { data: session } = useSession();
  const router = useRouter();
  const isVIP = session?.user?.isVIP;
  const shouldBlur = !isVIP;

  // 获取视频缩略图URL
  const thumbnailUrl = getThumbnailUrl(video);

  // 使用 react-intersection-observer 检测视频是否在视口中
  const { ref, inView } = useInView({
    threshold: 0.6, // 当60%的视频可见时触发
  });

  // 当视频进入视口时通知父组件
  useEffect(() => {
    if (inView) {
      onVideoInView();

      // 当视频进入视口时，如果还没有显示视频，则自动加载视频
      if (isActive && !showVideo && thumbnailLoaded) {
        setShowVideo(true);
      }
    }
  }, [inView, onVideoInView, isActive, showVideo, thumbnailLoaded]);

  // 处理视频预加载
  useEffect(() => {
    // 如果应该预加载但尚未预加载
    if (shouldPreload && !isPreloaded && !isPreloading) {
      setIsPreloading(true);

      // 创建一个新的video元素进行预加载
      const preloadVideo = document.createElement('video');
      preloadVideo.preload = 'auto';
      preloadVideo.src = video;
      preloadVideo.muted = false; // 默认不静音，有声音
      preloadVideo.style.display = 'none';

      // 尝试预缓冲一些视频数据
      try {
        // 设置视频加载优先级
        if ('fetchPriority' in preloadVideo) {
          (preloadVideo as any).fetchPriority = 'high';
        }

        // 尝试预加载视频的前几秒
        preloadVideo.load();
      } catch (e) {
        console.error('预加载视频时出错:', e);
      }

      // 监听预加载完成事件
      preloadVideo.addEventListener('loadeddata', () => {
        setIsPreloaded(true);
        setIsPreloading(false);
        if (onPreloaded) {
          onPreloaded(video);
        }
        console.log('预加载完成:', video);
      });

      // 监听预加载错误
      preloadVideo.addEventListener('error', () => {
        setIsPreloading(false);
        console.error('预加载失败:', video);
      });

      // 开始预加载
      preloadVideoRef.current = preloadVideo;
      document.body.appendChild(preloadVideo);

      // 清理函数
      return () => {
        if (preloadVideo && preloadVideo.parentNode) {
          preloadVideo.parentNode.removeChild(preloadVideo);
        }
      };
    }
  }, [shouldPreload, isPreloaded, isPreloading, video, onPreloaded]);

  // 处理视频播放/暂停
  useEffect(() => {
    if (!videoRef.current || !showVideo) return;

    if (isActive && inView) {
      // 预加载视频
      videoRef.current.load();

      // 尝试播放视频
      const playPromise = videoRef.current.play();

      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            setIsPlaying(true);
          })
          .catch((error) => {
            console.error("播放失败:", error);
            setIsPlaying(false);
            // iOS设备可能需要用户交互才能播放
            if (error.name === "NotAllowedError") {
              console.log("需要用户交互才能播放视频");
            }
          });
      }
    } else {
      // 如果视频不在视口中，暂停播放
      videoRef.current.pause();
      setIsPlaying(false);
    }
  }, [isActive, inView, showVideo]);

  // 处理缩略图加载完成
  const handleThumbnailLoaded = () => {
    setThumbnailLoaded(true);
  };

  // 处理视频加载事件
  const handleVideoLoaded = () => {
    // 先设置视频为已加载
    setIsLoaded(true);
    setIsError(false);

    // 如果已经预加载过，加载速度会更快
    if (isPreloaded) {
      console.log('使用预加载的视频:', video);
    }

    // 延迟隐藏缩略图，确保视频已经完全显示
    // 这样可以避免闪烁，实现平滑过渡
    setTimeout(() => {
      setHideThumbnail(true);
    }, 300); // 300ms延迟，与CSS过渡时间匹配
  };

  // 处理视频错误
  const handleVideoError = () => {
    setIsError(true);
    console.error("视频加载失败:", video);
  };

  // 切换静音状态 - 添加到视频元素的点击事件中使用
  const toggleMute = (e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    if (!videoRef.current) return;

    videoRef.current.muted = !videoRef.current.muted;
    setIsMuted(videoRef.current.muted);
  };

  // 监听来自VideoInfoCard的静音切换事件
  useEffect(() => {
    const handleMuteToggle = (event: CustomEvent) => {
      if (videoRef.current) {
        videoRef.current.muted = event.detail.muted;
        setIsMuted(event.detail.muted);
      }
    };

    window.addEventListener('videoMuteToggle', handleMuteToggle as EventListener);

    return () => {
      window.removeEventListener('videoMuteToggle', handleMuteToggle as EventListener);
    };
  }, []);

  // 处理缩略图点击
  const handleThumbnailClick = () => {
    // 如果已经预加载了视频，可以立即显示
    if (isPreloaded) {
      console.log('使用预加载的视频显示:', video);
    }
    setShowVideo(true);
  };

  // 处理视频点击
  const handleVideoClick = () => {
    if (!videoRef.current) return;

    if (isPlaying) {
      videoRef.current.pause();
      setIsPlaying(false);
    } else {
      const playPromise = videoRef.current.play();
      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            setIsPlaying(true);
          })
          .catch((error) => {
            console.error("播放失败:", error);
          });
      }
    }
  };

  return (
    <div
      ref={ref}
      className="relative w-full h-full bg-black"
    >
      {/* PC端模糊背景层 - 仅在PC端显示 */}
      {isDesktop && thumbnailLoaded && (
        <div className="absolute inset-0 overflow-hidden">
          <img
            src={thumbnailUrl}
            alt="背景"
            className="w-full h-full object-cover blur-[40px] scale-110 opacity-60"
            style={{
              filter: 'blur(40px) brightness(0.7)',
              transform: 'scale(1.3)', // 进一步增加放大倍数以避免模糊边缘
            }}
          />
          {/* 渐变遮罩层，增强层次感 */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-black/30"></div>
        </div>
      )}

      {/* 视频播放器 - 响应式布局 */}
      <div className={isDesktop ? "absolute inset-0 flex items-center justify-center z-10" : "absolute inset-0"}>
        {/* 视频缩略图 - 响应式显示，PC端和移动端使用相同的定位方式 */}
        <div className={`absolute inset-0 overflow-hidden transition-opacity duration-500 ${
          (showVideo && hideThumbnail) ? 'opacity-0 pointer-events-none' : 'opacity-100'
        }`}>
          <img
            src={thumbnailUrl}
            alt="视频缩略图"
            className={`w-full h-full ${isDesktop ? 'object-contain' : 'object-cover'} transition-opacity duration-300 ${
              thumbnailLoaded ? 'opacity-100' : 'opacity-0'
            } ${shouldBlur ? 'blur-[8px]' : ''}`}
            onLoad={handleThumbnailLoaded}
            onClick={handleThumbnailClick}
          />
          {/* 播放按钮 - 只在缩略图显示且视频未加载时显示 */}
          {!showVideo && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="bg-black/40 rounded-full p-3 transition-transform duration-300 hover:scale-110">
                <Play size={30} className="text-white" />
              </div>
            </div>
          )}
        </div>

        {/* 加载指示器 */}
        {showVideo && !isLoaded && !isError && (
          <div className="absolute inset-0 flex items-center justify-center z-10">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
          </div>
        )}

        {/* 错误提示 */}
        {isError && (
          <div className="absolute inset-0 flex items-center justify-center z-10">
            <p className="text-white">视频加载失败</p>
          </div>
        )}

        {/* 实际视频 - 响应式显示，PC端和移动端使用相同的定位方式 */}
        {showVideo && (
          <div className="absolute inset-0 overflow-hidden">
            <video
              ref={videoRef}
              src={video}
              className={`w-full h-full ${isDesktop ? 'object-contain' : 'object-cover'} transition-opacity duration-500 ${shouldBlur ? 'blur-[8px]' : ''} ${isLoaded ? 'opacity-100' : 'opacity-0'}`}
              loop
              playsInline
              muted={isMuted}
              preload="auto"
              onClick={handleVideoClick}
              onLoadedData={handleVideoLoaded}
              onError={handleVideoError}
              onDoubleClick={(e) => toggleMute(e)}
              data-webkit-playsinline="true"
              data-x-webkit-airplay="allow"
              data-x5-video-player-type="h5-page"
              data-x5-video-player-fullscreen="false"
              data-x5-video-orientation="portraint"
              controlsList="nodownload nofullscreen"
              disablePictureInPicture
            />
          </div>
        )}

        {/* VIP提示遮罩层 - 只在非VIP用户且视频已加载时显示 */}
        {shouldBlur && (showVideo ? isLoaded : thumbnailLoaded) && (
          <div className="absolute inset-0 flex items-center justify-center z-20 pointer-events-none">
            <div
              className={`bg-black/20 backdrop-blur-md rounded-2xl p-4 sm:p-6 mx-4 max-w-sm text-center transition-all duration-300 pointer-events-auto ${
                isDesktop ? 'max-w-md' : 'max-w-xs'
              }`}
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.15)',
              }}
            >
              {/* 提示文字 */}
              <h3 className={`text-white font-bold mb-2 ${isDesktop ? 'text-lg' : 'text-base'}`}>
                开通VIP观看抖妮高清视频
              </h3>
              <p className={`text-white/90 mb-4 leading-relaxed ${isDesktop ? 'text-sm' : 'text-xs'}`}>
                抖妮视频仅尊贵VIP会员可以观看
                <br />
                开通VIP即可享受高清无码观看体验
              </p>

              {/* 开通VIP按钮 */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  router.push('/vip');
                }}
                className={`bg-gradient-to-r from-yellow-400 to-yellow-600 hover:from-yellow-500 hover:to-yellow-700 text-white font-bold rounded-xl transition-all duration-300 transform hover:scale-105 active:scale-95 shadow-md shadow-yellow-500/20 ${
                  isDesktop ? 'px-6 py-3 text-sm' : 'px-4 py-2 text-xs'
                }`}
              >
                立即开通VIP
              </button>
            </div>
          </div>
        )}

        {/* 视频信息卡片 - 显示女孩信息 */}
        <VideoInfoCard
          girl={girl}
          isActive={isActive}
          bottomNavHeight={bottomNavHeight}
          isDesktop={isDesktop}
          initialMuted={isMuted}
          onMuteChange={(muted) => {
            if (videoRef.current) {
              videoRef.current.muted = muted;
              setIsMuted(muted);
            }
          }}
        />
      </div>
    </div>
  );
}
