"use client";

import { motion } from "framer-motion";

export default function AuthSkeleton() {
  return (
    <div
      className="relative flex items-center justify-center h-[calc(100vh-4rem)] py-8 overflow-x-hidden"
      style={{
        background: 'linear-gradient(135deg, #121212 0%, #000000 100%)'
      }}
    >
      {/* 装饰性背景元素 - 模拟 */}
      <div className="absolute inset-0 overflow-hidden h-full w-full">
        <div 
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[30rem] h-[30rem] opacity-10" 
          style={{
            background: 'radial-gradient(circle at center, rgba(255,184,0,0.35) 0%, transparent 70%)'
          }}
        />
        <div className="absolute inset-0 bg-[url('/patterns/grid.png')] opacity-[0.02] bg-repeat" />
      </div>

      {/* 主内容区域骨架屏 */}
      <div className="relative w-full max-w-md px-4 z-10">
        <div
          className="rounded-2xl p-8 backdrop-blur-sm"
          style={{
            background: 'linear-gradient(135deg, rgba(30,30,30,0.7) 0%, rgba(15,15,15,0.6) 100%)',
            boxShadow: '0 10px 25px -5px rgba(0,0,0,0.3), 0 10px 10px -5px rgba(0,0,0,0.2), 0 0 0 1px rgba(255,255,255,0.05) inset'
          }}
        >
          {/* 标题与图标骨架 */}
          <div className="flex flex-col items-center justify-center space-y-4 mb-6">
            {/* 装饰性图标骨架 */}
            <div className="w-14 h-14 bg-gray-800 rounded-full animate-pulse" />
            
            {/* 标题骨架 */}
            <div className="h-10 w-48 bg-gray-800 rounded-md animate-pulse" />
            
            {/* 副标题骨架 */}
            <div className="h-5 w-40 bg-gray-800 rounded-md animate-pulse" />
          </div>
          
          {/* 表单骨架 */}
          <div className="space-y-4 mt-8">
            {/* 输入框骨架 x2 */}
            <div className="h-12 bg-gray-800 rounded-lg animate-pulse" />
            <div className="h-12 bg-gray-800 rounded-lg animate-pulse" />
            
            {/* 按钮骨架 */}
            <div className="h-12 mt-4 bg-gradient-to-r from-gray-700 to-gray-800 rounded-lg animate-pulse" />
            
            {/* 底部文字骨架 */}
            <div className="flex justify-center mt-4">
              <div className="h-5 w-52 bg-gray-800 rounded-md animate-pulse" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
