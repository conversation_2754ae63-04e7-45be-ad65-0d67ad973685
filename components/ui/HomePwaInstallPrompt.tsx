"use client";

import { useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import dynamic from "next/dynamic";

// 使用dynamic导入，避免SSR问题
const InstallPwaPrompt = dynamic(
  () => import("./InstallPwaPrompt"),
  { ssr: false }
);

// iOS特定安装提示
const IOSInstallPrompt = dynamic(
  () => import("./IOSInstallPrompt"),
  { ssr: false }
);

const HomePwaInstallPrompt = () => {
  const pathname = usePathname();
  const [isMounted, setIsMounted] = useState(false);
  
  // 确保组件只在客户端渲染
  useEffect(() => {
    setIsMounted(true);
  }, []);
  
  // 如果组件未挂载或当前路径不是主页，则不显示任何内容
  if (!isMounted || pathname !== "/") {
    return null;
  }
  
  // 只在主页显示PWA安装提示
  return (
    <>
      <InstallPwaPrompt />
      <IOSInstallPrompt />
    </>
  );
};

export default HomePwaInstallPrompt;
