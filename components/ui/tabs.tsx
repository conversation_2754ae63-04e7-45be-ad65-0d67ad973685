'use client';

import React from 'react';

interface TabsProps {
  value: string;
  onValueChange: (value: string) => void;
  children: React.ReactNode;
}

export const Tabs: React.FC<TabsProps> = ({ value, onValueChange, children }) => {
  return <div className="tabs w-full">{children}</div>;
};

interface TabsListProps {
  children: React.ReactNode;
}

export const TabsList: React.FC<TabsListProps> = ({ children }) => {
  return <div className="tabs-list flex space-x-2 mb-4 border-b">{children}</div>;
};

interface TabsTriggerProps {
  value: string;
  children: React.ReactNode;
  onValueChange: (value: string) => void;
}

export const TabsTrigger: React.FC<TabsTriggerProps> = ({ value, children, onValueChange }) => {
  return (
    <button
      className={`tab-trigger px-4 py-2 text-sm font-medium transition-colors duration-200
        ${value === 'selected' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
      onClick={() => onValueChange(value)}
    >
      {children}
    </button>
  );
};
