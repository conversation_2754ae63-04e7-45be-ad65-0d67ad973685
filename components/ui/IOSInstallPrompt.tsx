"use client";

import { useState, useEffect } from "react";
import { X, Share } from "lucide-react";

const IOSInstallPrompt = () => {
  const [showPrompt, setShowPrompt] = useState(false);

  useEffect(() => {
    // 检查localStorage，避免频繁提示
    const hasPromptBeenShown = localStorage.getItem("iosPwaPromptShown");
    const lastPromptDate = localStorage.getItem("iosPwaPromptDate");
    const currentDate = new Date().toDateString();

    // 如果在同一天已经显示过提示，则不再显示
    if (hasPromptBeenShown === "true" && lastPromptDate === currentDate) {
      return;
    }

    // 检测是否是iOS设备
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream;
    
    // 检测是否在独立模式运行（已安装为PWA）
    const isInStandaloneMode = window.matchMedia('(display-mode: standalone)').matches || 
                              (window.navigator as any).standalone || 
                              document.referrer.includes('android-app://');

    // 只有在iOS设备且不是已安装的PWA时显示提示
    if (isIOS && !isInStandaloneMode) {
      // 延迟显示提示，给用户一些时间浏览网站
      setTimeout(() => {
        setShowPrompt(true);
        localStorage.setItem("iosPwaPromptShown", "true");
        localStorage.setItem("iosPwaPromptDate", currentDate);
      }, 5000);
    }
  }, []);

  const handleClose = () => {
    setShowPrompt(false);
  };

  if (!showPrompt) {
    return null;
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 p-4 pb-20 z-[10000] bg-background/95 backdrop-blur-sm border-t border-muted animate-slide-up">
      <div className="flex items-start justify-between">
        <div className="flex-1 mr-4">
          <h3 className="text-lg font-medium text-[#e50049]">添加佳妮俱乐部到主屏幕</h3>
          <p className="text-sm text-muted-foreground mt-1">获得最佳体验，无需打开浏览器即可访问</p>
        </div>
        <button
          onClick={handleClose}
          className="p-1 rounded-full hover:bg-muted transition-colors"
          aria-label="关闭"
        >
          <X className="h-5 w-5 text-muted-foreground" />
        </button>
      </div>
      
      <div className="mt-4 space-y-4">
        <div className="bg-muted/30 p-3 rounded-lg">
          <ol className="list-decimal pl-5 space-y-2 text-sm">
            <li>点击下方的<Share className="inline h-4 w-4 mx-1 text-[#e50049]" />分享按钮</li>
            <li>在弹出的菜单中，滚动并选择<span className="font-semibold">&#34;添加到主屏幕&#34;</span>选项</li>
            <li>点击<span className="font-semibold">&#34;添加&#34;</span>确认安装</li>
          </ol>
        </div>
        
        <div className="flex justify-center">
          <div className="flex items-center justify-center bg-muted/50 p-2 px-4 rounded-full animate-pulse">
            <Share className="h-5 w-5 mr-2 text-[#e50049]" />
            <span className="text-sm">点击分享按钮，然后选择&#34;添加到主屏幕&#34;</span>
          </div>
        </div>
        
        <button
          onClick={handleClose}
          className="w-full py-2 px-4 border border-muted hover:border-[#e50049] text-foreground font-medium rounded-lg transition-all duration-300"
        >
          我知道了
        </button>
      </div>
    </div>
  );
};

export default IOSInstallPrompt; 