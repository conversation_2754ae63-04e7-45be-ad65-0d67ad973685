"use client";

import { useState, useEffect } from "react";
import { X } from "lucide-react";

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: "accepted" | "dismissed";
    platform: string;
  }>;
  prompt(): Promise<void>;
}

const InstallPwaPrompt = () => {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [showPrompt, setShowPrompt] = useState(false);

  useEffect(() => {
    // 检测是否是iOS设备
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream;
    
    // 如果是iOS设备，不显示此提示
    if (isIOS) {
      return;
    }
    
    // 初始化时检查localStorage，避免频繁提示
    const hasPromptBeenShown = localStorage.getItem("pwaPromptShown");
    const lastPromptDate = localStorage.getItem("pwaPromptDate");
    const currentDate = new Date().toDateString();

    // 如果已经安装或者在同一天已经显示过提示，则不再显示
    if (hasPromptBeenShown === "true" && lastPromptDate === currentDate) {
      return;
    }

    const handler = (e: Event) => {
      // 阻止Chrome 76+自动显示安装提示
      e.preventDefault();
      // 保存事件，稍后使用
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      // 显示自定义安装提示
      setShowPrompt(true);
      // 记录提示已显示
      localStorage.setItem("pwaPromptShown", "true");
      localStorage.setItem("pwaPromptDate", currentDate);
    };

    // 添加事件监听
    window.addEventListener("beforeinstallprompt", handler);

    return () => {
      window.removeEventListener("beforeinstallprompt", handler);
    };
  }, []);

  const handleInstallClick = () => {
    if (!deferredPrompt) {
      return;
    }

    // 显示安装提示
    deferredPrompt.prompt();

    // 等待用户响应
    deferredPrompt.userChoice.then((choiceResult) => {
      if (choiceResult.outcome === "accepted") {
        console.log("用户接受了安装");
      } else {
        console.log("用户取消了安装");
      }
      // 重置延迟提示变量
      setDeferredPrompt(null);
    });

    // 隐藏我们的UI
    setShowPrompt(false);
  };

  const handleClose = () => {
    setShowPrompt(false);
  };

  if (!showPrompt) {
    return null;
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 p-4 pb-20 z-[10000] bg-background/95 backdrop-blur-sm border-t border-muted animate-slide-up">
      <div className="flex items-start justify-between">
        <div className="flex-1 mr-4">
          <h3 className="text-lg font-medium text-[#e50049]">添加佳妮俱乐部到主屏幕</h3>
          <p className="text-sm text-muted-foreground mt-1">安装我们的应用获得更好的体验，无需打开浏览器即可访问</p>
        </div>
        <button
          onClick={handleClose}
          className="p-1 rounded-full hover:bg-muted transition-colors"
          aria-label="关闭"
        >
          <X className="h-5 w-5 text-muted-foreground" />
        </button>
      </div>
      <div className="mt-4 flex space-x-3">
        <button
          onClick={handleInstallClick}
          className="flex-1 py-2 px-4 bg-[#e50049] hover:bg-[#c8003f] text-white font-medium rounded-lg transition-all duration-300"
        >
          立即安装
        </button>
        <button
          onClick={handleClose}
          className="py-2 px-4 border border-muted hover:border-[#e50049] text-foreground font-medium rounded-lg transition-all duration-300"
        >
          稍后再说
        </button>
      </div>
    </div>
  );
};

export default InstallPwaPrompt; 