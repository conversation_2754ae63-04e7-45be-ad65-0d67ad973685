import { SelectItem } from "./select";

interface SelectItemWrapperProps {
    children: React.ReactNode;
    key?: string;
    value: string;
}

const SelectItemWrapper: React.FC<SelectItemWrapperProps> = ({ children, value }) => {
    // Ensure the value is never an empty string
    const safeValue = value?.trim() || "_empty";

    return (
        <SelectItem value={safeValue}>
            {children}
        </SelectItem>
    );
};

export default SelectItemWrapper;
