"use client";

import React, { useState } from "react";
import { MessageCircle } from "lucide-react";
import { Button } from "./button";
import { cn } from "../../lib/utils";
import { motion } from "framer-motion";
import { show } from "@intercom/messenger-js-sdk";

interface ChatBubbleProps {
  className?: string;
  isAuthenticated?: boolean;
}

const ChatBubble: React.FC<ChatBubbleProps> = ({ className }) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    show(); // 调用intercom的show方法
  };

  return (
    <motion.div
      initial={{ scale: 0, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ type: "spring", stiffness: 260, damping: 20 }}
      className="fixed bottom-6 right-6 z-[9999]" // 高z-index确保按钮在最上层
    >
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className={cn(
          "relative group flex items-center gap-2 px-4 py-3 rounded-full shadow-2xl transition-all duration-500",
          "bg-gradient-to-r from-[#00C851] to-[#07C160]", // 绿色渐变
          "hover:from-[#00B347] hover:to-[#06B055]", // 悬停时的深绿色渐变
          "border border-white/20 backdrop-blur-sm",
          "hover:shadow-[0_20px_40px_rgba(7,193,96,0.4)]", // 绿色发光阴影
          className
        )}
        onClick={handleClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* 背景光效 */}
        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-[#00C851]/20 to-[#07C160]/20 blur-xl group-hover:blur-2xl transition-all duration-500" />

        {/* 图标 */}
        <MessageCircle className="w-5 h-5 text-white relative z-10" />

        {/* 客服文字 */}
        <span className="text-white font-medium text-sm relative z-10 tracking-wide">
          客服
        </span>

        {/* 悬停提示 */}
        {isHovered && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            className="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-black/90 text-white px-3 py-2 rounded-lg text-xs whitespace-nowrap backdrop-blur-sm border border-white/10"
          >
            <div className="relative">
              在线客服咨询
              {/* 小箭头 */}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black/90" />
            </div>
          </motion.div>
        )}
      </motion.button>
    </motion.div>
  );
};

export { ChatBubble }; 