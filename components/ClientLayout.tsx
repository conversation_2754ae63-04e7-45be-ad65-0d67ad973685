"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { signOut } from "next-auth/react";
import dynamic from "next/dynamic";
import { generateHash } from "../lib/auth";
import Navbar from "./Navbar";
import { useSidebarContext } from "../contexts/SidebarContext";
import { ChatBubble } from "./ui/ChatBubble";

// 移除不必要的动态导入声明，直接在useEffect中动态导入

export default function ClientLayout({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession();
  const [isMobile, setIsMobile] = useState(false);
  const [user, setUser] = useState<{ username: string; avatar: string; isVIP: boolean } | null>(null);
  const { isSidebarOpen, setIsSidebarOpen } = useSidebarContext();
  const router = useRouter();
  const [cities, setCities] = useState<string[]>([]);
  const [cityDataCounts, setCityDataCounts] = useState<{ [city: string]: number }>({});
  const [intercomLoaded, setIntercomLoaded] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // 动态加载和初始化Intercom
  useEffect(() => {
    if (status === "authenticated" && session?.user && !intercomLoaded) {
      setUser({
        username: session.user.username || session.user.email || "User",
        avatar: session.user.image || "/assets/default-avatar.png",
        isVIP: session.user.isVIP,
      });

      // 动态导入并初始化Intercom
      import("@intercom/messenger-js-sdk").then((IntercomModule) => {
        const { default: Intercom } = IntercomModule;
        const userHash = generateHash(session.user.id);

        Intercom({
          app_id: "e55i68ln",
          user_id: session.user.id,
          name: session.user.username,
          user_hash: userHash,
        });
        
        setIntercomLoaded(true);
      }).catch((error) => {
        console.error("Failed to load Intercom:", error);
      });
    } else if (status !== "authenticated" && intercomLoaded) {
      console.log("ClientLayout: User not authenticated");
      setUser(null);
      // 动态导入shutdown函数
      import("@intercom/messenger-js-sdk").then((IntercomModule) => {
        const { shutdown } = IntercomModule;
        shutdown();
        setIntercomLoaded(false);
      }).catch((error) => {
        console.error("Failed to shutdown Intercom:", error);
      });
    }
  }, [session, status, intercomLoaded]);

  return (
    <div className="flex flex-col min-h-screen">
      {!isMobile && (
        <Navbar
          user={user}
          status={status}
          isMobile={isMobile}
          isSidebarOpen={isSidebarOpen}
          setIsSidebarOpen={setIsSidebarOpen}
        />
      )}
      <div className="flex-1 flex relative">
        <main className="flex-1">
          {children}
        </main>
      </div>
      
      {/* 聊天气泡 - 仅在PC端、登录后显示 */}
      {!isMobile && status === "authenticated" && <ChatBubble isAuthenticated={true} />}
    </div>
  );
}
