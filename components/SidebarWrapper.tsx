'use client';

import { useEffect, useState } from "react";
import { getCityCounts } from "../lib/api";
import Sidebar from "./Sidebar";

// 设置页面重新验证时间为60秒
export const revalidate = 60;

interface SidebarWrapperProps {
  isMobile: boolean;
  isOpen: boolean;
  onClose: () => void;
  isAuthenticated: boolean;
}

export default function SidebarWrapper({
  isMobile,
  isOpen,
  onClose,
  isAuthenticated
}: SidebarWrapperProps) {
  const [cityDataCounts, setCityDataCounts] = useState<{ [city: string]: number }>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await getCityCounts();
        setCityDataCounts(data);
      } catch (error) {
        console.error("Failed to fetch city counts:", error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);

  return (
    <Sidebar
      cityDataCounts={cityDataCounts}
      loading={loading}
      isMobile={isMobile}
      isOpen={isOpen}
      onClose={onClose}
      isAuthenticated={isAuthenticated}
    />
  );
} 