"use client";

import React, { useState, useEffect } from "react";
import Sidebar from "./Sidebar";
import { getCities, getCityCounts } from "../lib/api";
import { useSession } from "next-auth/react";

interface CityLayoutClientProps {
  children: React.ReactNode;
  isSidebarOpen: boolean;
  setIsSidebarOpen: (isOpen: boolean) => void;
}

export default function CityLayoutClient({ 
  children, 
  isSidebarOpen, 
  setIsSidebarOpen 
}: CityLayoutClientProps) {
  const [cities, setCities] = useState<string[]>([]);
  const [cityDataCounts, setCityDataCounts] = useState<{ [city: string]: number }>({});
  const [loading, setLoading] = useState(true);
  const { data: session } = useSession();

  // 在PC端自动打开侧边栏
  useEffect(() => {
    const checkDevice = () => {
      const isPc = window.innerWidth > 768;
      if (isPc && !isSidebarOpen) {
        setIsSidebarOpen(true);
      }
    };

    // 初始检测
    checkDevice();

    // 监听窗口大小变化
    window.addEventListener("resize", checkDevice);
    return () => window.removeEventListener("resize", checkDevice);
  }, [setIsSidebarOpen, isSidebarOpen]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [fetchedCities, fetchedCityCounts] = await Promise.all([
          getCities(),
          getCityCounts()
        ]);
        setCities(fetchedCities);
        setCityDataCounts(fetchedCityCounts);
      } catch (err) {
        console.error("Failed to fetch data:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <div className="relative flex-grow flex flex-col md:flex-row overflow-hidden bg-dark-background">
      {/* 只在PC端显示固定侧边栏 */}
      <div className="hidden md:block md:flex-shrink-0">
        <Sidebar
          cities={cities}
          cityDataCounts={cityDataCounts}
          loading={loading}
          isMobile={false}
          isOpen={true}
          onClose={() => setIsSidebarOpen(false)}
          isAuthenticated={true}
          isVIP={session?.user?.isVIP}
        />
      </div>

      <div className="flex-1 overflow-auto min-w-0">
        {children}
      </div>
    </div>
  );
}
