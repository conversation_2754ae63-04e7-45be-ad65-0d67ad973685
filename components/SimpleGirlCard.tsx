"use client";

import React, { useState, useRef, useEffect } from "react";
import { Card, CardContent } from "./ui/card";
import { AspectRatio } from "./ui/aspect-ratio";
import { Girl } from "../types/girl";
import { Play, X } from "lucide-react";

interface SimpleGirlInfoProps {
  data: Girl;
}

export const SimpleGirlInfo: React.FC<SimpleGirlInfoProps> = ({ data }) => {
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  const [videoReady, setVideoReady] = useState<Record<number, boolean>>({});
  const videoRefs = useRef<Record<number, HTMLVideoElement>>({});
  
  // 简单判断媒体类型是否为视频
  const isVideo = (url: string) => {
    return url.includes('.mp4') || url.includes('.mov') || url.includes('.webm');
  };
  
  // 处理媒体预览点击
  const handleMediaClick = (index: number) => {
    setSelectedImageIndex(index);
  };
  
  // 关闭媒体预览
  const handleClosePreview = () => {
    setSelectedImageIndex(null);
  };
  
  // 设置视频引用并处理视频加载
  const setVideoRef = (element: HTMLVideoElement | null, index: number) => {
    if (!element) return;
    
    videoRefs.current[index] = element;
    
    // 简洁地解决视频预览问题
    if (!videoReady[index]) {
      // 设置视频只读取元数据以获取第一帧作为预览
      element.addEventListener('loadedmetadata', function() {
        try {
          // 尝试获取视频第一帧
          element.currentTime = 0.1;
        } catch (e) {
          console.error('无法设置视频当前时间:', e);
        }
      });
      
      // 当时间更新后，标记视频已准备好显示第一帧
      element.addEventListener('timeupdate', function onTimeUpdate() {
        if (element.currentTime > 0) {
          setVideoReady(prev => ({ ...prev, [index]: true }));
          element.removeEventListener('timeupdate', onTimeUpdate);
        }
      });
    }
  };
  
  return (
    <Card className="h-full bg-dark-secondary/60 backdrop-blur-sm border-dark-accent hover:shadow-md transition-shadow overflow-hidden">
      {/* 媒体展示区域 - 网格布局展示所有媒体 */}
      {data.media && data.media.length > 0 && (
        <div className="p-2 bg-black/20">
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
            {data.media.map((mediaUrl, index) => (
              <div 
                key={index} 
                className="aspect-square cursor-pointer relative overflow-hidden rounded-md"
                onClick={() => handleMediaClick(index)}
              >
                {isVideo(mediaUrl) ? (
                  <div className="relative w-full h-full bg-black/20">
                    <video 
                      ref={(el) => setVideoRef(el, index)}
                      src={mediaUrl} 
                      className="w-full h-full object-cover" 
                      muted 
                      playsInline
                      preload="metadata"
                    />
                    <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                      <Play className="text-white opacity-80" size={32} />
                    </div>
                    <div className="absolute bottom-0 right-0 bg-black/60 text-white text-xs px-1 py-0.5 m-1 rounded">
                      视频
                    </div>
                  </div>
                ) : (
                  <img 
                    src={mediaUrl} 
                    alt={`${data.title}-${index}`} 
                    className="w-full h-full object-cover" 
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      )}
      
      <CardContent className="p-4">
        <div className="space-y-2">
          <h3 className="text-lg font-semibold text-primary truncate">{data.title}</h3>
          
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="flex flex-col">
              <span className="text-muted-foreground">城市:</span>
              <span>{data.city}</span>
            </div>
            
            {data.district && (
              <div className="flex flex-col">
                <span className="text-muted-foreground">区域:</span>
                <span>{data.district}</span>
              </div>
            )}
            
            <div className="flex flex-col">
              <span className="text-muted-foreground">价格:</span>
              <span>{data.price}</span>
            </div>
            
            {data.age && (
              <div className="flex flex-col">
                <span className="text-muted-foreground">年龄:</span>
                <span>{data.age}</span>
              </div>
            )}
            
            {data.height && (
              <div className="flex flex-col">
                <span className="text-muted-foreground">身高:</span>
                <span>{data.height}</span>
              </div>
            )}
            
            {data.cup && (
              <div className="flex flex-col">
                <span className="text-muted-foreground">罩杯:</span>
                <span>{data.cup}</span>
              </div>
            )}
          </div>

          {data.type && data.type.length > 0 && (
            <div className="mt-2">
              <span className="text-muted-foreground text-sm">类型:</span>
              <div className="flex flex-wrap gap-1 mt-1">
                {data.type.map((type, index) => (
                  <span 
                    key={index} 
                    className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-md"
                  >
                    {type}
                  </span>
                ))}
              </div>
            </div>
          )}

          {data.service && data.service.length > 0 && (
            <div className="mt-2">
              <span className="text-muted-foreground text-sm">服务:</span>
              <div className="flex flex-wrap gap-1 mt-1">
                {data.service.map((service, index) => (
                  <span 
                    key={index} 
                    className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-md"
                  >
                    {service}
                  </span>
                ))}
              </div>
            </div>
          )}

          {data.feature && (
            <div className="mt-2">
              <span className="text-muted-foreground text-sm">特点:</span>
              <p className="text-sm mt-1">{data.feature}</p>
            </div>
          )}
        </div>
      </CardContent>
      
      {/* 媒体预览模态框 */}
      {selectedImageIndex !== null && (
        <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center" onClick={handleClosePreview}>
          <div className="relative max-w-4xl max-h-[90vh] w-full">
            {isVideo(data.media[selectedImageIndex]) ? (
              <video 
                src={data.media[selectedImageIndex]} 
                className="w-full h-full object-contain" 
                controls 
                autoPlay 
                onClick={(e) => e.stopPropagation()}
              />
            ) : (
              <img 
                src={data.media[selectedImageIndex]} 
                alt={data.title} 
                className="w-full h-full object-contain" 
                onClick={(e) => e.stopPropagation()}
              />
            )}
            <button 
              className="absolute top-4 right-4 bg-black/50 text-white rounded-full p-2"
              onClick={handleClosePreview}
            >
              <X size={20} />
            </button>
          </div>
        </div>
      )}
    </Card>
  );
};

export default SimpleGirlInfo;
