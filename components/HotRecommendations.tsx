"use client";

import React, { useEffect, useState } from 'react';
import { BarChart3, ExternalLink, Star } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

type GirlData = {
  id: string;
  girl_id: string;
  title: string;
  city: string;
  district?: string;
  media: string[];
  overallRating?: number;
  updatedAt: string;
};

type HotRecommendationsProps = {
  variant: 'mobile' | 'desktop';
  itemCount?: number;
  showViewAll?: boolean;
};

export function HotRecommendations({ 
  variant = 'desktop', 
  itemCount = 6,
  showViewAll = variant === 'desktop'
}: HotRecommendationsProps) {
  const [girlData, setGirlData] = useState<GirlData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    const fetchTopRatedGirls = async () => {
      try {
        setIsLoading(true);
        // 获取评分不为空的女孩数据，并且是3天前的数据
        const response = await fetch('/api/girls?page=1&perPage=' + itemCount + '&hasRating=true&beforeDays=true');
        const data = await response.json();
        
        if (data.girls && Array.isArray(data.girls)) {
          setGirlData(data.girls);
        }
      } catch (error) {
        console.error('获取热门女孩数据失败:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTopRatedGirls();
  }, [itemCount]);
  
  // 渲染评分星星
  const renderRatingStars = (rating: number | undefined) => {
    if (!rating) return null;
    
    const starRating = (rating / 2);
    const fullStars = Math.floor(starRating);
    const hasHalfStar = starRating % 1 >= 0.5;
    
    return (
      <div className="flex items-center gap-0.5">
        {Array.from({ length: fullStars }).map((_, i) => (
          <Star key={`full-${i}`} className="w-3 h-3 fill-yellow-400 text-yellow-400" />
        ))}
        {hasHalfStar && (
          <div className="relative w-3 h-3">
            <Star className="absolute w-3 h-3 text-yellow-400" />
            <div className="absolute top-0 left-0 w-1.5 h-3 overflow-hidden">
              <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
            </div>
          </div>
        )}
        {Array.from({ length: 5 - fullStars - (hasHalfStar ? 1 : 0) }).map((_, i) => (
          <Star key={`empty-${i}`} className="w-3 h-3 text-yellow-400" />
        ))}
        <span className="text-xs ml-1 text-yellow-400">
          {rating.toFixed(1)}
        </span>
      </div>
    );
  };
  return (
    <section className="w-full">
      <div className="flex justify-between items-center mb-3 md:mb-4">
        <h2 className="text-lg font-medium flex items-center gap-2">
          <span className={`${variant === 'mobile' ? 'text-lg' : 'text-xl'} mr-1`}>🔥</span>
          热门推荐
        </h2>
      </div>
      
      {isLoading ? (
        <div className={`grid grid-cols-2 gap-3 md:gap-4 ${variant === 'desktop' ? 'md:grid-cols-4 lg:grid-cols-6' : ''}`}>
          {Array.from({ length: itemCount }).map((_, index) => (
            <div key={index} className="rounded-xl bg-[#1a1a1a] border border-[#292929] animate-pulse">
              <div className="aspect-square bg-[#151515]"></div>
              <div className="p-3">
                <div className="h-4 bg-[#222222] rounded mb-2"></div>
                <div className="h-3 bg-[#222222] rounded w-3/4"></div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className={`grid grid-cols-2 gap-3 md:gap-4 ${variant === 'desktop' ? 'md:grid-cols-4 lg:grid-cols-6' : ''}`}>
          {girlData.map((girl) => (
            <Link 
              href={`/girl/${girl.girl_id}`} 
              key={girl.id} 
              className={`relative group overflow-hidden rounded-xl bg-[#1a1a1a] border border-[#292929] transition-all duration-300 ${
                variant === 'desktop' ? 'hover:-translate-y-1 hover:shadow-lg hover:shadow-[#e50049]/10 hover:border-[#e50049]/30' : ''
              }`}
            >
              {/* 背景效果 */}
              <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/5 group-hover:opacity-80 transition-opacity duration-300"></div>
              
              <div className="aspect-square bg-[#151515] relative overflow-hidden">
                {girl.media && girl.media.length > 0 ? (
                  <Image 
                    src={girl.media[0]} 
                    alt={girl.title} 
                    fill 
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                ) : (
                  <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,rgba(229,0,73,0.1),rgba(229,0,73,0)_70%)]}"></div>
                )}
              </div>
              
              <div className="p-3 relative z-10">
                <h3 className="font-medium text-sm mb-1 line-clamp-1">{girl.title}</h3>
                <div className="flex justify-between items-center">
                  <div className="text-xs text-gray-400">
                    {girl.city}{girl.district ? ` · ${girl.district}` : ''}
                  </div>
                  {girl.overallRating && renderRatingStars(girl.overallRating)}
                </div>
              </div>
            </Link>
          ))}
        </div>
      )}
    </section>
  );
}
