"use client";

import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import RegisterForm from "./RegisterForm";
import { motion } from "framer-motion";
import Image from "next/image";
import AuthSkeleton from "./AuthSkeleton";

interface RegisterClientProps {
	children?: React.ReactNode;
}

export default function RegisterClient({ children }: RegisterClientProps) {
	const { data: session, status } = useSession();
	const router = useRouter();

	useEffect(() => {
		if (status === "authenticated") {
			router.push("/city");
		}
	}, [status, router]);

	if (status === "loading") {
		return <AuthSkeleton />;
	}

	if (status === "authenticated") {
		return null;
	}

	return (
		<div
			className="relative flex items-center justify-center h-full py-8 overflow-x-hidden"
			style={{
				background: 'linear-gradient(135deg, #121212 0%, #000000 100%)'
			}}
		>
			{/* 装饰性背景元素 */}
			<div className="absolute inset-0 overflow-hidden h-full w-full">
				{/* 主光效 */}
				<div 
					className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[30rem] h-[30rem] opacity-20" 
					style={{
						background: 'radial-gradient(circle at center, rgba(255,184,0,0.35) 0%, transparent 70%)'
					}}
				/>
				
				{/* 右侧装饰光点 */}
				<motion.div 
					className="absolute left-0 top-0 w-64 h-64 rounded-full opacity-20 blur-3xl"
					style={{ background: 'radial-gradient(circle at center, #FFB800 0%, transparent 70%)' }}
					animate={{
						scale: [1, 1.2, 1],
						opacity: [0.15, 0.2, 0.15],
					}}
					transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
				/>
				
				{/* 右侧装饰图案 */}
				<motion.div 
					className="absolute right-0 top-1/3 w-40 h-40 opacity-10 blur-2xl"
					style={{ background: 'radial-gradient(circle at center, #E11D48 0%, transparent 70%)' }}
					animate={{
						scale: [1, 1.1, 1],
						opacity: [0.1, 0.15, 0.1],
					}}
					transition={{ duration: 5, repeat: Infinity, ease: "easeInOut", delay: 1 }}
				/>
				
				{/* 底部装饰光效 */}
				<motion.div 
					className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-full h-[10rem] opacity-[0.07] blur-3xl"
					style={{ background: 'linear-gradient(to top, rgba(255,184,0,0.3), transparent)' }}
					animate={{
						opacity: [0.07, 0.05, 0.07],
						y: [0, 10, 0]
					}}
					transition={{ duration: 10, repeat: Infinity, ease: "easeInOut" }}
				/>
				
				{/* 网格背景 - 提供精致的纹理 */}
				<div className="absolute inset-0 bg-[url('/patterns/grid.png')] opacity-[0.03] bg-repeat" />
			</div>

			{/* 主内容区域 */}
			<div className="relative w-full max-w-md px-4 z-10">
				<div
					className="rounded-2xl p-8 backdrop-blur-sm"
					style={{
						background: 'linear-gradient(135deg, rgba(30,30,30,0.9) 0%, rgba(15,15,15,0.8) 100%)',
						boxShadow: '0 10px 25px -5px rgba(0,0,0,0.3), 0 10px 10px -5px rgba(0,0,0,0.2), 0 0 0 1px rgba(255,255,255,0.05) inset'
					}}
				>
					{/* 标题与装饰图标 */}
					<div className="text-center space-y-3 mb-6">
						{/* 装饰性图标 */}
						<div className="flex justify-center mb-1">
							<div className="w-14 h-14 bg-gradient-to-br from-[#FFB800] to-[#FF8A00] rounded-full flex items-center justify-center shadow-lg shadow-amber-900/20 p-0.5">
								<div className="w-full h-full bg-black rounded-full flex items-center justify-center">
									<Image 
										src="/icons/icon-96x96.png" 
										alt="俱乐部图标" 
										width={42}
										height={42}
										className="rounded-full" 
									/>
								</div>
							</div>
						</div>
						<h2 className="text-3xl sm:text-4xl font-extrabold bg-gradient-to-r from-[#FFB800] to-[#FF8A00] bg-clip-text text-transparent">
							加入俱乐部
						</h2>
						<p className="text-[#999999] text-sm sm:text-base">开启你的专属约会之旅</p>
					</div>
					
					{/* 注册表单 */}
					<div>
						<RegisterForm />
					</div>
				</div>
			</div>
		</div>
	);
}
