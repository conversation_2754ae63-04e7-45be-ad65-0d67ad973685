"use client";

import React from 'react';
import Link from 'next/link';
import { ExternalLink, Star, BarChart3, Sparkles, ChevronRight } from 'lucide-react';

// 资源数据
const resources = [
  {
    name: '至真园™',
    description: '真实会员反馈',
    url: '/feedback',
    icon: <Sparkles className="w-5 h-5 text-[#e50049]" />,
    gradient: 'from-purple-700/60 to-indigo-700/60',
    character: '🦋',
    tag: '推荐'
  },
  {
    name: '⁵ꪝ极品营',
    description: '顶级5KW资源',
    url: '/jianier5kw',
    icon: <Star className="w-5 h-5 text-[#e50049]" />,
    gradient: 'from-violet-700/60 to-blue-700/60',
    character: '💎',
    tag: '热门'
  },
  {
    name: '博客园',
    description: '知识分享交流',
    url: '/blog',
    icon: <BarChart3 className="w-5 h-5 text-[#e50049]" />,
    gradient: 'from-indigo-800/60 to-blue-600/60',
    character: '📒',
    tag: '精选'
  },
  {
    name: '约会指南',
    description: '流程与服务说明',
    url: '/guide',
    icon: <ChevronRight className="w-5 h-5 text-[#e50049]" />,
    gradient: 'from-blue-700/60 to-indigo-600/60',
    character: '📝',
    tag: '必读'
  }
];

interface ResourceCardsProps {
  variant?: 'mobile' | 'desktop';
}

export function ResourceCards({ variant = 'desktop' }: ResourceCardsProps) {
  const isMobile = variant === 'mobile';

  return (
    <>
      {/* 标题栏 */}
      <div className={`flex justify-between items-center ${isMobile ? 'mb-3' : 'mb-4'}`}>
        <h2 className="text-lg font-medium flex items-center gap-2">
          <span className={`${isMobile ? 'text-lg' : 'text-xl'} mr-1`}>✨</span>
          极品资源
        </h2>
      </div>

      {/* 卡片网格 - 统一使用移动端样式 */}
      <div className={`grid ${isMobile ? 'grid-cols-2 gap-3' : 'grid-cols-4 gap-4'} w-full`}>
        {resources.map((resource) => {
          const cardContent = (
            <div className={`relative overflow-hidden rounded-xl bg-[#2a2a2a] border border-[#404040] h-full ${isMobile ? 'min-h-[100px]' : 'min-h-[160px]'}`}
                 style={{
                   transform: 'translateZ(0)', /* 强制GPU渲染 */
                   transition: 'transform 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease',
                   willChange: 'transform'
                 }}
                 onMouseEnter={(e) => {
                   // 主卡片动画
                   e.currentTarget.style.transform = 'translateY(-4px) translateZ(0)';
                   e.currentTarget.style.boxShadow = '0 10px 25px -5px rgba(229, 0, 73, 0.1)';
                   e.currentTarget.style.borderColor = 'rgba(229, 0, 73, 0.3)';

                   // 字符动画 - 使用正确的TypeScript类型转换
                   const charElement = e.currentTarget.querySelector(`.character-${resource.name}`) as HTMLElement;
                   if (charElement) {
                     charElement.style.transform = 'translate(-50%, -50%) rotate(-12deg) scale(1.05)';
                     charElement.style.opacity = '0.95';
                   }

                   // 高光层动画
                   const highlightElement = e.currentTarget.querySelector(`#highlight-${resource.name}`) as HTMLElement;
                   if (highlightElement) {
                     highlightElement.style.opacity = '1';
                   }

                   // 渐变叠加层动画
                   const overlayElement = e.currentTarget.querySelector(`#overlay-${resource.name}`) as HTMLElement;
                   if (overlayElement) {
                     overlayElement.style.opacity = '0.2';
                   }

                   // 名称文本动画
                   const titleElement = e.currentTarget.querySelector(`.title-${resource.name}`) as HTMLElement;
                   if (titleElement) {
                     titleElement.style.color = '#e50049';
                     titleElement.style.transform = 'translateX(4px)';
                   }

                   // 描述文本动画
                   const descElement = e.currentTarget.querySelector(`.desc-${resource.name}`) as HTMLElement;
                   if (descElement) {
                     descElement.style.color = 'rgba(255, 255, 255, 0.95)';
                     descElement.style.transform = 'translateX(4px)';
                   }

                   // 图标动画
                   const iconElement = e.currentTarget.querySelector(`.icon-${resource.name}`) as HTMLElement;
                   if (iconElement) {
                     iconElement.style.transform = 'scale(1.1)';
                     iconElement.style.color = '#e50049';
                   }

                   // 标签动画
                   const tagElement = e.currentTarget.querySelector(`.tag-${resource.name}`) as HTMLElement;
                   if (tagElement) {
                     tagElement.style.backgroundColor = 'rgba(229, 0, 73, 0.3)';
                   }
                 }}
                 onMouseLeave={(e) => {
                   // 主卡片动画复原
                   e.currentTarget.style.transform = 'translateZ(0)';
                   e.currentTarget.style.boxShadow = 'none';
                   e.currentTarget.style.borderColor = 'rgba(64, 64, 64, 1)';

                   // 字符动画复原 - 使用正确的TypeScript类型转换
                   const charElement = e.currentTarget.querySelector(`.character-${resource.name}`) as HTMLElement;
                   if (charElement) {
                     charElement.style.transform = 'translate(-50%, -50%) rotate(-12deg) scale(1)';
                     charElement.style.opacity = '0.9';
                   }

                   // 高光层动画复原
                   const highlightElement = e.currentTarget.querySelector(`#highlight-${resource.name}`) as HTMLElement;
                   if (highlightElement) {
                     highlightElement.style.opacity = '0';
                   }

                   // 渐变叠加层动画复原
                   const overlayElement = e.currentTarget.querySelector(`#overlay-${resource.name}`) as HTMLElement;
                   if (overlayElement) {
                     overlayElement.style.opacity = '0.4';
                   }

                   // 名称文本动画复原
                   const titleElement = e.currentTarget.querySelector(`.title-${resource.name}`) as HTMLElement;
                   if (titleElement) {
                     titleElement.style.color = 'white';
                     titleElement.style.transform = 'translateX(0)';
                   }

                   // 描述文本动画复原
                   const descElement = e.currentTarget.querySelector(`.desc-${resource.name}`) as HTMLElement;
                   if (descElement) {
                     descElement.style.color = isMobile ? 'rgba(229, 229, 229, 1)' : 'rgba(255, 255, 255, 0.8)';
                     descElement.style.transform = 'translateX(0)';
                   }

                   // 图标动画复原
                   const iconElement = e.currentTarget.querySelector(`.icon-${resource.name}`) as HTMLElement;
                   if (iconElement) {
                     iconElement.style.transform = 'scale(1)';
                     iconElement.style.color = '';
                   }

                   // 标签动画复原
                   const tagElement = e.currentTarget.querySelector(`.tag-${resource.name}`) as HTMLElement;
                   if (tagElement) {
                     tagElement.style.backgroundColor = 'rgba(229, 0, 73, 0.2)';
                   }
                 }}>
              {/* 背景渐变层 */}
              <div className={`absolute inset-0 bg-gradient-to-br ${resource.gradient}`} style={{ backfaceVisibility: 'hidden' }}>
                {/* 柔和纹理 - 固定定位 */}
                <div className="absolute inset-0 w-full h-full bg-[radial-gradient(ellipse_at_center,rgba(255,255,255,0.07),transparent_80%)]"
                     style={{ transform: 'translateZ(0)', backfaceVisibility: 'hidden' }}></div>

                {/* 光晕效果 - 固定定位 */}
                <div className="absolute inset-0 w-full h-full bg-[radial-gradient(circle_at_30%_30%,rgba(255,255,255,0.1),transparent_60%)]"
                     style={{ transform: 'translateZ(0)', backfaceVisibility: 'hidden' }}></div>

                {/* 渐变叠加 - 统一透明度 */}
                <div className="absolute inset-0 w-full h-full bg-gradient-to-t from-black/20 to-transparent"
                     style={{
                       opacity: 0.4,
                       transition: 'opacity 0.3s ease',
                       transform: 'translateZ(0)',
                       backfaceVisibility: 'hidden'
                     }}
                     id={`overlay-${resource.name}`}></div>

                {/* 大型资源图标水印 - 缓存资源名强制固定定位 */}
                <div className="absolute inset-0 flex items-center justify-center select-none"
                     style={{
                       pointerEvents: 'none',
                       overflow: 'hidden',
                       transform: 'translateZ(0)',
                       backfaceVisibility: 'hidden'
                     }}>
                  <span className={`character-${resource.name} ${isMobile ? 'text-[85px]' : 'text-[160px]'} font-bold text-white/90`}
                        style={{
                          textShadow: '0 0 15px rgba(0,0,0,0.3), 0 0 30px rgba(0,0,0,0.2)',
                          transition: 'transform 0.3s ease, opacity 0.3s ease',
                          transform: 'translate(-50%, -50%) rotate(-12deg) scale(1)',
                          opacity: 0.9,
                          willChange: 'transform, opacity',
                          backfaceVisibility: 'hidden',
                          position: 'absolute',
                          top: '50%',
                          left: '50%'
                        }}>
                    {resource.character}
                  </span>
                </div>
              </div>

              {/* 添加高光叠加层 - 固定定位 */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/15 to-transparent"
                   style={{
                     transition: 'opacity 0.3s ease',
                     transform: 'translateZ(0)',
                     backfaceVisibility: 'hidden'
                   }}></div>
              <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,rgba(255,255,255,0.25),transparent_70%)]"
                   style={{
                     opacity: 0,
                     transition: 'opacity 0.3s ease',
                     transform: 'translateZ(0)',
                     backfaceVisibility: 'hidden'
                   }}
                   id={`highlight-${resource.name}`}></div>

              <div className="p-3 md:p-4 flex flex-col h-full justify-between relative z-10">
                <div className="flex items-center justify-between mb-1">
                  <div className={`icon-${resource.name} flex items-center justify-center`}>
                    {resource.icon}
                  </div>
                  <span className={`tag-${resource.name} text-xs text-white/90 px-2 py-0.5 bg-[#e50049]/20 backdrop-blur-sm rounded-full`}>
                    {resource.tag}
                  </span>
                </div>

                <div className="mt-auto">
                  <h3 className={`title-${resource.name} ${isMobile ? 'text-base' : 'text-lg'} font-bold text-white mb-0.5`}>{resource.name}</h3>
                  <p className={`desc-${resource.name} text-xs ${isMobile ? 'text-gray-200' : 'text-white/80'}`}>{resource.description}</p>
                </div>
              </div>
            </div>
          );

          return resource.url.startsWith('http') ? (
            <a key={resource.name} href={resource.url} target="_blank" rel="noopener noreferrer" className="block">
              {cardContent}
            </a>
          ) : (
            <Link key={resource.name} href={resource.url} className="block">
              {cardContent}
            </Link>
          );
        })}
      </div>
    </>
  );
}