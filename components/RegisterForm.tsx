"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Loader2, User, Lock, MapPin } from "lucide-react";
import Link from "next/link";
import { signIn } from "next-auth/react";
import { toast } from "react-hot-toast";

// 检测全角符号的函数
const hasFullWidthChars = (str: string): boolean => {
	return /[\uff01-\uff5e]/.test(str);
};

export default function RegisterForm() {
	const [username, setUsername] = useState("");
	const [password, setPassword] = useState("");
	const [confirmPassword, setConfirmPassword] = useState("");
	const [city, setCity] = useState("");
	const [error, setError] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const router = useRouter();

	const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
		e.preventDefault();
		setIsLoading(true);
		setError("");

		if (username.includes(' ')) {
			toast.error('用户名不能包含空格');
			setIsLoading(false);
			return;
		}

		if (hasFullWidthChars(username)) {
			toast.error('用户名不能包含全角符号');
			setIsLoading(false);
			return;
		}

		if (!username || !city) {
			toast.error('请填写所有必填项');
			setIsLoading(false);
			return;
		}

		if (password !== confirmPassword) {
			setError("密码不匹配");
			setIsLoading(false);
			return;
		}

		try {
			const response = await fetch("/api/register", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({
					username,
					password,
					city,
				}),
			});

			if (response.ok) {
				const result = await signIn("credentials", {
					username,
					password,
					redirect: false,
				});

				if (result?.error) {
					setError(result.error);
				} else {
					router.push("/");
				}
			} else {
				const data = await response.json();
				setError(data.message || "注册失败");
			}
		} catch (error) {
			setError("注册过程中出现错误");
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<form onSubmit={handleSubmit} className="space-y-6 mt-8">
			<div className="space-y-4">
				<div className="space-y-2">
					<Label htmlFor="username" className="text-sm font-medium text-[#FFB800]">
						俱乐部匿名ID
					</Label>
					<div className="relative">
						<User className="absolute left-3 top-2.5 h-5 w-5 text-[#666666]" />
						<Input
							type="text"
							id="username"
							value={username}
							onChange={(e) => {
								const value = e.target.value;
								if (!value.includes(' ') && !hasFullWidthChars(value)) {
									setUsername(value);
								}
							}}
							placeholder="创建你的俱乐部匿名ID"
							required
							className="pl-10 bg-white border-none text-black placeholder:text-[#999999]"
						/>
					</div>
				</div>

				<div className="space-y-2">
					<Label htmlFor="city" className="text-sm font-medium text-[#FFB800]">
						常驻城市
					</Label>
					<div className="relative">
						<MapPin className="absolute left-3 top-2.5 h-5 w-5 text-[#666666]" />
						<Input
							type="text"
							id="city"
							value={city}
							onChange={(e) => setCity(e.target.value)}
							placeholder="输入你的常驻城市"
							required
							className="pl-10 bg-white border-none text-black placeholder:text-[#999999]"
						/>
					</div>
				</div>

				<div className="space-y-2">
					<Label htmlFor="password" className="text-sm font-medium text-[#FFB800]">
						密码
					</Label>
					<div className="relative">
						<Lock className="absolute left-3 top-2.5 h-5 w-5 text-[#666666]" />
						<Input
							type="password"
							id="password"
							value={password}
							onChange={(e) => setPassword(e.target.value)}
							placeholder="创建你的密码"
							required
							className="pl-10 bg-white border-none text-black placeholder:text-[#999999]"
						/>
					</div>
				</div>

				<div className="space-y-2">
					<Label htmlFor="confirmPassword" className="text-sm font-medium text-[#FFB800]">
						确认密码
					</Label>
					<div className="relative">
						<Lock className="absolute left-3 top-2.5 h-5 w-5 text-[#666666]" />
						<Input
							type="password"
							id="confirmPassword"
							value={confirmPassword}
							onChange={(e) => setConfirmPassword(e.target.value)}
							placeholder="再次输入密码"
							required
							className="pl-10 bg-white border-none text-black placeholder:text-[#999999]"
						/>
					</div>
				</div>
			</div>

			{error && (
				<div className="bg-red-500/5 border border-red-500/10 rounded-lg p-3">
					<p className="text-red-500 text-sm">{error}</p>
				</div>
			)}

			<Button
				type="submit"
				disabled={isLoading}
				className="w-full h-11 bg-[#E11D48] hover:bg-[#E11D48]/90 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
			>
				{isLoading ? (
					<>
						<Loader2 className="mr-2 h-5 w-5 animate-spin" />
						注册中...
					</>
				) : (
					"注册"
				)}
			</Button>

			<div className="text-center mt-6">
				<Link
					href="/login"
					className="text-sm text-[#FFB800] hover:text-[#FFB800]/80 transition duration-200 hover:underline"
				>
					已有账号？点击登录
				</Link>
			</div>
		</form>
	);
}
