"use client";

import React, { useState, useEffect, useMemo, useCallback, useRef } from "react";
import { getGirls, getCityCounts, getDistrictCounts, getPriceCounts, getCups, getTypes, getServices, getCities } from "../lib/api";
import GirlCard from "./GirlCard";
import GirlCardSkeleton from "./GirlCardSkeleton";
import Pagination from "./Pagination";
import { urlToCityName, cityNameToUrl } from "../lib/cityNameMapping";
import Masonry from "react-masonry-css";
import "../styles/masonry.css";
import { Girl } from "../types/girl";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Button } from "./ui/button";
import { ChevronRight, ChevronUp, ChevronDown } from "lucide-react";
import { cityDistricts, firstTierCities } from "../lib/cityNameMapping";
import SelectItemWrapper from "./ui/SelectItemWrapper";
import { normalizeDistrict, getDisplayDistrict, getDistrictVariants, mergeDistrictCounts } from "../lib/districtUtils";
import { useSession } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import ScrollButtons from "./ScrollButtons";
import Breadcrumb from "./Breadcrumb";

interface CityPageClientProps {
	cityName: string;
}

const CityPageClient = React.memo(({ cityName }: CityPageClientProps) => {
	const router = useRouter();
	const searchParams = useSearchParams();
	const [decodedCityName, setDecodedCityName] = useState(urlToCityName[cityName] || decodeURIComponent(cityName));
	const { data: session } = useSession();
	const [girlsData, setGirlsData] = useState<{ girls: Girl[]; totalPages: number }>({
		girls: [],
		totalPages: 0,
	});
	const [cities, setCities] = useState<string[]>([]);
	const [cityCounts, setCityCounts] = useState<Record<string, number>>({});
	const [districtCounts, setDistrictCounts] = useState<Record<string, number>>({});
	const [priceCounts, setPriceCounts] = useState<Record<string, number>>({});
	const [cups, setCups] = useState<Record<string, number>>({});
	const [types, setTypes] = useState<Record<string, number>>({});
	const [services, setServices] = useState<Record<string, number>>({});
	const [isLoading, setIsLoading] = useState(true);
	const [currentPage, setCurrentPage] = useState(1);
	const [cardsPerPage, setCardsPerPage] = useState(20);
	const [isMobile, setIsMobile] = useState(false);
	const [filters, setFilters] = useState({
		city: decodedCityName,
		district: searchParams.get("district") || "",
		price: searchParams.get("price") || "3K",
		cup: searchParams.get("cup") || "",
		type: searchParams.get("type") || "",
		service: searchParams.get("service") || "",
	});

	const breakpointColumnsObj = useMemo(
		() => ({
			default: 5,
			2000: 4,
			1600: 3,
			1200: 2,
			768: 1,
			500: 1,
		}),
		[]
	);

	useEffect(() => {
		const handleResize = () => {
			setIsMobile(window.innerWidth <= 768);
		};
		handleResize();
		window.addEventListener("resize", handleResize);
		return () => window.removeEventListener("resize", handleResize);
	}, []);

	useEffect(() => {
		const fetchData = async () => {
			setIsLoading(true);
			try {
				const normalizedFilters = {
					...filters,
					district: filters.district && filters.district !== "_all" 
						? getDistrictVariants(filters.district)
						: "",
					city: decodedCityName,
					price: filters.price || "3K"
				};

				// 未登录用户只加载6个卡片
				const effectiveCardsPerPage = session?.user ? cardsPerPage : 6;

				const [girlsResponse, cityCountsData, districtCountsData, priceCountsData, cupsData, typesData, servicesData, citiesData] = await Promise.all([
					getGirls(currentPage, effectiveCardsPerPage, normalizedFilters),
					getCityCounts(),
					getDistrictCounts(decodedCityName),
					getPriceCounts(decodedCityName),
					getCups(decodedCityName),
					getTypes(decodedCityName),
					getServices(decodedCityName),
					getCities(),
				]);

				const normalizedDistrictCounts = mergeDistrictCounts(districtCountsData);

				setGirlsData(girlsResponse);
				setCityCounts(cityCountsData);
				setDistrictCounts(normalizedDistrictCounts);
				setPriceCounts(priceCountsData);
				setCups(cupsData);
				setTypes(typesData);
				setServices(servicesData);
				setCities(citiesData);
			} catch (error) {
				console.error("Error fetching data:", error);
			} finally {
				setIsLoading(false);
			}
		};

		fetchData();
	}, [decodedCityName, currentPage, cardsPerPage, filters, session?.user]);

	const updateUrlParams = useCallback((newFilters: typeof filters) => {
		const params = new URLSearchParams();
		Object.entries(newFilters).forEach(([key, value]) => {
			if (value && key !== "city") {
				params.set(key, value);
			}
		});
		const newUrl = window.location.pathname + (params.toString() ? `?${params.toString()}` : "");
		window.history.replaceState({}, "", newUrl);
	}, []);

	const handleFilterChange = useCallback((key: string, value: string) => {
		const newFilters = {
			...filters,
			[key]: value === "_all" ? "" : value,
		};
		
		// 确保价格始终为3K
		if (key === "price" && value === "_all") {
			newFilters.price = "3K";
		}
		
		setFilters(newFilters);
		setCurrentPage(1);
		updateUrlParams(newFilters);
	}, [filters, updateUrlParams]);

	const handlePageChange = useCallback((newPage: number) => {
		setCurrentPage(newPage);
	}, []);

	const sortedDistricts = Object.entries(districtCounts).sort((a, b) => b[1] - a[1]);
	const sortedPriceCounts = Object.entries(priceCounts).sort((a, b) => b[1] - a[1]);
	const sortedCups = Object.entries(cups).sort((a, b) => b[1] - a[1]);
	const sortedTypes = Object.entries(types).sort((a, b) => b[1] - a[1]);
	const sortedServices = Object.entries(services).sort((a, b) => b[1] - a[1]);

	const isFirstTierCity = firstTierCities.includes(decodedCityName);

	const renderFilters = useMemo(() => {
		return (
			<div className="w-full">
				<div className="w-full mb-2 md:mb-0 md:hidden">
					{isFirstTierCity && (
						<Select onValueChange={(value) => handleFilterChange("district", value)} value={filters.district || "_all"}>
							<SelectTrigger className="w-full h-9 px-2 text-sm text-dark-foreground">
								<SelectValue placeholder="选择区域" />
							</SelectTrigger>
							<SelectContent className="bg-black text-white border-gray-800">
								<SelectItem value="_all">所有区域</SelectItem>
								{cityDistricts[decodedCityName]
									? cityDistricts[decodedCityName]
											.filter((district) => {
												const normalizedDistrict = normalizeDistrict(district);
												return normalizedDistrict && districtCounts[normalizedDistrict] > 0;
											})
											.sort((a, b) => {
												const countA = districtCounts[normalizeDistrict(a)] || 0;
												const countB = districtCounts[normalizeDistrict(b)] || 0;
												return countB - countA;
											})
											.map((district) => {
												const normalizedDistrict = normalizeDistrict(district);
												const displayName = getDisplayDistrict(normalizedDistrict);
												const count = districtCounts[normalizedDistrict] || 0;
												return (
													<SelectItemWrapper key={district} value={normalizedDistrict}>
														{displayName} ({count})
													</SelectItemWrapper>
												);
											})
									: Object.entries(districtCounts)
									.filter(([_, count]) => count > 0)
											.sort(([, a], [, b]) => b - a)
											.map(([district, count]) => {
												const normalizedDistrict = normalizeDistrict(district);
												const displayName = getDisplayDistrict(normalizedDistrict);
												return (
													<SelectItemWrapper key={district} value={normalizedDistrict}>
														{displayName} ({count})
													</SelectItemWrapper>
												);
											})}
							</SelectContent>
						</Select>
					)}
				</div>

				<div className={`grid ${isMobile ? 'grid-cols-2' : 'grid-cols-3 md:flex md:flex-row'} w-full gap-2`}>
					<div className="hidden md:block">
						{isFirstTierCity && (
							<Select onValueChange={(value) => handleFilterChange("district", value)} value={filters.district || "_all"}>
								<SelectTrigger className="w-[180px] h-9 px-2 text-sm text-dark-foreground">
									<SelectValue placeholder="选择区域" />
								</SelectTrigger>
								<SelectContent className="bg-black text-white border-gray-800">
									<SelectItem value="_all">所有区域</SelectItem>
									{cityDistricts[decodedCityName]
										? cityDistricts[decodedCityName]
												.filter((district) => {
													const normalizedDistrict = normalizeDistrict(district);
													return normalizedDistrict && districtCounts[normalizedDistrict] > 0;
												})
												.sort((a, b) => {
													const countA = districtCounts[normalizeDistrict(a)] || 0;
													const countB = districtCounts[normalizeDistrict(b)] || 0;
													return countB - countA;
												})
												.map((district) => {
													const normalizedDistrict = normalizeDistrict(district);
													const displayName = getDisplayDistrict(normalizedDistrict);
													const count = districtCounts[normalizedDistrict] || 0;
													return (
														<SelectItemWrapper key={district} value={normalizedDistrict}>
															{displayName} ({count})
														</SelectItemWrapper>
													);
												})
										: Object.entries(districtCounts)
										.filter(([_, count]) => count > 0)
												.sort(([, a], [, b]) => b - a)
												.map(([district, count]) => {
													const normalizedDistrict = normalizeDistrict(district);
													const displayName = getDisplayDistrict(normalizedDistrict);
													return (
														<SelectItemWrapper key={district} value={normalizedDistrict}>
															{displayName} ({count})
														</SelectItemWrapper>
													);
												})}
								</SelectContent>
							</Select>
						)}
					</div>

					<Select onValueChange={(value) => handleFilterChange("price", value)} value={filters.price || "3K"}>
						<SelectTrigger className="w-full h-9 px-2 text-sm text-dark-foreground">
							<SelectValue placeholder="选择档次" />
						</SelectTrigger>
						<SelectContent className="bg-black text-white border-gray-800">
							<SelectItem value="3K">3K 精选资源</SelectItem>
							<div 
								onClick={() => router.push("/jianier5kw")} 
								className="relative flex w-full cursor-pointer select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors hover:bg-accent hover:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
							>
								<span>5W极品资源在💎⁵ꪝ极品营</span>
							</div>
						</SelectContent>
					</Select>

					<Select onValueChange={(value) => handleFilterChange("cup", value)} value={filters.cup || "_all"}>
						<SelectTrigger className="w-full h-9 px-2 text-sm text-dark-foreground">
							<SelectValue placeholder="选择罩杯" />
						</SelectTrigger>
						<SelectContent className="bg-black text-white border-gray-800">
							<SelectItem value="_all">所有罩杯</SelectItem>
							{sortedCups
								.filter(([_, count]) => count > 0)
								.map(([cup, count]) => (
									<SelectItem key={cup} value={cup}>
										{cup || "未知"} ({count})
									</SelectItem>
								))}
						</SelectContent>
					</Select>

					<Select onValueChange={(value) => handleFilterChange("type", value)} value={filters.type || "_all"}>
						<SelectTrigger className="w-full h-9 px-2 text-sm text-dark-foreground">
							<SelectValue placeholder="选择类型" />
						</SelectTrigger>
						<SelectContent className="max-h-[300px] overflow-y-auto bg-black text-white border-gray-800">
							<SelectItem value="_all">全部类型</SelectItem>
							{Object.entries(types)
								.filter(([_, count]) => count > 0)
								.map(([type, count]) => (
									<SelectItem key={type} value={type}>
										{type || "未知"} ({count})
									</SelectItem>
								))}
						</SelectContent>
					</Select>

					<Select onValueChange={(value) => handleFilterChange("service", value)} value={filters.service || "_all"}>
						<SelectTrigger className="w-full h-9 px-2 text-sm text-dark-foreground">
							<SelectValue placeholder="选择服务" />
						</SelectTrigger>
						<SelectContent className="bg-black text-white border-gray-800">
							<SelectItem value="_all">所有服务</SelectItem>
							{sortedServices
								.filter(([_, count]) => count > 0)
								.map(([service, count]) => (
									<SelectItem key={service} value={service}>
										{service || "未知"} ({count})
									</SelectItem>
								))}
						</SelectContent>
					</Select>
				</div>
			</div>
		);
	}, [filters, districtCounts, priceCounts, cups, types, services]);

	const renderGirlsList = useMemo(() => {
		return (
			<div className="mb-6">
				<Masonry
					breakpointCols={{
						default: 5,
						1600: 4,
						1200: 3,
						768: 2,
						500: 1,
					}}
					className="my-masonry-grid"
					columnClassName="my-masonry-grid_column"
				>
					{girlsData.girls.map((girl, index) => (
						<div key={girl.id} className="mb-4 w-full girl-card">
							<GirlCard 
								data={girl} 
								forceShowContent={!!session?.user}
							/>
						</div>
					))}
				</Masonry>
			</div>
		);
	}, [girlsData, session]);

	const renderPagination = useMemo(() => {
		return (
			<div className={`mt-8 flex justify-center ${isMobile ? 'pb-12' : ''}`}>
				<Pagination
					currentPage={currentPage}
					totalPages={girlsData.totalPages}
					onPageChange={handlePageChange}
					isMobile={isMobile}
				/>
			</div>
		);
	}, [currentPage, girlsData.totalPages, handlePageChange, isMobile]);

	const breadcrumbItems = useMemo(() => {
		const items = [
			{ label: "城市导航", href: "/city" },
			{ label: `${decodedCityName}资源库` }
		];
		
		if (filters.district && filters.district !== "_all") {
			const displayDistrict = getDisplayDistrict(filters.district);
			items.push({ label: displayDistrict });
		}
		
		return items;
	}, [decodedCityName, filters.district]);

	return (
		<div className="px-4 pt-4 pb-2 max-w-full mx-auto">
			<Breadcrumb items={breadcrumbItems} />
			
			<div className="flex flex-wrap gap-4 mb-6">
				{renderFilters}
			</div>

			{isLoading ? (
				<div className="mt-6">
					<Masonry
						breakpointCols={breakpointColumnsObj}
						className="my-masonry-grid"
						columnClassName="my-masonry-grid_column"
					>
						{Array.from({ length: 10 }).map((_, index) => (
							<div key={index} className="mb-4">
								<GirlCardSkeleton />
							</div>
						))}
					</Masonry>
				</div>
			) : (
				<>
					<div className="mt-6">
						<Masonry
							breakpointCols={breakpointColumnsObj}
							className="my-masonry-grid"
							columnClassName="my-masonry-grid_column"
						>
							{girlsData.girls.map((girl) => (
								<div key={girl.id} className="mb-4 girl-card">
									<GirlCard data={girl} forceShowContent={!!session?.user} />
								</div>
							))}
						</Masonry>
					</div>
					
					{session?.user ? (
						<div className={`mt-8 flex justify-center ${isMobile ? 'pb-12' : ''}`}>
							<Pagination
								currentPage={currentPage}
								totalPages={girlsData.totalPages}
								onPageChange={handlePageChange}
								isMobile={isMobile}
							/>
						</div>
					) : (
						<div className="mt-8 text-center">
							<p className="text-gray-400 mb-4">注册登录后查看更多内容</p>
						</div>
					)}
				</>
			)}
			{!isLoading && <ScrollButtons 
				onGoToNextPage={() => {
					if (currentPage < girlsData.totalPages && session?.user) {
						handlePageChange(currentPage + 1);
						window.scrollTo({ top: 0, behavior: 'smooth' });
					}
				}} 
				isLastPage={currentPage >= girlsData.totalPages}
			/>}
		</div>
	);
});

CityPageClient.displayName = 'CityPageClient';

export default CityPageClient;
