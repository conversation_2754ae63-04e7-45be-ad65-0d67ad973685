import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, ComponentType } from "react";
import { toast } from "react-hot-toast";

interface WithAuthOptions {
	requireVIP?: boolean;
	redirectTo?: string;
	message?: string;
}

const withAuth = <P extends object>(
	WrappedComponent: ComponentType<P>,
	options: WithAuthOptions = {}
) => {
	const WithAuth: React.FC<P> = (props) => {
		const { data: session, status } = useSession();
		const router = useRouter();
		const {
			requireVIP = false,
			redirectTo = "/login",
			message = "请先登录"
		} = options;

		useEffect(() => {
			if (status === "loading") return;

			if (!session) {
				toast.error(message);
				router.push(redirectTo);
				return;
			}

			// VIP权限验证
			if (requireVIP && !session.user?.isVIP) {
				toast.error("此页面需要VIP权限");
				router.push("/vip");
				return;
			}
		}, [session, status, router, requireVIP, redirectTo, message]);

		// 加载状态显示
		if (status === "loading") {
			return (
				<div className="flex items-center justify-center min-h-screen">
					<div className="text-xl text-[#ffa31a]">加载中...</div>
				</div>
			);
		}

		// 未登录或需要VIP但不是VIP时不显示内容
		if (!session || (requireVIP && !session.user?.isVIP)) {
			return null;
		}

		return <WrappedComponent {...props} />;
	};

	WithAuth.displayName = `WithAuth(${
		WrappedComponent.displayName || WrappedComponent.name || "Component"
	})`;

	return WithAuth;
};

export default withAuth;
