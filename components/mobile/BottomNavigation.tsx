"use client";

import { Home, MapPin, Compass, Video } from "lucide-react";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";
import Link from "next/link";
import { cn } from "../../lib/utils";
import Intercom, { show } from "@intercom/messenger-js-sdk";
import { LucideIcon } from "lucide-react";
import { useSession } from "next-auth/react";
import { toast } from "react-hot-toast";

type NavigationItem = {
  label: string;
  path?: string;
  onClick?: (isLoggedIn: boolean) => void;
} & (
  | { icon: LucideIcon; iconPath?: never }
  | { icon?: never; iconPath: string }
);

export function BottomNavigation() {
  const pathname = usePathname();
  const { data: session } = useSession();
  const router = useRouter();

  const handleCustomerService = () => {
    if (session) {
      // 已登录，打开客服对话
      show();
    } else {
      // 未登录，显示提示并跳转到登录页面
      toast.error("请登录后联系在线客服");
      router.push("/login");
    }
  };

  const navigationItems: NavigationItem[] = [
    {
      label: "首页",
      icon: Home,
      path: "/",
    },
    {
      label: "城市",
      icon: MapPin,
      path: "/city",
    },
    {
      label: "抖妮",
      icon: Video,
      path: "/tiktok",
    },
    {
      label: "发现",
      icon: Compass,
      path: "/discover",
    },
    {
      label: "客服",
      iconPath: "/icons/kefu.jpg",
      onClick: handleCustomerService,
    },
  ];

  return (
    <nav className="fixed bottom-0 left-0 right-0 z-50 h-14 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-t">
      <div className="grid h-full grid-cols-5 mx-auto max-w-md">
        {navigationItems.map((item) => {
          const isActive = pathname === (item.path || "");
          const Component = item.onClick ? "button" : Link;
          const commonProps = {
            className: cn(
              "flex flex-col items-center justify-center gap-0.5 text-muted-foreground",
              isActive && "text-primary"
            ),
          };

          return item.onClick ? (
            <button
              key={item.label}
              onClick={() => item.onClick?.(!!session)}
              {...commonProps}
            >
              <div className="relative">
                {item.iconPath ? (
                  <>
                    <Image
                      src={item.iconPath}
                      alt={item.label}
                      width={20}
                      height={20}
                      className={cn(
                        "h-5 w-5 rounded-full object-cover transition-all",
                        isActive && "scale-110"
                      )}
                    />
                    {item.label === "客服" && (
                      <span className="absolute bottom-0 -right-0.5 h-1.5 w-1.5 rounded-full bg-green-500 ring-0.5 ring-background"></span>
                    )}
                  </>
                ) : item.icon ? (
                  <>
                    <item.icon
                      className={cn(
                        "h-5 w-5 rounded-full object-cover transition-all",
                        isActive && "scale-110"
                      )}
                    />
                  </>
                ) : null}
              </div>
              <span className="text-xs font-medium">{item.label}</span>
            </button>
          ) : (
            <Link
              key={item.path}
              href={item.path!}
              {...commonProps}
            >
              <div className="relative">
                {item.iconPath ? (
                  <>
                    <Image
                      src={item.iconPath}
                      alt={item.label}
                      width={20}
                      height={20}
                      className={cn(
                        "h-5 w-5 rounded-full object-cover transition-all",
                        isActive && "scale-110"
                      )}
                    />
                    {item.label === "客服" && (
                      <span className="absolute bottom-0 -right-0.5 h-1.5 w-1.5 rounded-full bg-green-500 ring-0.5 ring-background"></span>
                    )}
                  </>
                ) : item.icon ? (
                  <>
                    <item.icon
                      className={cn(
                        "h-5 w-5 rounded-full object-cover transition-all",
                        isActive && "scale-110"
                      )}
                    />
                  </>
                ) : null}
              </div>
              <span className="text-xs font-medium">{item.label}</span>
            </Link>
          );
        })}
      </div>
    </nav>
  );
}