"use client";

import { BottomNavigation } from "./BottomNavigation";
import "../../styles/navigation.css";
import dynamic from "next/dynamic";

// 使用dynamic导入，避免SSR问题
const HomePwaInstallPrompt = dynamic(
  () => import("../ui/HomePwaInstallPrompt"),
  { ssr: false }
);

interface MobileLayoutProps {
  children: React.ReactNode;
}

export function MobileLayout({ children }: MobileLayoutProps) {
  return (
    <main className="min-h-screen flex flex-col">
      <div className="main-content flex-1">
        {children}
      </div>

      {/* 仅在移动设备上的主页显示PWA安装提示，确保在底部导航栏之前渲染 */}
      <div className="block md:hidden">
        <HomePwaInstallPrompt />
      </div>

      <footer className="fixed bottom-0 left-0 right-0 block md:hidden z-[9999]">
        <BottomNavigation />
      </footer>
    </main>
  );
}