"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { User, Mail, Calendar, MapPin, Heart, X, DollarSign, Zap, Briefcase, Crown } from "lucide-react";

export default function ProfileSkeleton() {
  return (
    <div className="relative min-h-screen bg-background pt-16 px-4 overflow-hidden">
      {/* 背景样式保持与ProfileContent一致 */}
      <div className="absolute inset-0 w-full h-full bg-gradient-to-b from-background to-background">
        <div className="absolute inset-0 w-full h-full bg-[radial-gradient(ellipse_at_top_right,rgba(229,0,73,0.15),rgba(229,0,73,0)_50%)]"></div>
        <div className="absolute inset-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(229,0,73,0.1),rgba(229,0,73,0)_50%)]"></div>
      </div>

      <div className="absolute inset-0 w-full h-full opacity-30">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(229,0,73,0.05)_1px,transparent_1px)] bg-[size:32px_32px]"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(229,0,73,0.05)_1px,transparent_1px)] bg-[size:24px_24px] rotate-15"></div>
      </div>

      <div className="relative container mx-auto max-w-4xl">
        {/* 静态版本的浮动元素 */}
        <div className="absolute -top-20 -left-20 w-60 h-60 bg-[#e50049]/5 rounded-full filter blur-3xl"></div>
        <div className="absolute -top-20 -right-20 w-60 h-60 bg-[#e50049]/5 rounded-full filter blur-3xl"></div>

        <div className="text-center mb-16">
          {/* 用户名骨架屏 */}
          <div className="flex justify-center mb-4">
            <Skeleton className="h-[60px] w-[200px] bg-[#e50049]/10" />
          </div>
          
          {/* VIP状态骨架屏 */}
          <div className="flex items-center justify-center gap-2 mb-4">
            <Skeleton className="h-8 w-24 rounded-full bg-gray-700/20" />
          </div>
          
          {/* 注册日期骨架屏 */}
          <Skeleton className="h-6 w-40 mx-auto bg-muted/50" />
        </div>

        <div className="grid gap-6">
          {/* 基本信息卡片骨架屏 */}
          <div className="relative overflow-hidden rounded-2xl border border-[#e50049]/20 bg-[#e50049]/5 p-6">
            <div className="absolute inset-0">
              <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/5"></div>
              <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,rgba(255,255,255,0.05),transparent_50%)]"></div>
            </div>
            
            <div className="relative">
              <h2 className="text-2xl font-bold mb-6 text-[#e50049]">基本信息</h2>
              <div className="grid gap-4">
                <SkeletonItem icon={<User className="w-5 h-5" />} color="#e50049" />
                <SkeletonItem icon={<MapPin className="w-5 h-5" />} color="#e50049" />
              </div>
            </div>
          </div>

          {/* 偏好设置卡片骨架屏 */}
          <div className="relative overflow-hidden rounded-2xl border border-[#ffa31a]/20 bg-[#ffa31a]/5 p-6">
            <div className="absolute inset-0">
              <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/5"></div>
              <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,rgba(255,255,255,0.05),transparent_50%)]"></div>
            </div>
            
            <div className="relative">
              <h2 className="text-2xl font-bold mb-6 text-[#ffa31a]">偏好设置</h2>
              <div className="grid gap-4">
                <SkeletonItem icon={<Heart className="w-5 h-5" />} color="#ffa31a" />
                <SkeletonItem icon={<X className="w-5 h-5" />} color="#ffa31a" />
                <SkeletonItem icon={<DollarSign className="w-5 h-5" />} color="#ffa31a" />
              </div>
            </div>
          </div>

          {/* 特殊偏好卡片骨架屏 */}
          <div className="relative overflow-hidden rounded-2xl border border-[#00a6ff]/20 bg-[#00a6ff]/5 p-6">
            <div className="absolute inset-0">
              <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/5"></div>
              <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,rgba(255,255,255,0.05),transparent_50%)]"></div>
            </div>
            
            <div className="relative">
              <h2 className="text-2xl font-bold mb-6 text-[#00a6ff]">特殊偏好</h2>
              <div className="grid gap-4">
                <SkeletonItem icon={<Zap className="w-5 h-5" />} color="#00a6ff" />
                <SkeletonItem icon={<Briefcase className="w-5 h-5" />} color="#00a6ff" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function SkeletonItem({ icon, color }: { icon: React.ReactNode; color: string }) {
  return (
    <div className="flex items-center space-x-4 p-3 rounded-xl hover:bg-white/5 transition-colors">
      <div className={`w-10 h-10 rounded-xl flex items-center justify-center`} style={{ backgroundColor: `${color}20` }}>
        <div className="text-[#e50049]">{icon}</div>
      </div>
      <div className="flex-1">
        <Skeleton className="h-4 w-24 mb-2" />
        <Skeleton className="h-5 w-40" />
      </div>
    </div>
  );
} 