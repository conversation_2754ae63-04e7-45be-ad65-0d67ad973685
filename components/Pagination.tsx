"use client";

import React, { useState } from "react";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import SelectItemWrapper from "./ui/SelectItemWrapper";

interface PaginationProps {
	currentPage: number;
	totalPages: number;
	onPageChange: (page: number) => void;
	onPageSizeChange?: (pageSize: number) => void;
	isMobile?: boolean;
}

const Pagination: React.FC<PaginationProps> = ({ 
	currentPage, 
	totalPages, 
	onPageChange, 
	onPageSizeChange,
	isMobile = false
}) => {
	const [jumpToPage, setJumpToPage] = useState("");

	const handlePageChange = (page: number) => {
		onPageChange(page);
		setTimeout(() => {
			window.scrollTo(0, 0);
		}, 100);
	};

	const handleJumpToPage = () => {
		const pageNumber = parseInt(jumpToPage, 10);
		if (pageNumber >= 1 && pageNumber <= totalPages) {
			handlePageChange(pageNumber);
			setJumpToPage("");
		}
	};

	const renderDesktopPagination = () => (
		<div className="flex items-center space-x-2 text-sm">
			<Button 
				size="sm" 
				onClick={() => handlePageChange(1)} 
				disabled={currentPage === 1} 
				className="bg-dark-accent hover:bg-dark-accent-hover text-dark-foreground"
			>
				首页
			</Button>
			<Button
				size="sm"
				onClick={() => handlePageChange(currentPage - 1)}
				disabled={currentPage === 1}
				className={`px-4 py-2 rounded-md ${
					currentPage === 1 ? "bg-dark-secondary text-dark-muted cursor-not-allowed" : "bg-dark-accent text-dark-foreground hover:bg-dark-accent-hover"
				} transition-colors duration-200`}
			>
				上一页
			</Button>
			<span className="text-dark-foreground">
				第 {currentPage} 页，共 {totalPages} 页
			</span>
			<Button
				size="sm"
				onClick={() => handlePageChange(currentPage + 1)}
				disabled={currentPage === totalPages}
				className={`px-4 py-2 rounded-md ${
					currentPage === totalPages ? "bg-dark-secondary text-dark-muted cursor-not-allowed" : "bg-dark-accent text-dark-foreground hover:bg-dark-accent-hover"
				} transition-colors duration-200`}
			>
				下一页
			</Button>
			<Button
				size="sm"
				onClick={() => handlePageChange(totalPages)}
				disabled={currentPage === totalPages}
				className="bg-dark-accent hover:bg-dark-accent-hover text-dark-foreground"
			>
				末页
			</Button>
			<Input
				type="number"
				value={jumpToPage}
				onChange={(e) => setJumpToPage(e.target.value)}
				placeholder="页码"
				className="w-16 h-8 bg-dark-secondary text-dark-foreground border-dark-accent focus:border-dark-accent-hover focus:ring-2 focus:ring-dark-accent-hover"
			/>
			<Button 
				size="sm" 
				onClick={handleJumpToPage} 
				className="bg-dark-accent text-dark-foreground hover:bg-dark-accent-hover transition-colors duration-200"
			>
				跳转
			</Button>
			{onPageSizeChange && (
				<Select onValueChange={(value) => onPageSizeChange(Number(value))}>
					<SelectTrigger className="w-[100px] h-8 bg-dark-secondary text-dark-foreground border-dark-accent">
						<SelectValue placeholder="每页显示" />
					</SelectTrigger>
					<SelectContent className="bg-dark-secondary text-dark-foreground border border-dark-accent">
						<SelectItemWrapper value="10">每页10条</SelectItemWrapper>
						<SelectItemWrapper value="20">每页20条</SelectItemWrapper>
						<SelectItemWrapper value="50">每页50条</SelectItemWrapper>
					</SelectContent>
				</Select>
			)}
		</div>
	);

	const renderMobilePagination = () => (
		<div className="flex items-center justify-between w-full max-w-full text-sm">
			<Button
				size="sm"
				onClick={() => handlePageChange(currentPage - 1)}
				disabled={currentPage === 1}
				className="bg-dark-accent hover:bg-dark-accent-hover text-dark-foreground px-2"
				variant="ghost"
			>
				上一页
			</Button>
			<div className="flex items-center">
				<span className="text-dark-foreground whitespace-nowrap text-base font-medium">
					{currentPage}/{totalPages}
				</span>
			</div>
			<Button
				size="sm"
				onClick={() => handlePageChange(currentPage + 1)}
				disabled={currentPage === totalPages}
				className="bg-dark-accent hover:bg-dark-accent-hover text-dark-foreground px-2"
				variant="ghost"
			>
				下一页
			</Button>
		</div>
	);

	return (
		<div className={`flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-2 mt-4 bg-dark-background p-4 rounded-lg ${isMobile ? 'w-full max-w-[95%] mx-auto' : ''}`}>
			{isMobile ? renderMobilePagination() : renderDesktopPagination()}
		</div>
	);
};

export default Pagination;
