"use client";

import React, { useState, useEffect, useMemo, useCallback } from "react";
import { useRouter } from "next/navigation";
import { Girl, getFeedbackData } from "../lib/api";
import GirlCard from "./GirlCard";
import GirlCardSkeleton from "./GirlCardSkeleton";
import Masonry from "react-masonry-css";
import "../styles/masonry.css";
import Pagination from "./Pagination";
import { useSession } from "next-auth/react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import SelectItemWrapper from "./ui/SelectItemWrapper";
import { ChevronUp, ChevronDown } from "lucide-react";
import { Button } from "./ui/button";
import ScrollButtons from "./ScrollButtons";
import { useSearchParams } from "next/navigation";
import { firstTierCities } from "../lib/cityNameMapping";

interface FeedbackPageClientProps {
  initialData: {
    girls: Girl[];
    totalPages: number;
  };
}

const FeedbackPageClient = React.memo(({ initialData }: FeedbackPageClientProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session } = useSession();
  const [currentPage, setCurrentPage] = useState(1);
  const [cardsPerPage, setCardsPerPage] = useState(20);
  const [girls, setGirls] = useState<Girl[]>(initialData.girls);
  const [totalPages, setTotalPages] = useState(initialData.totalPages);
  const [isLoading, setIsLoading] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isExtraNarrow, setIsExtraNarrow] = useState(false);
  const [imagesLoading, setImagesLoading] = useState(true);
  const [filters, setFilters] = useState({
    city: searchParams.get("city") || "",
    price: searchParams.get("price") || "",
  });

  const [filterOptions, setFilterOptions] = useState({
    cities: new Set<string>(),
    prices: new Set<string>(),
  });

  const [allFilterOptions, setAllFilterOptions] = useState({
    cities: new Set<string>(),
    prices: new Set<string>(),
  });

  const breakpointColumnsObj = useMemo(
    () => ({
      default: 5,
      2000: 4,
      1600: 3,
      1200: 2,
      768: 1,
      500: 1,
      400: 1,
      350: 1
    }),
    []
  );

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    const handleExtraNarrowScreen = () => {
      setIsExtraNarrow(window.innerWidth <= 400);
    };
    handleExtraNarrowScreen();
    window.addEventListener("resize", handleExtraNarrowScreen);
    return () => window.removeEventListener("resize", handleExtraNarrowScreen);
  }, []);

  useEffect(() => {
    const updateFilterOptions = () => {
      const newOptions = {
        cities: new Set<string>(),
        prices: new Set<string>(),
      };

      girls.forEach(girl => {
        if (girl.city) newOptions.cities.add(girl.city);
        if (girl.price) newOptions.prices.add(girl.price);
      });

      setFilterOptions(newOptions);
    };

    updateFilterOptions();
  }, [girls]);

  useEffect(() => {
    const fetchAllOptions = async () => {
      try {
        const data = await getFeedbackData(1, 1000); // 获取足够多的数据来提取所有选项
        const newOptions = {
          cities: new Set<string>(),
          prices: new Set<string>(),
        };

        data.girls.forEach(girl => {
          if (girl.city) newOptions.cities.add(girl.city);
          if (girl.price) newOptions.prices.add(girl.price);
        });

        setAllFilterOptions(newOptions);
      } catch (error) {
        console.error("Error fetching all filter options:", error);
      }
    };

    fetchAllOptions();
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // 未登录用户只加载6个卡片
        const effectiveCardsPerPage = session?.user ? cardsPerPage : 6;

        const data = await getFeedbackData(currentPage, effectiveCardsPerPage, filters);
        setGirls(data.girls);
        setTotalPages(data.totalPages);
      } catch (error) {
        console.error("Error fetching feedback data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [currentPage, cardsPerPage, filters, session?.user]);

  const updateUrlParams = useCallback((newFilters: typeof filters) => {
    const params = new URLSearchParams();
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value) {
        params.set(key, value);
      }
    });
    const newUrl = window.location.pathname + (params.toString() ? `?${params.toString()}` : "");
    window.history.replaceState({}, "", newUrl);
  }, []);

  const handleFilterChange = useCallback((key: string, value: string) => {
    const newFilters = {
      ...filters,
      [key]: value === "_all" ? "" : value,
    };
    setFilters(newFilters);
    setCurrentPage(1);
    updateUrlParams(newFilters);
  }, [filters, updateUrlParams]);

  const handlePageChange = useCallback((newPage: number) => {
    setCurrentPage(newPage);
    setTimeout(() => {
      window.scrollTo(0, 0);
    }, 100);
  }, []);

  const [layoutKey, setLayoutKey] = useState(0);

  useEffect(() => {
    if (!isLoading && girls.length > 0) {
      setImagesLoading(true);

      const initialTimer = setTimeout(() => {
        setLayoutKey(prev => prev + 1);
      }, 500);

      const fullLoadTimer = setTimeout(() => {
        setImagesLoading(false);
        setLayoutKey(prev => prev + 1);
      }, 2000);

      return () => {
        clearTimeout(initialTimer);
        clearTimeout(fullLoadTimer);
      };
    }
  }, [isLoading, girls]);

  // 城市排序函数：一线城市按指定顺序在前，其他城市按字母顺序在后
  const sortCities = useCallback((cities: string[]) => {
    const firstTier = firstTierCities.filter(city => cities.includes(city));
    const others = cities.filter(city => !firstTierCities.includes(city)).sort();
    return [...firstTier, ...others];
  }, []);

  const renderFilters = useMemo(() => {
    const sortedCities = sortCities(Array.from(allFilterOptions.cities));

    return (
      <div className="w-full">
        <div className="grid grid-cols-2 md:flex md:flex-row w-full gap-2">
          <Select onValueChange={(value) => handleFilterChange("city", value)} value={filters.city || "_all"}>
            <SelectTrigger className="w-full md:w-[180px] h-9 px-2 text-sm text-dark-foreground">
              <SelectValue placeholder="选择城市" />
            </SelectTrigger>
            <SelectContent className="bg-black text-white border-gray-800">
              <SelectItem value="_all">所有城市</SelectItem>
              {sortedCities.map(city => (
                <SelectItem key={city} value={city}>
                  {city}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select onValueChange={(value) => handleFilterChange("price", value)} value={filters.price || "_all"}>
            <SelectTrigger className="w-full md:w-[180px] h-9 px-2 text-sm text-dark-foreground">
              <SelectValue placeholder="选择价格" />
            </SelectTrigger>
            <SelectContent className="bg-black text-white border-gray-800">
              <SelectItem value="_all">所有档次</SelectItem>
              {Array.from(allFilterOptions.prices).sort().map(price => (
                <SelectItem key={price} value={price}>
                  {price}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    );
  }, [filters, allFilterOptions, handleFilterChange, sortCities]);

  return (
    <div className={`${isExtraNarrow ? 'px-3' : 'px-4'} pt-4 pb-2 max-w-full mx-auto ${imagesLoading ? 'masonry-loading' : ''}`}>
      <div className="flex flex-wrap gap-4 mb-6">
        {renderFilters}
      </div>

      {isLoading ? (
        <Masonry
          key={`loading-${layoutKey}`}
          breakpointCols={breakpointColumnsObj}
          className={`my-masonry-grid ${isExtraNarrow ? 'extra-narrow-screen' : ''}`}
          columnClassName="my-masonry-grid_column"
        >
          {Array.from({ length: 10 }).map((_, index) => (
            <div key={index} className="mb-4">
              <GirlCardSkeleton />
            </div>
          ))}
        </Masonry>
      ) : (
        <>
          <Masonry
            key={`loaded-${layoutKey}`}
            breakpointCols={breakpointColumnsObj}
            className={`my-masonry-grid ${isExtraNarrow ? 'extra-narrow-screen' : ''}`}
            columnClassName="my-masonry-grid_column"
          >
            {girls.map((girl) => (
              <div key={girl.id} className="mb-4 w-full mx-auto girl-card">
                <GirlCard data={girl} />
              </div>
            ))}
          </Masonry>
          {session?.user ? (
            <div className={`mt-8 flex justify-center ${isMobile ? 'pb-20' : ''}`}>
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
                isMobile={isMobile}
              />
            </div>
          ) : (
            <div className="mt-8 text-center">
              <p className="text-gray-400 mb-4">注册登录后查看更多内容</p>
            </div>
          )}
        </>
      )}

      <ScrollButtons />
    </div>
  );
});

FeedbackPageClient.displayName = 'FeedbackPageClient';

export default FeedbackPageClient;