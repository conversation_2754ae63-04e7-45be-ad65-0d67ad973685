"use client";

import { useSession } from "next-auth/react";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { Card, CardContent, CardTitle } from "./ui/card";
import { User, Mail, Calendar, MapPin, Heart, X, DollarSign, Zap, Briefcase, Crown, ArrowLeft } from "lucide-react";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Button } from "./ui/button";
import ProfileSkeleton from "./ProfileSkeleton";

interface UserProfile {
	username: string;
	city: string | null;
	preferredTypes: string[];
	dislikedTraits: string[];
	priceRanges: string[];
	fetishes: string[];
	preferredJobs: string[];
	createdAt: string;
}

export default function ProfileContent() {
	const { data: session } = useSession();
	const [profile, setProfile] = useState<UserProfile | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const router = useRouter();

	useEffect(() => {
		const fetchProfile = async () => {
			if (session?.user?.username) {
				try {
					setIsLoading(true);
					const response = await fetch("/api/profile");
					if (!response.ok) {
						throw new Error(`HTTP error! status: ${response.status}`);
					}
					const data = await response.json();
					setProfile(data);
				} catch (error) {
					console.error("Error fetching profile:", error);
					setError("加载个人资料失败，请稍后再试");
				} finally {
					setIsLoading(false);
				}
			}
		};

		fetchProfile();
	}, [session]);

	if (isLoading) {
		return <ProfileSkeleton />;
	}

	if (error) {
		return <div className="text-center py-8 text-red-500">{error}</div>;
	}

	if (!profile) {
		return <div className="text-center py-8 text-[#e50049]">没有可用的个人资料数据</div>;
	}

	return (
		<div className="relative min-h-screen bg-background pt-16 px-4 overflow-hidden">
			{/* 添加返回按钮 */}
			<div className="relative container mx-auto max-w-4xl mb-4">
				<Link href="/city" className="inline-flex items-center text-[#e50049] hover:text-[#ff1464] transition-colors">
					<ArrowLeft className="mr-2 h-5 w-5" />
					<span>返回主页</span>
				</Link>
			</div>

			{/* Background Gradient - 静态版本 */}
			<div className="absolute inset-0 w-full h-full bg-gradient-to-b from-background to-background">
				<div className="absolute inset-0 w-full h-full bg-[radial-gradient(ellipse_at_top_right,rgba(229,0,73,0.15),rgba(229,0,73,0)_50%)]"></div>
				<div className="absolute inset-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(229,0,73,0.1),rgba(229,0,73,0)_50%)]"></div>
			</div>

			{/* Soft Pattern Background - 静态版本 */}
			<div className="absolute inset-0 w-full h-full opacity-30">
				<div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(229,0,73,0.05)_1px,transparent_1px)] bg-[size:32px_32px]"></div>
				<div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(229,0,73,0.05)_1px,transparent_1px)] bg-[size:24px_24px] rotate-15"></div>
			</div>

			<div className="relative container mx-auto max-w-4xl">
				{/* 静态版本的浮动元素 */}
				<div className="absolute -top-20 -left-20 w-60 h-60 bg-[#e50049]/5 rounded-full filter blur-3xl"></div>
				<div className="absolute -top-20 -right-20 w-60 h-60 bg-[#e50049]/5 rounded-full filter blur-3xl"></div>

				<div className="text-center mb-16">
					<h1 className="text-[40px] md:text-[60px] font-bold tracking-tight mb-4">
						<span className="relative inline-block">
							<span className="absolute -inset-2 bg-gradient-to-r from-[#e50049]/20 to-[#ff1464]/20 blur-xl"></span>
							<span className="relative z-10 inline-block text-white">{profile.username}</span>
						</span>
					</h1>
					{/* VIP Status */}
					<div className="flex items-center justify-center gap-2 mb-4">
						{session?.user?.isVIP ? (
							<div className="relative group">
								<div className="absolute inset-0 bg-gradient-to-r from-[#ffa31a] to-[#ff1464] rounded-full blur-md opacity-50 group-hover:opacity-75 transition-opacity"></div>
								<div className="relative px-4 py-1 bg-gradient-to-r from-[#ffa31a] to-[#ff1464] rounded-full flex items-center gap-2">
									<Crown className="w-5 h-5 text-white" />
									<span className="text-white font-semibold">VIP会员</span>
								</div>
							</div>
						) : (
							<Link href="/vip">
								<div className="relative group cursor-pointer">
									<div className="absolute inset-0 bg-gradient-to-r from-gray-500 to-gray-700 rounded-full blur-md opacity-50 group-hover:opacity-75 transition-opacity"></div>
									<div className="relative px-4 py-1 bg-gradient-to-r from-gray-500 to-gray-700 rounded-full flex items-center gap-2">
										<Crown className="w-5 h-5 text-gray-300" />
										<span className="text-gray-300 font-semibold">普通会员</span>
									</div>
								</div>
							</Link>
						)}
					</div>
					<p className="text-xl text-muted-foreground">
						注册于 {new Date(profile.createdAt).toLocaleDateString("zh-CN")}
					</p>
				</div>

				<div className="grid gap-6">
					{/* 基本信息 */}
					<div className="relative overflow-hidden rounded-2xl border border-[#e50049]/20 bg-[#e50049]/5 p-6">
						<div className="absolute inset-0">
							<div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/5"></div>
							<div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,rgba(255,255,255,0.05),transparent_50%)]"></div>
						</div>
						
						<div className="relative">
							<h2 className="text-2xl font-bold mb-6 text-[#e50049]">基本信息</h2>
							<div className="grid gap-4">
								<ProfileItem
									icon={<User className="w-5 h-5" />}
									label="俱乐部匿名ID"
									value={profile.username}
									color="#e50049"
								/>
								<ProfileItem
									icon={<MapPin className="w-5 h-5" />}
									label="所在地"
									value={profile.city || "未设置"}
									color="#e50049"
								/>
							</div>
						</div>
					</div>

					{/* 偏好设置 */}
					<div className="relative overflow-hidden rounded-2xl border border-[#ffa31a]/20 bg-[#ffa31a]/5 p-6">
						<div className="absolute inset-0">
							<div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/5"></div>
							<div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,rgba(255,255,255,0.05),transparent_50%)]"></div>
						</div>
						
						<div className="relative">
							<h2 className="text-2xl font-bold mb-6 text-[#ffa31a]">偏好设置</h2>
							<div className="grid gap-4">
								<ProfileItem
									icon={<Heart className="w-5 h-5" />}
									label="喜欢的类型"
									value={profile.preferredTypes.join(", ") || "未设置"}
									color="#ffa31a"
								/>
								<ProfileItem
									icon={<X className="w-5 h-5" />}
									label="不喜欢的特征"
									value={profile.dislikedTraits.join(", ") || "未设置"}
									color="#ffa31a"
								/>
								<ProfileItem
									icon={<DollarSign className="w-5 h-5" />}
									label="接受的价位"
									value={profile.priceRanges.join(", ") || "未设置"}
									color="#ffa31a"
								/>
							</div>
						</div>
					</div>

					{/* 特殊偏好 */}
					<div className="relative overflow-hidden rounded-2xl border border-[#00a6ff]/20 bg-[#00a6ff]/5 p-6">
						<div className="absolute inset-0">
							<div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/5"></div>
							<div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,rgba(255,255,255,0.05),transparent_50%)]"></div>
						</div>
						
						<div className="relative">
							<h2 className="text-2xl font-bold mb-6 text-[#00a6ff]">特殊偏好</h2>
							<div className="grid gap-4">
								<ProfileItem
									icon={<Zap className="w-5 h-5" />}
									label="癖好/偏好"
									value={profile.fetishes.join(", ") || "未设置"}
									color="#00a6ff"
								/>
								<ProfileItem
									icon={<Briefcase className="w-5 h-5" />}
									label="倾向职业"
									value={profile.preferredJobs.join(", ") || "未设置"}
									color="#00a6ff"
								/>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}

function ProfileItem({ icon, label, value, color }: { icon: React.ReactNode; label: string; value: string; color: string }) {
	return (
		<div className="flex items-center space-x-4 p-3 rounded-xl hover:bg-white/5 transition-colors">
			<div className={`w-10 h-10 rounded-xl flex items-center justify-center`} style={{ backgroundColor: `${color}20` }}>
				<div className="text-[#e50049]">{icon}</div>
			</div>
			<div className="flex-1">
				<p className="text-sm text-muted-foreground">{label}</p>
				<p className="text-base font-medium text-white">{value}</p>
			</div>
		</div>
	);
}
