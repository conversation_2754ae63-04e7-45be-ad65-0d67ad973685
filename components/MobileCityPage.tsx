"use client";

import React from 'react';
import Link from 'next/link';
import { CityGrid } from './CityGrid';
import { Search, ChevronRight, Star, MapPin, Users, Sparkles, ExternalLink, BarChart3 } from 'lucide-react';
import { ResourceCards } from './ResourceCards';
import { HotRecommendations } from './HotRecommendations';

export function MobileCityPage() {
  return (
    <div className="w-full pb-16 pt-2">
      
      {/* 极品资源 */}
      <div className="px-4 mb-8 mt-2">
        <ResourceCards variant="mobile" />
      </div>
      
      {/* 城市目录 */}
      <div className="px-4 mb-8">
        <div className="flex justify-between items-center mb-3">
          <h2 className="text-lg font-medium flex items-center gap-2">
            <span className="text-lg mr-1">🌆</span> 城市目录
          </h2>
        </div>
        <CityGrid />
      </div>
      
      {/* 推荐城市 */}
      <div className="px-4 mb-8">
        <HotRecommendations variant="mobile" />
      </div>
    </div>
  );
} 